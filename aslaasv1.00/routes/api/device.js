const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const { imageUpload } = require('../../utils/FileUploader');
const admin = require('../../middleware/admin');
const adminOrInstaller = require('../../middleware/adminOrInstaller');
const {
    driverConfirm,
    getConnectedMqttClients,
    getDevices,
    getAll,
    editDevice,
    myDevice,
    setByAdmin,

    orderInfo,
    driverInfo,
    orderConfirm,

    register,
    deleteDevice,
    updateDevice,
    controlAnalog,
    controlDevice,
    changeActive,
    checkLine,
    setGpsTime,
    switchSubscribe,
    addBleDevice,
    removeBleDevice,
    getLocations,

    extendsBalance,
    setDriverProfile,
    listAll,
    sendSimCheckCommand,

    deleteMultipleDevices,
    updateDeviceRenter,
    getDeviceVersions,

} = require('../../controller/deviceController');
const { sendMqtt,publish } = require('../../utils/channel');


/******************** */


router.post('/driver-confirm', auth, driverConfirm)

router.get('/get-connections', auth, getConnectedMqttClients)
router.post('/my-device', auth, myDevice)
router.post('/gets', auth, getAll)
router.post('/list/all', auth, listAll)
router.post('/edit/:id', auth, editDevice)

router.post('/set-by-admin/:id', admin, setByAdmin);


router.post('/order-info', auth, orderInfo)
router.post('/driver-info', auth, driverInfo)
router.post('/order-confirm', auth, orderConfirm);



router.post('/register', auth, register);
router.post('/admin-create', auth, adminOrInstaller, register); // Admin/Installer endpoint for creating devices for users
router.post('/delete', auth, adminOrInstaller, deleteDevice); // Allow both admin and installer to delete devices
router.post('/set/:id', auth, updateDevice);
router.post('/delete-multiple', auth, admin, deleteMultipleDevices);

router.post("/control/analog", auth, controlAnalog);
router.post('/control/:cmd', auth, controlDevice);
router.post('/change-active', auth, changeActive)

router.post('/checkline', auth, checkLine)

router.post('/set-gps-time', auth, setGpsTime)

router.post('/switch-subscribe', auth, switchSubscribe)

router.post('/add-ble-device', auth, addBleDevice)
router.post('/remove-ble-device', auth, removeBleDevice)

// for admin part get all locations from devices 
router.post('/get-locations', auth, getLocations);

router.post("/extend-balance", auth, extendsBalance);

router.post('/set-driver-profile', auth, [
        imageUpload.single('driverLicenseFile')
    ],
    setDriverProfile
);
router.post("/check-sim", auth, sendSimCheckCommand);
router.post('/publish',auth, publish)

router.post('/update-renter', auth, updateDeviceRenter);
router.get('/versions', auth, getDeviceVersions);

module.exports = router;
