#!/usr/bin/env node

/**
 * Script to check for remaining console.log statements in the backend
 * Usage: node scripts/check-console-logs.js
 */

const fs = require('fs');
const path = require('path');

const excludeDirs = ['node_modules', 'build', 'uploads', '.git'];
const includeExtensions = ['.js'];

function findConsoleLogsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const consoleLogLines = [];
    
    lines.forEach((line, index) => {
      if (line.match(/console\.(log|error|warn|info|debug)/)) {
        consoleLogLines.push({
          line: index + 1,
          content: line.trim()
        });
      }
    });
    
    return consoleLogLines;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return [];
  }
}

function scanDirectory(dirPath) {
  const results = [];
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory() && !excludeDirs.includes(item)) {
        results.push(...scanDirectory(itemPath));
      } else if (stat.isFile() && includeExtensions.includes(path.extname(item))) {
        const consoleLogs = findConsoleLogsInFile(itemPath);
        if (consoleLogs.length > 0) {
          results.push({
            file: itemPath,
            logs: consoleLogs
          });
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }
  
  return results;
}

function main() {
  console.log('🔍 Scanning for console.log statements in backend files...\n');
  
  const results = scanDirectory('.');
  
  if (results.length === 0) {
    console.log('✅ No console.log statements found in backend files!');
    return;
  }
  
  console.log(`⚠️  Found console.log statements in ${results.length} files:\n`);
  
  results.forEach(result => {
    console.log(`📄 ${result.file}`);
    result.logs.forEach(log => {
      console.log(`   Line ${log.line}: ${log.content}`);
    });
    console.log('');
  });
  
  console.log(`\n📊 Summary: ${results.reduce((total, result) => total + result.logs.length, 0)} console.log statements found in ${results.length} files`);
}

if (require.main === module) {
  main();
}

module.exports = { scanDirectory, findConsoleLogsInFile };
