const GpsLogModel = require("../models/gpslog");
const LogModel = require("../models/log");
const UserModel = require("../models/user");

const SimStatusLog = require("../models/simSmsLog");
const DeviceModel = require("../models/device");
const carGpsLog = require("../models/carGps"); // adjust the path as needed

const removeSimLog = async (req, res) => {
  try {
    const _id = req.params.id;
    await SimStatusLog.findByIdAndRemove(_id);
    return res.json({ success: true });
  } catch (err) {
    console.log(err);
    return res.json({ success: false });
  }
};
const readSimLog = async (req, res) => {
  try {
    const _id = req.body.id;
    await SimStatusLog.findByIdAndUpdate(_id, { read: true });
    return res.json({ success: true });
  } catch (err) {
    console.log(err);
    return res.json({ success: false });
  }
};
const getSimStatusLog = async (req, res) => {
  try {
    const deviceNumber = req.query.deviceNumber;

    if (!deviceNumber) {
      return res.status(400).json({
        success: false,
        message: "deviceNumber is required as a query parameter.",
      });
    }

    const logs = await SimStatusLog.find({ deviceNumber }).sort({ received: -1 });

    if (logs.length === 0) {
      return res.status(404).json({
        success: false,
        message: `No logs found for phone number: ${deviceNumber}`,
      });
    }

    // Balance patterns:
    const balanceRegexes = [
      /(\d+(\.\d+)?)\s*TG/i,
      /(\d+(\.\d+)?)\s*tug/i
    ];

    // Date patterns:
    // (1) YYYY/MM/DD
    // (2) YY/MM/DD
    // (3) DD-MM-YYYY
    const dateRegexes = [
      /(\d{4}\/\d{2}\/\d{2})/, // YYYY/MM/DD
      /(\d{2}\/\d{2}\/\d{2})/, // YY/MM/DD
      /(\d{2}-\d{2}-\d{4})/    // DD-MM-YYYY
    ];

    // Filter logs to only those that have at least one valid balance and one valid date format
    const validLogs = logs.filter(log => {
      const content = log.content;
      const hasBalance = balanceRegexes.some(regex => regex.test(content));
      const hasDate = dateRegexes.some(regex => regex.test(content));
      return hasBalance && hasDate;
    });

    if (validLogs.length === 0) {
      return res.status(404).json({
        success: false,
        message: `No valid logs with balance and expired date found for phone number: ${deviceNumber}`,
      });
    }

    // The most recent valid log
    const latestLog = validLogs[0];
    const content = latestLog.content;

    // Extract the balance
    let balance = 'Not available';
    for (const br of balanceRegexes) {
      const match = content.match(br);
      if (match) {
        // Convert to integer if desired
        balance = parseInt(match[1], 10).toString();
        break;
      }
    }

    // Extract the expired date
    let rawDate = 'Not available';
    for (const dr of dateRegexes) {
      const match = content.match(dr);
      if (match && match[1]) {
        rawDate = match[1];
        break;
      }
    }

    // Normalize the date to YYYY/MM/DD format
    let expiredDate = 'Not available';
    if (rawDate !== 'Not available') {
      if (rawDate.includes('/')) {
        // Could be YYYY/MM/DD or YY/MM/DD
        const parts = rawDate.split('/');
        if (parts[0].length === 4) {
          // YYYY/MM/DD format already
          expiredDate = rawDate;
        } else {
          // YY/MM/DD format, assume 20YY
          const [yy, mm, dd] = parts;
          const yyyy = `20${yy}`;
          expiredDate = `${yyyy}/${mm}/${dd}`;
        }
      } else if (rawDate.includes('-')) {
        // DD-MM-YYYY format, convert to YYYY/MM/DD
        const [dd, mm, yyyy] = rawDate.split('-');
        expiredDate = `${yyyy}/${mm}/${dd}`;
      }
    }

    const data = {
      deviceNumber: latestLog.deviceNumber,
      balance,
      expiredDate
    };

    return res.json({ success: true, data });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ success: false, err: err.message });
  }
};

const getSimLogs = async (req, res) => {
  try {
    const { deviceNumber } = req.query; // Get the deviceNumber from query parameters

    let matchStage = {};
    if (deviceNumber) {
      matchStage = { deviceNumber }; // If deviceNumber is provided, filter by it
    }

    // Fetch the last 10 logs for the specified device, sorted by the 'received' field in descending order
    const logs = await SimStatusLog.aggregate([
      { $match: matchStage }, // Apply the filter if deviceNumber is provided
      { $sort: { received: -1 } },
      {
        $group: {
          _id: "$deviceNumber",
          logs: { $push: "$$ROOT" },
        },
      },
      {
        $project: {
          deviceNumber: "$_id",
          logs: { $slice: ["$logs", 10] }, // Get only the last 10 logs
        },
      },
    ]);

    if (logs.length === 0) {
      return res.json({ success: false, message: 'No logs found for this device' });
    }

    // Prepare the data to be sent in response
    const data = logs[0].logs.map(log => ({
      deviceNumber: logs[0].deviceNumber,
      content: log.content,
      received: log.received,
    }));

    // Send the logs as response
    return res.json({ success: true, data });
  } catch (err) {
    console.error(err);
    // Handle any errors that occur during the process
    return res.status(500).json({ success: false, err: err.message });
  }
};

const getGpsLogByDate = async (req, res) => {
  
  try {
    const { from, to, deviceNumber } = req.body;
    // console.log(deviceNumber, from, to)
    const logs = await GpsLogModel.find({
      imei: deviceNumber,
      createdAt: { $gte: from, $lte: new Date(`${to} 23:59:59`) },
    }).sort({ createdAt: 1 });

    const fakes = [
      {
        imei: deviceNumber,
        battery: 4000,
        bles: [
          {
            door: "00",
            shock: "01",
            humidity: 12,
            temperature: -12,
            brightness: "000",
          },
        ],
        lat: 33.52001088075479,
        lng: 36.26829385757446,
      },
      {
        imei: deviceNumber,
        battery: 3990,
        bles: [
          {
            door: "01",
            shock: "01",
            humidity: 12,
            temperature: -12,
            brightness: "000",
          },
        ],
        lat: 33.50546582848033,
        lng: 36.29547681726967,
      },
      {
        imei: deviceNumber,
        battery: 3980,
        bles: [
          {
            door: "02",
            shock: "01",
            humidity: 12,
            temperature: -12,
            brightness: "000",
          },
        ],
        lat: 33.48546582848033,
        lng: 36.31829385757446,
      },
      {
        imei: deviceNumber,
        battery: 3980,
        bles: [
          {
            door: "03",
            shock: "01",
            humidity: 12,
            temperature: -12,
            brightness: "000",
          },
        ],
        lat: 33.46001088075479,
        lng: 36.32829385757446,
      },
    ];

    return res.status(200).json({ success: true, logs: fakes });
  } catch (err) {
    res.status(500).json({ success: false, logs: [] });
  }
};

const getLogByDate = async (req, res) => {
  try {
    // Fetch logs with simplified MongoDB aggregation pipeline
    let logs = await carGpsLog.aggregate([
      { $sort: { createdAt: -1 } }, // Sort by createdAt field in descending order
      {
        $group: {
          _id: "$deviceNumber",
          payload: { $first: "$payload" }, // Get the first (latest) payload for each deviceNumber
          createdAt: { $first: "$createdAt" }, // Also get the timestamp of the first (latest) log
          speed: { $first: "$speed" }, // Also get the speed of the first (latest) log
        },
      },
      {
        $project: {
          _id: 0,
          deviceNumber: "$_id",
          payload: 1,
          createdAt: 1, // Include the createdAt timestamp in the results
          speed: 1, // Include the speed in the results
        },
      },
    ]);

    // Extract Lat, Lon, Speed, and timestamp in the application code
    logs = logs.map(log => {
      try {
        // Parse the payload as JSON; assuming it's double-encoded based on your structure
        const parsedPayload = JSON.parse(JSON.parse(log.payload));
        // Extract Lat and Lon from the parsed object
        const { Lat, Lon } = parsedPayload;
        return {
          deviceNumber: log.deviceNumber,
          Lat,
          Lon,
          Speed: log.speed, // Include the speed
          createdAt: log.createdAt, // Include the timestamp
        };
      } catch (error) {
        console.error("Error parsing payload for deviceNumber:", log.deviceNumber, error);
        // Return null or some default value if parsing fails, including timestamp and speed
        return {
          deviceNumber: log.deviceNumber,
          Lat: null,
          Lon: null,
          Speed: log.speed,
          createdAt: log.createdAt,
        };
      }
    });

    // Return the processed logs for GPS history tracking
    return res.status(200).json({ success: true, logs });
  } catch (error) {
    console.error("Error fetching logs:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
};

// Fetch GPS data by deviceNumber
const fetchLastDataByDeviceNumber = async (req, res) => {
  try {
    const { deviceNumber } = req.query;

    if (!deviceNumber) {
      return res.status(400).json({
        success: false,
        message: "deviceNumber is required as a query parameter.",
      });
    }

    // Fetch the latest GPS log for the device
    const latestLog = await carGpsLog.findOne({ deviceNumber }).sort({ createdAt: -1 });

    if (!latestLog) {
      return res.status(404).json({
        success: false,
        message: `No GPS log found for device number: ${deviceNumber}`,
      });
    }

    // Parse the payload from string to JSON
    let parsedPayload;
    try {
      parsedPayload = JSON.parse(latestLog.payload);
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Error parsing payload data",
        error: error.message,
      });
    }

    // Return only the latest payload as JSON
    return res.status(200).json({
      success: true,
      data: parsedPayload,
    });

  } catch (err) {
    console.error(err);
    return res.status(500).json({
      success: false,
      error: err.message,
    });
  }
};


const getClients = (logs, users) => {
  const clients = [];
  const indexes = [];
  logs.forEach((log) => {
    if (!indexes.includes(log.deviceNumber)) {
      indexes.push(log.deviceNumber);
      const _users = users.filter((u) => `${u._id}` == log.user);
      const row = {
        _id: log._id,
        user: _users.length > 0 ? _users[0].username : "",
        device: log.deviceNumber,
        sent: 0,
        received: 0,
        failed: 0,
      };

      logs.forEach((l) => {
        row.sent +=
          l.deviceNumber == log.deviceNumber && l.sent == "yes" ? 1 : 0;
        row.received +=
          l.deviceNumber == log.deviceNumber && l.sent == "receive" ? 1 : 0;
        row.failed +=
          l.deviceNumber == log.deviceNumber && l.sent.toLowerCase() == "no"
            ? 1
            : 0;
      });

      clients.push(row);
    }
  });
  return clients;
};


const getLogList = async (req, res) => {
  const logs = await LogModel.find({ user: req.user.id }).sort({
    createdAt: -1,
  });

  return res.status(200).json({ success: true, logs });
};

const getLogListByAll = async (req, res) => {
  try {
    // Fetch logs with simplified MongoDB aggregation pipeline
    let logs = await carGpsLog.aggregate([
      { $sort: { createdAt: -1 } }, // Sort by createdAt field in descending order
      {
        $group: {
          _id: "$deviceNumber",
          payload: { $first: "$payload" }, // Get the first (latest) payload for each deviceNumber
        },
      },
      {
        $project: {
          _id: 0,
          deviceNumber: "$_id",
          payload: 1, // Include the payload in the results
        },
      },
    ]);

    // Extract Lat and Lon in the application code
    logs = logs.map(log => {
      try {
        // Parse the payload as JSON; assuming it's double-encoded based on your structure
        const parsedPayload = JSON.parse(JSON.parse(log.payload));
        // Extract Lat and Lon from the parsed object
        const { Lat, Lon } = parsedPayload;
        return {
          deviceNumber: log.deviceNumber,
          Lat,
          Lon,
        };
      } catch (error) {
        console.error("Error parsing payload for deviceNumber:", log.deviceNumber, error);
        // Return null or some default value if parsing fails
        return {
          deviceNumber: log.deviceNumber,
          Lat: null,
          Lon: null,
        };
      }
    });

    // Return the processed logs
    return res.status(200).json({ success: true, logs });
  } catch (error) {
    console.error("Error fetching logs:", error);
    return res.status(500).json({ success: false, error: error.message });
  }
};

const getListByAllShare = async (req, res) => {
  // Car rent logs have been removed, returning empty logs
  const logs = [];
  return res.status(200).json({ success: true, logs });
};

const deleteLog = async (req, res) => {
  try {
    await LogModel.deleteMany({
      _id: { $in: req.body.ids },
    });
    res.status(200).json({ success: true });
  } catch (err) {
    res.status(500).json({ success: false });
  }
};

module.exports = {
  getLogByDate,
  getGpsLogByDate,
  getLogList,
  getLogListByAll,
  getListByAllShare,
  deleteLog,
  getSimStatusLog,
  readSimLog,
  removeSimLog,
  getSimLogs,
  fetchLastDataByDeviceNumber
};
