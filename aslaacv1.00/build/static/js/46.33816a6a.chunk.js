/*! For license information please see 46.33816a6a.chunk.js.LICENSE.txt */
(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[46],{1197:function(e,t,r){"use strict";r.r(t),r.d(t,"set_cptable",(function(){return g})),r.d(t,"set_fs",(function(){return Re})),r.d(t,"version",(function(){return Gh})),r.d(t,"parse_xlscfb",(function(){return tf})),r.d(t,"parse_zip",(function(){return hh})),r.d(t,"read",(function(){return bh})),r.d(t,"readFile",(function(){return wh})),r.d(t,"readFileSync",(function(){return wh})),r.d(t,"write",(function(){return _h})),r.d(t,"writeFile",(function(){return xh})),r.d(t,"writeFileSync",(function(){return xh})),r.d(t,"writeFileAsync",(function(){return Oh})),r.d(t,"writeXLSX",(function(){return kh})),r.d(t,"writeFileXLSX",(function(){return Ch})),r.d(t,"utils",(function(){return zh})),r.d(t,"stream",(function(){return Vh})),r.d(t,"SSF",(function(){return ke})),r.d(t,"CFB",(function(){return Ce}));var a={version:"0.18.5"},n=1200,s=1252,i=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],o={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},c=function(e){-1!=i.indexOf(e)&&(s=o[0]=e)};var l=function(e){n=e,c(e)};function f(){l(1200),c(1252)}function h(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function u(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var d,p=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return 255==t&&254==r?function(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}(e.slice(2)):254==t&&255==r?u(e.slice(2)):65279==t?e.slice(1):e},m=function(e){return String.fromCharCode(e)},v=function(e){return String.fromCharCode(e)};function g(e){d=e,l=function(e){n=e,c(e)},p=function(e){return 255===e.charCodeAt(0)&&254===e.charCodeAt(1)?d.utils.decode(1200,h(e.slice(2))):e},m=function(e){return 1200===n?String.fromCharCode(e):d.utils.decode(n,[255&e,e>>8])[0]},v=function(e){return d.utils.decode(s,[e])[0]},Br()}var b="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function w(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0,c=0,l=0;l<e.length;)s=(r=e.charCodeAt(l++))>>2,i=(3&r)<<4|(a=e.charCodeAt(l++))>>4,o=(15&a)<<2|(n=e.charCodeAt(l++))>>6,c=63&n,isNaN(a)?o=c=64:isNaN(n)&&(c=64),t+=b.charAt(s)+b.charAt(i)+b.charAt(o)+b.charAt(c);return t}function T(e){var t="",r=0,a=0,n=0,s=0,i=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var c=0;c<e.length;)r=b.indexOf(e.charAt(c++))<<2|(s=b.indexOf(e.charAt(c++)))>>4,t+=String.fromCharCode(r),a=(15&s)<<4|(i=b.indexOf(e.charAt(c++)))>>2,64!==i&&(t+=String.fromCharCode(a)),n=(3&i)<<6|(o=b.indexOf(e.charAt(c++))),64!==o&&(t+=String.fromCharCode(n));return t}var E=function(){return"undefined"!==typeof Buffer&&"undefined"!==typeof process&&"undefined"!==typeof process.versions&&!!process.versions.node}(),y=function(){if("undefined"!==typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function S(e){return E?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}function k(e){return E?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}var _=function(e){return E?y(e,"binary"):e.split("").map((function(e){return 255&e.charCodeAt(0)}))};function A(e){if("undefined"===typeof ArrayBuffer)return _(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=255&e.charCodeAt(a);return t}function x(e){if(Array.isArray(e))return e.map((function(e){return String.fromCharCode(e)})).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function C(e){if("undefined"==typeof ArrayBuffer)throw new Error("Unsupported");if(e instanceof ArrayBuffer)return C(new Uint8Array(e));for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var O=E?function(e){return Buffer.concat(e.map((function(e){return Buffer.isBuffer(e)?e:y(e)})))}:function(e){if("undefined"!==typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)if(n=e[t].length,e[t]instanceof Uint8Array)a.set(e[t],r);else{if("string"==typeof e[t])throw"wtf";a.set(new Uint8Array(e[t]),r)}return a}return[].concat.apply([],e.map((function(e){return Array.isArray(e)?e:[].slice.call(e)})))};var R=/\u0000/g,I=/[\u0001-\u0006]/g;function N(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function F(e,t){var r=""+e;return r.length>=t?r:Je("0",t-r.length)+r}function D(e,t){var r=""+e;return r.length>=t?r:Je(" ",t-r.length)+r}function P(e,t){var r=""+e;return r.length>=t?r:r+Je(" ",t-r.length)}var L=Math.pow(2,32);function M(e,t){return e>L||e<-L?function(e,t){var r=""+Math.round(e);return r.length>=t?r:Je("0",t-r.length)+r}(e,t):function(e,t){var r=""+e;return r.length>=t?r:Je("0",t-r.length)+r}(Math.round(e),t)}function U(e,t){return t=t||0,e.length>=7+t&&103===(32|e.charCodeAt(t))&&101===(32|e.charCodeAt(t+1))&&110===(32|e.charCodeAt(t+2))&&101===(32|e.charCodeAt(t+3))&&114===(32|e.charCodeAt(t+4))&&97===(32|e.charCodeAt(t+5))&&108===(32|e.charCodeAt(t+6))}var B=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],W=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];var H={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"\u4e0a\u5348/\u4e0b\u5348 "hh"\u6642"mm"\u5206"ss"\u79d2 "'},z={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},V={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function G(e,t,r){for(var a=e<0?-1:1,n=e*a,s=0,i=1,o=0,c=1,l=0,f=0,h=Math.floor(n);l<t&&(o=(h=Math.floor(n))*i+s,f=h*l+c,!(n-h<5e-8));)n=1/(n-h),s=i,i=o,c=l,l=f;if(f>t&&(l>t?(f=c,o=s):(f=l,o=i)),!r)return[0,a*o,f];var u=Math.floor(a*o/f);return[u,a*o-u*f,f]}function j(e,t,r){if(e>2958465||e<0)return null;var a=0|e,n=Math.floor(86400*(e-a)),s=0,i=[],o={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(o.u)<1e-6&&(o.u=0),t&&t.date1904&&(a+=1462),o.u>.9999&&(o.u=0,86400==++n&&(o.T=n=0,++a,++o.D)),60===a)i=r?[1317,10,29]:[1900,2,29],s=3;else if(0===a)i=r?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var c=new Date(1900,0,1);c.setDate(c.getDate()+a-1),i=[c.getFullYear(),c.getMonth()+1,c.getDate()],s=c.getDay(),a<60&&(s=(s+6)%7),r&&(s=function(e,t){t[0]-=581;var r=e.getDay();e<60&&(r=(r+6)%7);return r}(c,i))}return o.y=i[0],o.m=i[1],o.d=i[2],o.S=n%60,n=Math.floor(n/60),o.M=n%60,n=Math.floor(n/60),o.H=n,o.q=s,o}var X=new Date(1899,11,31,0,0,0),Y=X.getTime(),K=new Date(1900,2,1,0,0,0);function J(e,t){var r=e.getTime();return t?r-=1262304e5:e>=K&&(r+=864e5),(r-(Y+6e4*(e.getTimezoneOffset()-X.getTimezoneOffset())))/864e5}function q(e){return-1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Z(e){var t,r=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return t=r>=-4&&r<=-1?e.toPrecision(10+r):Math.abs(r)<=9?function(e){var t=e<0?12:11,r=q(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)}(e):10===r?e.toFixed(10).substr(0,12):function(e){var t=q(e.toFixed(11));return t.length>(e<0?12:11)||"0"===t||"-0"===t?e.toPrecision(6):t}(e),q(function(e){return-1==e.indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}(t.toUpperCase()))}function Q(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):Z(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return Te(14,J(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function ee(e,t,r,a){var n,s="",i=0,o=0,c=r.y,l=0;switch(e){case 98:c=r.y+543;case 121:switch(t.length){case 1:case 2:n=c%100,l=2;break;default:n=c%1e4,l=4}break;case 109:switch(t.length){case 1:case 2:n=r.m,l=t.length;break;case 3:return W[r.m-1][1];case 5:return W[r.m-1][0];default:return W[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:n=r.d,l=t.length;break;case 3:return B[r.q][0];default:return B[r.q][1]}break;case 104:switch(t.length){case 1:case 2:n=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:n=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:n=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;return 0!==r.u||"s"!=t&&"ss"!=t?(o=a>=2?3===a?1e3:100:1===a?10:1,(i=Math.round(o*(r.S+r.u)))>=60*o&&(i=0),"s"===t?0===i?"0":""+i/o:(s=F(i,2+a),"ss"===t?s.substr(0,2):"."+s.substr(2,t.length-1))):F(r.S,t.length);case 90:switch(t){case"[h]":case"[hh]":n=24*r.D+r.H;break;case"[m]":case"[mm]":n=60*(24*r.D+r.H)+r.M;break;case"[s]":case"[ss]":n=60*(60*(24*r.D+r.H)+r.M)+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=3===t.length?1:2;break;case 101:n=c,l=1}return l>0?F(n,l):""}function te(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var re=/%/g;function ae(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+ae(e,-t);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),-1===(r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n)).indexOf("e")){var i=Math.floor(Math.log(t)*Math.LOG10E);for(-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s);"0."===r.substr(0,2);)r=(r=r.charAt(0)+r.substr(2,n)+"."+r.substr(2+n)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,a){return t+r+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var ne=/# (\?+)( ?)\/( ?)(\d+)/;var se=/^#*0*\.([0#]+)/,ie=/\).*[0#]/,oe=/\(###\) ###\\?-####/;function ce(e){for(var t,r="",a=0;a!=e.length;++a)switch(t=e.charCodeAt(a)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function le(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function fe(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function he(e,t,r){if(40===e.charCodeAt(0)&&!t.match(ie)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?he("n",a,r):"("+he("n",a,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var a=t.length-1;44===t.charCodeAt(a-1);)--a;return pe(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var a=t.replace(re,""),n=t.length-a.length;return pe(e,a,r*Math.pow(10,2*n))+Je("%",n)}(e,t,r);if(-1!==t.indexOf("E"))return ae(t,r);if(36===t.charCodeAt(0))return"$"+he(e,t.substr(" "==t.charAt(1)?2:1),r);var n,s,i,o,c=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+M(c,t.length);if(t.match(/^[#?]+$/))return"0"===(n=M(r,0))&&(n=""),n.length>t.length?n:ce(t.substr(0,t.length-n.length))+n;if(s=t.match(ne))return function(e,t,r){var a=parseInt(e[4],10),n=Math.round(t*a),s=Math.floor(n/a),i=n-s*a,o=a;return r+(0===s?"":""+s)+" "+(0===i?Je(" ",e[1].length+1+e[4].length):D(i,e[1].length)+e[2]+"/"+e[3]+F(o,e[4].length))}(s,c,l);if(t.match(/^#+0+$/))return l+M(c,t.length-t.indexOf("0"));if(s=t.match(se))return n=le(r,s[1].length).replace(/^([^\.]+)$/,"$1."+ce(s[1])).replace(/\.$/,"."+ce(s[1])).replace(/\.(\d*)$/,(function(e,t){return"."+t+Je("0",ce(s[1]).length-t.length)})),-1!==t.indexOf("0.")?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+le(c,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+te(M(c,0));if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+he(e,t,-r):te(""+(Math.floor(r)+function(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}(r,s[1].length)))+"."+F(fe(r,s[1].length),s[1].length);if(s=t.match(/^#,#*,#0/))return he(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=N(he(e,t.replace(/[\\-]/g,""),r)),i=0,N(N(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(t.match(oe))return"("+(n=he(e,"##########",r)).substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),o=G(c,Math.pow(10,i)-1,!1),n=""+l," "==(f=pe("n",s[1],o[1])).charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],(f=P(o[2],i)).length<s[4].length&&(f=ce(s[4].substr(s[4].length-f.length))+f),n+=f;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),l+((o=G(c,Math.pow(10,i)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?D(o[1],i)+s[2]+"/"+s[3]+P(o[2],i):Je(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=M(r,0),t.length<=n.length?n:ce(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0?]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=t.indexOf(".")-i,u=t.length-n.length-h;return ce(t.substr(0,h)+n+t.substr(t.length-u))}if(s=t.match(/^00,000\.([#0]*0)$/))return i=fe(r,s[1].length),r<0?"-"+he(e,t,-r):te(function(e){return e<2147483647&&e>-2147483648?""+(e>=0?0|e:e-1|0):""+Math.floor(e)}(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?F(0,3-e.length):"")+e}))+"."+F(i,s[1].length);switch(t){case"###,##0.00":return he(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=te(M(c,0));return"0"!==d?l+d:"";case"###,###.00":return he(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return he(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function ue(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+ue(e,-t);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),!(r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n)).match(/[Ee]/)){var i=Math.floor(Math.log(t)*Math.LOG10E);-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,a){return t+r+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function de(e,t,r){if(40===e.charCodeAt(0)&&!t.match(ie)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?de("n",a,r):"("+de("n",a,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var a=t.length-1;44===t.charCodeAt(a-1);)--a;return pe(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var a=t.replace(re,""),n=t.length-a.length;return pe(e,a,r*Math.pow(10,2*n))+Je("%",n)}(e,t,r);if(-1!==t.indexOf("E"))return ue(t,r);if(36===t.charCodeAt(0))return"$"+de(e,t.substr(" "==t.charAt(1)?2:1),r);var n,s,i,o,c=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+F(c,t.length);if(t.match(/^[#?]+$/))return n=""+r,0===r&&(n=""),n.length>t.length?n:ce(t.substr(0,t.length-n.length))+n;if(s=t.match(ne))return function(e,t,r){return r+(0===t?"":""+t)+Je(" ",e[1].length+2+e[4].length)}(s,c,l);if(t.match(/^#+0+$/))return l+F(c,t.length-t.indexOf("0"));if(s=t.match(se))return n=(n=(""+r).replace(/^([^\.]+)$/,"$1."+ce(s[1])).replace(/\.$/,"."+ce(s[1]))).replace(/\.(\d*)$/,(function(e,t){return"."+t+Je("0",ce(s[1]).length-t.length)})),-1!==t.indexOf("0.")?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+(""+c).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+te(""+c);if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+de(e,t,-r):te(""+r)+"."+Je("0",s[1].length);if(s=t.match(/^#,#*,#0/))return de(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=N(de(e,t.replace(/[\\-]/g,""),r)),i=0,N(N(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(t.match(oe))return"("+(n=de(e,"##########",r)).substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),o=G(c,Math.pow(10,i)-1,!1),n=""+l," "==(f=pe("n",s[1],o[1])).charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],(f=P(o[2],i)).length<s[4].length&&(f=ce(s[4].substr(s[4].length-f.length))+f),n+=f;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),l+((o=G(c,Math.pow(10,i)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?D(o[1],i)+s[2]+"/"+s[3]+P(o[2],i):Je(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=""+r,t.length<=n.length?n:ce(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=t.indexOf(".")-i,u=t.length-n.length-h;return ce(t.substr(0,h)+n+t.substr(t.length-u))}if(s=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+de(e,t,-r):te(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?F(0,3-e.length):"")+e}))+"."+F(0,s[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=te(""+c);return"0"!==d?l+d:"";default:if(t.match(/\.[0#?]*$/))return de(e,t.slice(0,t.lastIndexOf(".")),r)+ce(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function pe(e,t,r){return(0|r)===r?de(e,t,r):he(e,t,r)}var me=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function ve(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":U(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"\u4e0a":if("A/P"===e.substr(t,3).toUpperCase())return!0;if("AM/PM"===e.substr(t,5).toUpperCase())return!0;if("\u4e0a\u5348/\u4e0b\u5348"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(a=r;"]"!==e.charAt(t++)&&t<e.length;)a+=e.charAt(t);if(a.match(me))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t," "!=e.charAt(t)&&"*"!=e.charAt(t)||++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;default:++t}return!1}var ge=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function be(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function we(e,t){var r=function(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),!0===r)throw new Error("Format |"+e+"| unterminated string ");return t}(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if("number"!==typeof t)return[4,4===r.length||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var s=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[a,s];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var i=r[0].match(ge),o=r[1].match(ge);return be(t,i)?[a,r[0]]:be(t,o)?[a,r[1]]:[a,r[null!=i&&null!=o?2:1]]}return[a,s]}function Te(e,t,r){null==r&&(r={});var a="";switch(typeof e){case"string":a="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(a=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:H)[e])&&(a=r.table&&r.table[z[e]]||H[z[e]]),null==a&&(a=V[e]||"General")}if(U(a,0))return Q(t,r);t instanceof Date&&(t=J(t,r.date1904));var n=we(a,t);if(U(n[1]))return Q(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,a){for(var n,s,i,o=[],c="",l=0,f="",h="t",u="H";l<e.length;)switch(f=e.charAt(l)){case"G":if(!U(e,l))throw new Error("unrecognized character "+f+" in "+e);o[o.length]={t:"G",v:"General"},l+=7;break;case'"':for(c="";34!==(i=e.charCodeAt(++l))&&l<e.length;)c+=String.fromCharCode(i);o[o.length]={t:"t",v:c},++l;break;case"\\":var d=e.charAt(++l),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++l;break;case"_":o[o.length]={t:"t",v:" "},l+=2;break;case"@":o[o.length]={t:"T",v:t},++l;break;case"B":case"b":if("1"===e.charAt(l+1)||"2"===e.charAt(l+1)){if(null==n&&null==(n=j(t,r,"2"===e.charAt(l+1))))return"";o[o.length]={t:"X",v:e.substr(l,2)},h=f,l+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0)return"";if(null==n&&null==(n=j(t,r)))return"";for(c=f;++l<e.length&&e.charAt(l).toLowerCase()===f;)c+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),o[o.length]={t:f,v:c},h=f;break;case"A":case"a":case"\u4e0a":var m={t:f,v:f};if(null==n&&(n=j(t,r)),"A/P"===e.substr(l,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?"P":"A"),m.t="T",u="h",l+=3):"AM/PM"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",l+=5,u="h"):"\u4e0a\u5348/\u4e0b\u5348"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"\u4e0b\u5348":"\u4e0a\u5348"),m.t="T",l+=5,u="h"):(m.t="t",++l),null==n&&"T"===m.t)return"";o[o.length]=m,h=f;break;case"[":for(c=f;"]"!==e.charAt(l++)&&l<e.length;)c+=e.charAt(l);if("]"!==c.slice(-1))throw'unterminated "[" block: |'+c+"|";if(c.match(me)){if(null==n&&null==(n=j(t,r)))return"";o[o.length]={t:"Z",v:c.toLowerCase()},h=c.charAt(1)}else c.indexOf("$")>-1&&(c=(c.match(/\$([^-\[\]]*)/)||[])[1]||"$",ve(e)||(o[o.length]={t:"t",v:c}));break;case".":if(null!=n){for(c=f;++l<e.length&&"0"===(f=e.charAt(l));)c+=f;o[o.length]={t:"s",v:c};break}case"0":case"#":for(c=f;++l<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(l))>-1;)c+=f;o[o.length]={t:"n",v:c};break;case"?":for(c=f;e.charAt(++l)===f;)c+=f;o[o.length]={t:f,v:c},h=f;break;case"*":++l," "!=e.charAt(l)&&"*"!=e.charAt(l)||++l;break;case"(":case")":o[o.length]={t:1===a?"t":f,v:f},++l;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(c=f;l<e.length&&"0123456789".indexOf(e.charAt(++l))>-1;)c+=e.charAt(l);o[o.length]={t:"D",v:c};break;case" ":o[o.length]={t:f,v:f},++l;break;case"$":o[o.length]={t:"t",v:"$"},++l;break;default:if(-1===",$-+/():!^&'~{}<>=\u20acacfijklopqrtuvwxzP".indexOf(f))throw new Error("unrecognized character "+f+" in "+e);o[o.length]={t:"t",v:f},++l}var v,g=0,b=0;for(l=o.length-1,h="t";l>=0;--l)switch(o[l].t){case"h":case"H":o[l].t=u,h="h",g<1&&(g=1);break;case"s":(v=o[l].v.match(/\.0+$/))&&(b=Math.max(b,v[0].length-1)),g<3&&(g=3);case"d":case"y":case"M":case"e":h=o[l].t;break;case"m":"s"===h&&(o[l].t="M",g<2&&(g=2));break;case"X":break;case"Z":g<1&&o[l].v.match(/[Hh]/)&&(g=1),g<2&&o[l].v.match(/[Mm]/)&&(g=2),g<3&&o[l].v.match(/[Ss]/)&&(g=3)}switch(g){case 0:break;case 1:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H);break;case 2:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M)}var w,T="";for(l=0;l<o.length;++l)switch(o[l].t){case"t":case"T":case" ":case"D":break;case"X":o[l].v="",o[l].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[l].v=ee(o[l].t.charCodeAt(0),o[l].v,n,b),o[l].t="t";break;case"n":case"?":for(w=l+1;null!=o[w]&&("?"===(f=o[w].t)||"D"===f||(" "===f||"t"===f)&&null!=o[w+1]&&("?"===o[w+1].t||"t"===o[w+1].t&&"/"===o[w+1].v)||"("===o[l].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===o[w].v||" "===o[w].v&&null!=o[w+1]&&"?"==o[w+1].t));)o[l].v+=o[w].v,o[w]={v:"",t:";"},++w;T+=o[l].v,l=w-1;break;case"G":o[l].t="t",o[l].v=Q(t,r)}var E,y,S="";if(T.length>0){40==T.charCodeAt(0)?(E=t<0&&45===T.charCodeAt(0)?-t:t,y=pe("n",T,E)):(y=pe("n",T,E=t<0&&a>1?-t:t),E<0&&o[0]&&"t"==o[0].t&&(y=y.substr(1),o[0].v="-"+o[0].v)),w=y.length-1;var k=o.length;for(l=0;l<o.length;++l)if(null!=o[l]&&"t"!=o[l].t&&o[l].v.indexOf(".")>-1){k=l;break}var _=o.length;if(k===o.length&&-1===y.indexOf("E")){for(l=o.length-1;l>=0;--l)null!=o[l]&&-1!=="n?".indexOf(o[l].t)&&(w>=o[l].v.length-1?(w-=o[l].v.length,o[l].v=y.substr(w+1,o[l].v.length)):w<0?o[l].v="":(o[l].v=y.substr(0,w+1),w=-1),o[l].t="t",_=l);w>=0&&_<o.length&&(o[_].v=y.substr(0,w+1)+o[_].v)}else if(k!==o.length&&-1===y.indexOf("E")){for(w=y.indexOf(".")-1,l=k;l>=0;--l)if(null!=o[l]&&-1!=="n?".indexOf(o[l].t)){for(s=o[l].v.indexOf(".")>-1&&l===k?o[l].v.indexOf(".")-1:o[l].v.length-1,S=o[l].v.substr(s+1);s>=0;--s)w>=0&&("0"===o[l].v.charAt(s)||"#"===o[l].v.charAt(s))&&(S=y.charAt(w--)+S);o[l].v=S,o[l].t="t",_=l}for(w>=0&&_<o.length&&(o[_].v=y.substr(0,w+1)+o[_].v),w=y.indexOf(".")+1,l=k;l<o.length;++l)if(null!=o[l]&&(-1!=="n?(".indexOf(o[l].t)||l===k)){for(s=o[l].v.indexOf(".")>-1&&l===k?o[l].v.indexOf(".")+1:0,S=o[l].v.substr(0,s);s<o[l].v.length;++s)w<y.length&&(S+=y.charAt(w++));o[l].v=S,o[l].t="t",_=l}}}for(l=0;l<o.length;++l)null!=o[l]&&"n?".indexOf(o[l].t)>-1&&(E=a>1&&t<0&&l>0&&"-"===o[l-1].v?-t:t,o[l].v=pe(o[l].t,o[l].v,E),o[l].t="t");var A="";for(l=0;l!==o.length;++l)null!=o[l]&&(A+=o[l].v);return A}(n[1],t,r,n[0])}function Ee(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r)if(void 0!=H[r]){if(H[r]==e){t=r;break}}else t<0&&(t=r);t<0&&(t=391)}return H[t]=e,t}function ye(e){for(var t=0;392!=t;++t)void 0!==e[t]&&Ee(e[t],t)}function Se(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"\u4e0a\u5348/\u4e0b\u5348 "hh"\u6642"mm"\u5206"ss"\u79d2 "',H=e}var ke={format:Te,load:Ee,_table:H,load_table:ye,parse_date_code:j,is_date:ve,get_table:function(){return ke._table=H}},_e={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},Ae=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;var xe=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=new Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1,t[r]=e;return"undefined"!==typeof Int32Array?new Int32Array(t):t}();var r=function(e){var t=0,r=0,a=0,n="undefined"!==typeof Int32Array?new Int32Array(4096):new Array(4096);for(a=0;256!=a;++a)n[a]=e[a];for(a=0;256!=a;++a)for(r=e[a],t=256+a;t<4096;t+=256)r=n[t]=r>>>8^e[255&r];var s=[];for(a=1;16!=a;++a)s[a-1]="undefined"!==typeof Int32Array?n.subarray(256*a,256*a+256):n.slice(256*a,256*a+256);return s}(t),a=r[0],n=r[1],s=r[2],i=r[3],o=r[4],c=r[5],l=r[6],f=r[7],h=r[8],u=r[9],d=r[10],p=r[11],m=r[12],v=r[13],g=r[14];return e.table=t,e.bstr=function(e,r){for(var a=-1^r,n=0,s=e.length;n<s;)a=a>>>8^t[255&(a^e.charCodeAt(n++))];return~a},e.buf=function(e,r){for(var b=-1^r,w=e.length-15,T=0;T<w;)b=g[e[T++]^255&b]^v[e[T++]^b>>8&255]^m[e[T++]^b>>16&255]^p[e[T++]^b>>>24]^d[e[T++]]^u[e[T++]]^h[e[T++]]^f[e[T++]]^l[e[T++]]^c[e[T++]]^o[e[T++]]^i[e[T++]]^s[e[T++]]^n[e[T++]]^a[e[T++]]^t[e[T++]];for(w+=15;T<w;)b=b>>>8^t[255&(b^e[T++])];return~b},e.str=function(e,r){for(var a=-1^r,n=0,s=e.length,i=0,o=0;n<s;)(i=e.charCodeAt(n++))<128?a=a>>>8^t[255&(a^i)]:i<2048?a=(a=a>>>8^t[255&(a^(192|i>>6&31))])>>>8^t[255&(a^(128|63&i))]:i>=55296&&i<57344?(i=64+(1023&i),o=1023&e.charCodeAt(n++),a=(a=(a=(a=a>>>8^t[255&(a^(240|i>>8&7))])>>>8^t[255&(a^(128|i>>2&63))])>>>8^t[255&(a^(128|o>>6&15|(3&i)<<4))])>>>8^t[255&(a^(128|63&o))]):a=(a=(a=a>>>8^t[255&(a^(224|i>>12&15))])>>>8^t[255&(a^(128|i>>6&63))])>>>8^t[255&(a^(128|63&i))];return~a},e}(),Ce=function(){var e,t={};function r(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:r(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(0,t+1)}function a(e){if("/"==e.charAt(e.length-1))return a(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(t+1)}function n(e,t){"string"===typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var a=t.getFullYear()-1980;a=(a=a<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,a)}function s(e){Jr(e,0);for(var t={},r=0;e.l<=e.length-4;){var a=e.read_shift(2),n=e.read_shift(2),s=e.l+n,i={};if(21589===a)1&(r=e.read_shift(1))&&(i.mtime=e.read_shift(4)),n>5&&(2&r&&(i.atime=e.read_shift(4)),4&r&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime));e.l=s,t[a]=i}return t}function i(){return e||(e={})}function o(e,t){if(80==e[0]&&75==e[1])return de(e,t);if(109==(32|e[0])&&105==(32|e[1]))return function(e,t){if("mime-version:"!=N(e.slice(0,13)).toLowerCase())throw new Error("Unsupported MAD header");var r=t&&t.root||"",a=(E&&Buffer.isBuffer(e)?e.toString("binary"):N(e)).split("\r\n"),n=0,s="";for(n=0;n<a.length;++n)if(s=a[n],/^Content-Location:/i.test(s)&&(s=s.slice(s.indexOf("file")),r||(r=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),s.slice(0,r.length)!=r););var i=(a[1]||"").match(/boundary="(.*?)"/);if(!i)throw new Error("MAD cannot find boundary");var o="--"+(i[1]||""),c=[],l=[],f={FileIndex:c,FullPaths:l};u(f);var h,d=0;for(n=0;n<a.length;++n){var p=a[n];p!==o&&p!==o+"--"||(d++&&we(f,a.slice(h,n),r),h=n)}return f}(e,t);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var r,a,n,s,i,o,d=512,p=[],m=e.slice(0,512);Jr(m,0);var v=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(b,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(m);switch(r=v[0]){case 3:d=512;break;case 4:d=4096;break;case 0:if(0==v[1])return de(e,t);default:throw new Error("Major Version: Expected 3 or 4 saw "+r)}512!==d&&Jr(m=e.slice(0,d),28);var w=e.slice(0,d);!function(e,t){var r=9;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw new Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw new Error("Sector Shift: Expected 12 saw "+r);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}(m,r);var T=m.read_shift(4,"i");if(3===r&&0!==T)throw new Error("# Directory Sectors: Expected 0 saw "+T);m.l+=4,s=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),i=m.read_shift(4,"i"),a=m.read_shift(4,"i"),o=m.read_shift(4,"i"),n=m.read_shift(4,"i");for(var y=-1,S=0;S<109&&!((y=m.read_shift(4,"i"))<0);++S)p[S]=y;var k=function(e,t){for(var r=Math.ceil(e.length/t)-1,a=[],n=1;n<r;++n)a[n-1]=e.slice(n*t,(n+1)*t);return a[r-1]=e.slice(r*t),a}(e,d);l(o,n,k,d,p);var _=function(e,t,r,a){var n=e.length,s=[],i=[],o=[],c=[],l=a-1,f=0,h=0,u=0,d=0;for(f=0;f<n;++f)if(o=[],(u=f+t)>=n&&(u-=n),!i[u]){c=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,o[o.length]=h,c.push(e[h]);var m=r[Math.floor(4*h/a)];if(a<4+(d=4*h&l))throw new Error("FAT boundary crossed: "+h+" 4 "+a);if(!e[m])break;if(p[h=Gr(e[m],d)])break}s[u]={nodes:o,data:wr([c])}}return s}(k,s,p,d);_[s].name="!Directory",a>0&&i!==g&&(_[i].name="!MiniFAT"),_[p[0]].name="!FAT",_.fat_addrs=p,_.ssz=d;var A=[],x=[],C=[];!function(e,t,r,a,n,s,i,o){for(var l,u=0,d=a.length?2:0,p=t[e].data,m=0,v=0;m<p.length;m+=128){var b=p.slice(m,m+128);Jr(b,64),v=b.read_shift(2),l=Er(b,0,v-d),a.push(l);var w={name:l,type:b.read_shift(1),color:b.read_shift(1),L:b.read_shift(4,"i"),R:b.read_shift(4,"i"),C:b.read_shift(4,"i"),clsid:b.read_shift(16),state:b.read_shift(4,"i"),start:0,size:0};0!==b.read_shift(2)+b.read_shift(2)+b.read_shift(2)+b.read_shift(2)&&(w.ct=h(b,b.l-8)),0!==b.read_shift(2)+b.read_shift(2)+b.read_shift(2)+b.read_shift(2)&&(w.mt=h(b,b.l-8)),w.start=b.read_shift(4,"i"),w.size=b.read_shift(4,"i"),w.size<0&&w.start<0&&(w.size=w.type=0,w.start=g,w.name=""),5===w.type?(u=w.start,n>0&&u!==g&&(t[u].name="!StreamData")):w.size>=4096?(w.storage="fat",void 0===t[w.start]&&(t[w.start]=f(r,w.start,t.fat_addrs,t.ssz)),t[w.start].name=w.name,w.content=t[w.start].data.slice(0,w.size)):(w.storage="minifat",w.size<0?w.size=0:u!==g&&w.start!==g&&t[u]&&(w.content=c(w,t[u].data,(t[o]||{}).data))),w.content&&Jr(w.content,0),s[l]=w,i.push(w)}}(s,_,k,A,a,{},x,i),function(e,t,r){for(var a=0,n=0,s=0,i=0,o=0,c=r.length,l=[],f=[];a<c;++a)l[a]=f[a]=a,t[a]=r[a];for(;o<f.length;++o)n=e[a=f[o]].L,s=e[a].R,i=e[a].C,l[a]===a&&(-1!==n&&l[n]!==n&&(l[a]=l[n]),-1!==s&&l[s]!==s&&(l[a]=l[s])),-1!==i&&(l[i]=a),-1!==n&&a!=l[a]&&(l[n]=l[a],f.lastIndexOf(n)<o&&f.push(n)),-1!==s&&a!=l[a]&&(l[s]=l[a],f.lastIndexOf(s)<o&&f.push(s));for(a=1;a<c;++a)l[a]===a&&(-1!==s&&l[s]!==s?l[a]=l[s]:-1!==n&&l[n]!==n&&(l[a]=l[n]));for(a=1;a<c;++a)if(0!==e[a].type){if((o=a)!=l[o])do{o=l[o],t[a]=t[o]+"/"+t[a]}while(0!==o&&-1!==l[o]&&o!=l[o]);l[a]=-1}for(t[0]+="/",a=1;a<c;++a)2!==e[a].type&&(t[a]+="/")}(x,C,A),A.shift();var O={FileIndex:x,FullPaths:C};return t&&t.raw&&(O.raw={header:w,sectors:k}),O}function c(e,t,r){for(var a=e.start,n=e.size,s=[],i=a;r&&n>0&&i>=0;)s.push(t.slice(i*v,i*v+v)),n-=v,i=Gr(r,4*i);return 0===s.length?Zr(0):O(s).slice(0,e.size)}function l(e,t,r,a,n){var s=g;if(e===g){if(0!==t)throw new Error("DIFAT chain shorter than expected")}else if(-1!==e){var i=r[e],o=(a>>>2)-1;if(!i)return;for(var c=0;c<o&&(s=Gr(i,4*c))!==g;++c)n.push(s);l(Gr(i,a-4),t-1,r,a,n)}}function f(e,t,r,a,n){var s=[],i=[];n||(n=[]);var o=a-1,c=0,l=0;for(c=t;c>=0;){n[c]=!0,s[s.length]=c,i.push(e[c]);var f=r[Math.floor(4*c/a)];if(a<4+(l=4*c&o))throw new Error("FAT boundary crossed: "+c+" 4 "+a);if(!e[f])break;c=Gr(e[f],l)}return{nodes:s,data:wr([i])}}function h(e,t){return new Date(1e3*(Vr(e,t+4)/1e7*Math.pow(2,32)+Vr(e,t)/1e7-11644473600))}function u(e,t){var r=t||{},a=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=a+"/",e.FileIndex[0]={name:a,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="\x01Sh33tJ5";if(Ce.find(e,"/"+t))return;var r=Zr(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),d(e)}(e)}function d(e,t){u(e);for(var n=!1,s=!1,i=e.FullPaths.length-1;i>=0;--i){var o=e.FileIndex[i];switch(o.type){case 0:s?n=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(o.R*o.L*o.C)&&(n=!0),o.R>-1&&o.L>-1&&o.R==o.L&&(n=!0);break;default:n=!0}}if(n||t){var c=new Date(1987,1,19),l=0,f=Object.create?Object.create(null):{},h=[];for(i=0;i<e.FullPaths.length;++i)f[e.FullPaths[i]]=!0,0!==e.FileIndex[i].type&&h.push([e.FullPaths[i],e.FileIndex[i]]);for(i=0;i<h.length;++i){var d=r(h[i][0]);(s=f[d])||(h.push([d,{name:a(d).replace("/",""),type:1,clsid:x,ct:c,mt:c,content:null}]),f[d]=!0)}for(h.sort((function(e,t){return function(e,t){for(var r=e.split("/"),a=t.split("/"),n=0,s=0,i=Math.min(r.length,a.length);n<i;++n){if(s=r[n].length-a[n].length)return s;if(r[n]!=a[n])return r[n]<a[n]?-1:1}return r.length-a.length}(e[0],t[0])})),e.FullPaths=[],e.FileIndex=[],i=0;i<h.length;++i)e.FullPaths[i]=h[i][0],e.FileIndex[i]=h[i][1];for(i=0;i<h.length;++i){var p=e.FileIndex[i],m=e.FullPaths[i];if(p.name=a(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||x,0===i)p.C=h.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(l=i+1;l<h.length&&r(e.FullPaths[l])!=m;++l);for(p.C=l>=h.length?-1:l,l=i+1;l<h.length&&r(e.FullPaths[l])!=r(m);++l);p.R=l>=h.length?-1:l,p.type=1}else r(e.FullPaths[i+1]||"")==r(m)&&(p.R=i+1),p.type=2}}}function p(e,t){var r=t||{};if("mad"==r.fileType)return function(e,t){for(var r=t||{},a=r.boundary||"SheetJS",n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(a="------="+a).slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,o=e.FileIndex[0],c=1;c<e.FullPaths.length;++c)if(i=e.FullPaths[c].slice(s.length),(o=e.FileIndex[c]).size&&o.content&&"\x01Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,(function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"})).replace(/[\u0080-\uFFFF]/g,(function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"}));for(var l=o.content,f=E&&Buffer.isBuffer(l)?l.toString("binary"):N(l),h=0,u=Math.min(1024,f.length),d=0,p=0;p<=u;++p)(d=f.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;n.push(a),n.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+i),n.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),n.push("Content-Type: "+ve(o,i)),n.push(""),n.push(m?be(f):ge(f))}return n.push(a+"--\r\n"),n.join("\r\n")}(e,r);if(d(e),"zip"===r.fileType)return function(e,t){var r=t||{},a=[],s=[],i=Zr(1),o=r.compression?8:0,c=0;0;var l=0,f=0,h=0,u=0,d=e.FullPaths[0],p=d,m=e.FileIndex[0],v=[],g=0;for(l=1;l<e.FullPaths.length;++l)if(p=e.FullPaths[l].slice(d.length),(m=e.FileIndex[l]).size&&m.content&&"\x01Sh33tJ5"!=p){var b=h,w=Zr(p.length);for(f=0;f<p.length;++f)w.write_shift(1,127&p.charCodeAt(f));w=w.slice(0,w.l),v[u]=xe.buf(m.content,0);var T=m.content;8==o&&(T=F(T)),(i=Zr(30)).write_shift(4,67324752),i.write_shift(2,20),i.write_shift(2,c),i.write_shift(2,o),m.mt?n(i,m.mt):i.write_shift(4,0),i.write_shift(-4,8&c?0:v[u]),i.write_shift(4,8&c?0:T.length),i.write_shift(4,8&c?0:m.content.length),i.write_shift(2,w.length),i.write_shift(2,0),h+=i.length,a.push(i),h+=w.length,a.push(w),h+=T.length,a.push(T),8&c&&((i=Zr(12)).write_shift(-4,v[u]),i.write_shift(4,T.length),i.write_shift(4,m.content.length),h+=i.l,a.push(i)),(i=Zr(46)).write_shift(4,33639248),i.write_shift(2,0),i.write_shift(2,20),i.write_shift(2,c),i.write_shift(2,o),i.write_shift(4,0),i.write_shift(-4,v[u]),i.write_shift(4,T.length),i.write_shift(4,m.content.length),i.write_shift(2,w.length),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(4,0),i.write_shift(4,b),g+=i.l,s.push(i),g+=w.length,s.push(w),++u}return i=Zr(22),i.write_shift(4,101010256),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,u),i.write_shift(2,u),i.write_shift(4,g),i.write_shift(4,h),i.write_shift(2,0),O([O(a),O(s),i])}(e,r);var a=function(e){for(var t=0,r=0,a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(n.content){var s=n.content.length;s>0&&(s<4096?t+=s+63>>6:r+=s+511>>9)}}for(var i=e.FullPaths.length+3>>2,o=t+127>>7,c=(t+7>>3)+r+i+o,l=c+127>>7,f=l<=109?0:Math.ceil((l-109)/127);c+l+f+127>>7>l;)f=++l<=109?0:Math.ceil((l-109)/127);var h=[1,f,l,o,i,r,t,0];return e.FileIndex[0].size=t<<6,h[7]=(e.FileIndex[0].start=h[0]+h[1]+h[2]+h[3]+h[4]+h[5])+(h[6]+7>>3),h}(e),s=Zr(a[7]<<9),i=0,o=0;for(i=0;i<8;++i)s.write_shift(1,A[i]);for(i=0;i<8;++i)s.write_shift(2,0);for(s.write_shift(2,62),s.write_shift(2,3),s.write_shift(2,65534),s.write_shift(2,9),s.write_shift(2,6),i=0;i<3;++i)s.write_shift(2,0);for(s.write_shift(4,0),s.write_shift(4,a[2]),s.write_shift(4,a[0]+a[1]+a[2]+a[3]-1),s.write_shift(4,0),s.write_shift(4,4096),s.write_shift(4,a[3]?a[0]+a[1]+a[2]-1:g),s.write_shift(4,a[3]),s.write_shift(-4,a[1]?a[0]-1:g),s.write_shift(4,a[1]),i=0;i<109;++i)s.write_shift(-4,i<a[2]?a[1]+i:-1);if(a[1])for(o=0;o<a[1];++o){for(;i<236+127*o;++i)s.write_shift(-4,i<a[2]?a[1]+i:-1);s.write_shift(-4,o===a[1]-1?g:o+1)}var c=function(e){for(o+=e;i<o-1;++i)s.write_shift(-4,i+1);e&&(++i,s.write_shift(-4,g))};for(o=i=0,o+=a[1];i<o;++i)s.write_shift(-4,C.DIFSECT);for(o+=a[2];i<o;++i)s.write_shift(-4,C.FATSECT);c(a[3]),c(a[4]);for(var l=0,f=0,h=e.FileIndex[0];l<e.FileIndex.length;++l)(h=e.FileIndex[l]).content&&((f=h.content.length)<4096||(h.start=o,c(f+511>>9)));for(c(a[6]+7>>3);511&s.l;)s.write_shift(-4,C.ENDOFCHAIN);for(o=i=0,l=0;l<e.FileIndex.length;++l)(h=e.FileIndex[l]).content&&(!(f=h.content.length)||f>=4096||(h.start=o,c(f+63>>6)));for(;511&s.l;)s.write_shift(-4,C.ENDOFCHAIN);for(i=0;i<a[4]<<2;++i){var u=e.FullPaths[i];if(u&&0!==u.length){h=e.FileIndex[i],0===i&&(h.start=h.size?h.start-1:g);var p=0===i&&r.root||h.name;if(f=2*(p.length+1),s.write_shift(64,p,"utf16le"),s.write_shift(2,f),s.write_shift(1,h.type),s.write_shift(1,h.color),s.write_shift(-4,h.L),s.write_shift(-4,h.R),s.write_shift(-4,h.C),h.clsid)s.write_shift(16,h.clsid,"hex");else for(l=0;l<4;++l)s.write_shift(4,0);s.write_shift(4,h.state||0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,h.start),s.write_shift(4,h.size),s.write_shift(4,0)}else{for(l=0;l<17;++l)s.write_shift(4,0);for(l=0;l<3;++l)s.write_shift(4,-1);for(l=0;l<12;++l)s.write_shift(4,0)}}for(i=1;i<e.FileIndex.length;++i)if((h=e.FileIndex[i]).size>=4096)if(s.l=h.start+1<<9,E&&Buffer.isBuffer(h.content))h.content.copy(s,s.l,0,h.size),s.l+=h.size+511&-512;else{for(l=0;l<h.size;++l)s.write_shift(1,h.content[l]);for(;511&l;++l)s.write_shift(1,0)}for(i=1;i<e.FileIndex.length;++i)if((h=e.FileIndex[i]).size>0&&h.size<4096)if(E&&Buffer.isBuffer(h.content))h.content.copy(s,s.l,0,h.size),s.l+=h.size+63&-64;else{for(l=0;l<h.size;++l)s.write_shift(1,h.content[l]);for(;63&l;++l)s.write_shift(1,0)}if(E)s.l=s.length;else for(;s.l<s.length;)s.write_shift(1,0);return s}t.version="1.2.1";var m,v=64,g=-2,b="d0cf11e0a1b11ae1",A=[208,207,17,224,161,177,26,225],x="00000000000000000000000000000000",C={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:g,FREESECT:-1,HEADER_SIGNATURE:b,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:x,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function N(e){for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function F(e){return m?m.deflateRawSync(e):ne(e)}var D=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],P=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],L=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function M(e){var t=139536&(e<<1|e<<11)|558144&(e<<5|e<<15);return 255&(t>>16|t>>8|t)}for(var U="undefined"!==typeof Uint8Array,B=U?new Uint8Array(256):[],W=0;W<256;++W)B[W]=M(W);function H(e,t){var r=B[255&e];return t<=8?r>>>8-t:(r=r<<8|B[e>>8&255],t<=16?r>>>16-t:(r=r<<8|B[e>>16&255])>>>24-t)}function z(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=6?0:e[a+1]<<8))>>>r&3}function V(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=5?0:e[a+1]<<8))>>>r&7}function G(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=3?0:e[a+1]<<8))>>>r&31}function j(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=1?0:e[a+1]<<8))>>>r&127}function X(e,t,r){var a=7&t,n=t>>>3,s=(1<<r)-1,i=e[n]>>>a;return r<8-a?i&s:(i|=e[n+1]<<8-a,r<16-a?i&s:(i|=e[n+2]<<16-a,r<24-a?i&s:(i|=e[n+3]<<24-a)&s))}function $(e,t,r){var a=7&t,n=t>>>3;return a<=5?e[n]|=(7&r)<<a:(e[n]|=r<<a&255,e[n+1]=(7&r)>>8-a),t+3}function Y(e,t,r){return r=(1&r)<<(7&t),e[t>>>3]|=r,t+1}function K(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=r,t+8}function J(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=255&r,e[a+2]=r>>>8,t+16}function q(e,t){var r=e.length,a=2*r>t?2*r:t+5,n=0;if(r>=t)return e;if(E){var s=k(a);if(e.copy)e.copy(s);else for(;n<e.length;++n)s[n]=e[n];return s}if(U){var i=new Uint8Array(a);if(i.set)i.set(e);else for(;n<r;++n)i[n]=e[n];return i}return e.length=a,e}function Z(e){for(var t=new Array(e),r=0;r<e;++r)t[r]=0;return t}function Q(e,t,r){var a=1,n=0,s=0,i=0,o=0,c=e.length,l=U?new Uint16Array(32):Z(32);for(s=0;s<32;++s)l[s]=0;for(s=c;s<r;++s)e[s]=0;c=e.length;var f=U?new Uint16Array(c):Z(c);for(s=0;s<c;++s)l[n=e[s]]++,a<n&&(a=n),f[s]=0;for(l[0]=0,s=1;s<=a;++s)l[s+16]=o=o+l[s-1]<<1;for(s=0;s<c;++s)0!=(o=e[s])&&(f[s]=l[o+16]++);var h=0;for(s=0;s<c;++s)if(0!=(h=e[s]))for(o=H(f[s],a)>>a-h,i=(1<<a+4-h)-1;i>=0;--i)t[o|i<<h]=15&h|s<<4;return a}var ee=U?new Uint16Array(512):Z(512),te=U?new Uint16Array(32):Z(32);if(!U){for(var re=0;re<512;++re)ee[re]=0;for(re=0;re<32;++re)te[re]=0}!function(){for(var e=[],t=0;t<32;t++)e.push(5);Q(e,te,32);var r=[];for(t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);Q(r,ee,288)}();var ae=function(){for(var e=U?new Uint8Array(32768):[],t=0,r=0;t<L.length-1;++t)for(;r<L[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var a=U?new Uint8Array(259):[];for(t=0,r=0;t<P.length-1;++t)for(;r<P[t+1];++r)a[r]=t;return function(t,r){return t.length<8?function(e,t){for(var r=0;r<e.length;){var a=Math.min(65535,e.length-r),n=r+a==e.length;for(t.write_shift(1,+n),t.write_shift(2,a),t.write_shift(2,65535&~a);a-- >0;)t[t.l++]=e[r++]}return t.l}(t,r):function(t,r){for(var n=0,s=0,i=U?new Uint16Array(32768):[];s<t.length;){var o=Math.min(65535,t.length-s);if(o<10){for(7&(n=$(r,n,+!(s+o!=t.length)))&&(n+=8-(7&n)),r.l=n/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);o-- >0;)r[r.l++]=t[s++];n=8*r.l}else{n=$(r,n,+!(s+o!=t.length)+2);for(var c=0;o-- >0;){var l=t[s],f=-1,h=0;if((f=i[c=32767&(c<<5^l)])&&((f|=-32768&s)>s&&(f-=32768),f<s))for(;t[f+h]==t[s+h]&&h<250;)++h;if(h>2){(l=a[h])<=22?n=K(r,n,B[l+1]>>1)-1:(K(r,n,3),K(r,n+=5,B[l-23]>>5),n+=3);var u=l<8?0:l-4>>2;u>0&&(J(r,n,h-P[l]),n+=u),l=e[s-f],n=K(r,n,B[l]>>3),n-=3;var d=l<4?0:l-2>>1;d>0&&(J(r,n,s-f-L[l]),n+=d);for(var p=0;p<h;++p)i[c]=32767&s,c=32767&(c<<5^t[s]),++s;o-=h-1}else l<=143?l+=48:n=Y(r,n,1),n=K(r,n,B[l]),i[c]=32767&s,++s}n=K(r,n,0)-1}}return r.l=(n+7)/8|0,r.l}(t,r)}}();function ne(e){var t=Zr(50+Math.floor(1.1*e.length)),r=ae(e,t);return t.slice(0,r)}var se=U?new Uint16Array(32768):Z(32768),ie=U?new Uint16Array(32768):Z(32768),oe=U?new Uint16Array(128):Z(128),ce=1,le=1;function fe(e,t){var r=G(e,t)+257,a=G(e,t+=5)+1,n=function(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=4?0:e[a+1]<<8))>>>r&15}(e,t+=5)+4;t+=4;for(var s=0,i=U?new Uint8Array(19):Z(19),o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],c=1,l=U?new Uint8Array(8):Z(8),f=U?new Uint8Array(8):Z(8),h=i.length,u=0;u<n;++u)i[D[u]]=s=V(e,t),c<s&&(c=s),l[s]++,t+=3;var d=0;for(l[0]=0,u=1;u<=c;++u)f[u]=d=d+l[u-1]<<1;for(u=0;u<h;++u)0!=(d=i[u])&&(o[u]=f[d]++);var p=0;for(u=0;u<h;++u)if(0!=(p=i[u])){d=B[o[u]]>>8-p;for(var m=(1<<7-p)-1;m>=0;--m)oe[d|m<<p]=7&p|u<<3}var v=[];for(c=1;v.length<r+a;)switch(t+=7&(d=oe[j(e,t)]),d>>>=3){case 16:for(s=3+z(e,t),t+=2,d=v[v.length-1];s-- >0;)v.push(d);break;case 17:for(s=3+V(e,t),t+=3;s-- >0;)v.push(0);break;case 18:for(s=11+j(e,t),t+=7;s-- >0;)v.push(0);break;default:v.push(d),c<d&&(c=d)}var g=v.slice(0,r),b=v.slice(r);for(u=r;u<286;++u)g[u]=0;for(u=a;u<30;++u)b[u]=0;return ce=Q(g,se,286),le=Q(b,ie,30),t}function he(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[S(t),2];for(var r=0,a=0,n=k(t||1<<18),s=0,i=n.length>>>0,o=0,c=0;0==(1&a);)if(a=V(e,r),r+=3,a>>>1!=0)for(a>>1==1?(o=9,c=5):(r=fe(e,r),o=ce,c=le);;){!t&&i<s+32767&&(i=(n=q(n,s+32767)).length);var l=X(e,r,o),f=a>>>1==1?ee[l]:se[l];if(r+=15&f,0===((f>>>=4)>>>8&255))n[s++]=f;else{if(256==f)break;var h=(f-=257)<8?0:f-4>>2;h>5&&(h=0);var u=s+P[f];h>0&&(u+=X(e,r,h),r+=h),l=X(e,r,c),r+=15&(f=a>>>1==1?te[l]:ie[l]);var d=(f>>>=4)<4?0:f-2>>1,p=L[f];for(d>0&&(p+=X(e,r,d),r+=d),!t&&i<u&&(i=(n=q(n,u+100)).length);s<u;)n[s]=n[s-p],++s}}else{7&r&&(r+=8-(7&r));var m=e[r>>>3]|e[1+(r>>>3)]<<8;if(r+=32,m>0)for(!t&&i<s+m&&(i=(n=q(n,s+m)).length);m-- >0;)n[s++]=e[r>>>3],r+=8}return t?[n,r+7>>>3]:[n.slice(0,s),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function ue(e,t){if(!e)throw new Error(t);"undefined"!==typeof console&&console.error(t)}function de(e,t){var r=e;Jr(r,0);var a={FileIndex:[],FullPaths:[]};u(a,{root:t.root});for(var n=r.length-4;(80!=r[n]||75!=r[n+1]||5!=r[n+2]||6!=r[n+3])&&n>=0;)--n;r.l=n+4,r.l+=4;var i=r.read_shift(2);r.l+=6;var o=r.read_shift(4);for(r.l=o,n=0;n<i;++n){r.l+=20;var c=r.read_shift(4),l=r.read_shift(4),f=r.read_shift(2),h=r.read_shift(2),d=r.read_shift(2);r.l+=8;var p=r.read_shift(4),m=s(r.slice(r.l+f,r.l+f+h));r.l+=f+h+d;var v=r.l;r.l=p+4,pe(r,c,l,a,m),r.l=v}return a}function pe(e,t,r,a,n){e.l+=2;var i=e.read_shift(2),o=e.read_shift(2),c=function(e){var t=65535&e.read_shift(2),r=65535&e.read_shift(2),a=new Date,n=31&r,s=15&(r>>>=5);r>>>=4,a.setMilliseconds(0),a.setFullYear(r+1980),a.setMonth(s-1),a.setDate(n);var i=31&t,o=63&(t>>>=5);return t>>>=6,a.setHours(t),a.setMinutes(o),a.setSeconds(i<<1),a}(e);if(8257&i)throw new Error("Unsupported ZIP encryption");e.read_shift(4);for(var l=e.read_shift(4),f=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d="",p=0;p<h;++p)d+=String.fromCharCode(e[e.l++]);if(u){var v=s(e.slice(e.l,e.l+u));(v[21589]||{}).mt&&(c=v[21589].mt),((n||{})[21589]||{}).mt&&(c=n[21589].mt)}e.l+=u;var g=e.slice(e.l,e.l+l);switch(o){case 8:g=function(e,t){if(!m)return he(e,t);var r=new(0,m.InflateRaw),a=r._processChunk(e.slice(e.l),r._finishFlushFlag);return e.l+=r.bytesRead,a}(e,f);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+o)}var b=!1;8&i&&(134695760==e.read_shift(4)&&(e.read_shift(4),b=!0),l=e.read_shift(4),f=e.read_shift(4)),l!=t&&ue(b,"Bad compressed size: "+t+" != "+l),f!=r&&ue(b,"Bad uncompressed size: "+r+" != "+f),Te(a,d,g,{unsafe:!0,mt:c})}var me={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function ve(e,t){if(e.ctype)return e.ctype;var r=e.name||"",a=r.match(/\.([^\.]+)$/);return a&&me[a[1]]||t&&(a=(r=t).match(/[\.\\]([^\.\\])+$/))&&me[a[1]]?me[a[1]]:"application/octet-stream"}function ge(e){for(var t=w(e),r=[],a=0;a<t.length;a+=76)r.push(t.slice(a,a+76));return r.join("\r\n")+"\r\n"}function be(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,(function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)}));"\n"==(t=t.replace(/ $/gm,"=20").replace(/\t$/gm,"=09")).charAt(0)&&(t="=0D"+t.slice(1));for(var r=[],a=(t=t.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A")).split("\r\n"),n=0;n<a.length;++n){var s=a[n];if(0!=s.length)for(var i=0;i<s.length;){var o=76,c=s.slice(i,i+o);"="==c.charAt(o-1)?o--:"="==c.charAt(o-2)?o-=2:"="==c.charAt(o-3)&&(o-=3),c=s.slice(i,i+o),(i+=o)<s.length&&(c+="="),r.push(c)}else r.push("")}return r.join("\r\n")}function we(e,t,r){for(var a,n="",s="",i="",o=0;o<10;++o){var c=t[o];if(!c||c.match(/^\s*$/))break;var l=c.match(/^(.*?):\s*([^\s].*)$/);if(l)switch(l[1].toLowerCase()){case"content-location":n=l[2].trim();break;case"content-type":i=l[2].trim();break;case"content-transfer-encoding":s=l[2].trim()}}switch(++o,s.toLowerCase()){case"base64":a=_(T(t.slice(o).join("")));break;case"quoted-printable":a=function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r];r<=e.length&&"="==a.charAt(a.length-1);)a=a.slice(0,a.length-1)+e[++r];t.push(a)}for(var n=0;n<t.length;++n)t[n]=t[n].replace(/[=][0-9A-Fa-f]{2}/g,(function(e){return String.fromCharCode(parseInt(e.slice(1),16))}));return _(t.join("\r\n"))}(t.slice(o));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+s)}var f=Te(e,n.slice(r.length),a,{unsafe:!0});i&&(f.ctype=i)}function Te(e,t,r,n){var s=n&&n.unsafe;s||u(e);var i=!s&&Ce.find(e,t);if(!i){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),i={name:a(t),type:2},e.FileIndex.push(i),e.FullPaths.push(o),s||Ce.utils.cfb_gc(e)}return i.content=r,i.size=r?r.length:0,n&&(n.CLSID&&(i.clsid=n.CLSID),n.mt&&(i.mt=n.mt),n.ct&&(i.ct=n.ct)),i}return t.find=function(e,t){var r=e.FullPaths.map((function(e){return e.toUpperCase()})),a=r.map((function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]})),n=!1;47===t.charCodeAt(0)?(n=!0,t=r[0].slice(0,-1)+t):n=-1!==t.indexOf("/");var s=t.toUpperCase(),i=!0===n?r.indexOf(s):a.indexOf(s);if(-1!==i)return e.FileIndex[i];var o=!s.match(I);for(s=s.replace(R,""),o&&(s=s.replace(I,"!")),i=0;i<r.length;++i){if((o?r[i].replace(I,"!"):r[i]).replace(R,"")==s)return e.FileIndex[i];if((o?a[i].replace(I,"!"):a[i]).replace(R,"")==s)return e.FileIndex[i]}return null},t.read=function(t,r){var a=r&&r.type;switch(a||E&&Buffer.isBuffer(t)&&(a="buffer"),a||"base64"){case"file":return function(t,r){return i(),o(e.readFileSync(t),r)}(t,r);case"base64":return o(_(T(t)),r);case"binary":return o(_(t),r)}return o(t,r)},t.parse=o,t.write=function(t,r){var a=p(t,r);switch(r&&r.type||"buffer"){case"file":return i(),e.writeFileSync(r.filename,a),a;case"binary":return"string"==typeof a?a:N(a);case"base64":return w("string"==typeof a?a:N(a));case"buffer":if(E)return Buffer.isBuffer(a)?a:y(a);case"array":return"string"==typeof a?_(a):a}return a},t.writeFile=function(t,r,a){i();var n=p(t,a);e.writeFileSync(r,n)},t.utils={cfb_new:function(e){var t={};return u(t,e),t},cfb_add:Te,cfb_del:function(e,t){u(e);var r=Ce.find(e,t);if(r)for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==r)return e.FileIndex.splice(a,1),e.FullPaths.splice(a,1),!0;return!1},cfb_mov:function(e,t,r){u(e);var n=Ce.find(e,t);if(n)for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==n)return e.FileIndex[s].name=a(r),e.FullPaths[s]=r,!0;return!1},cfb_gc:function(e){d(e,!0)},ReadShift:Xr,CheckField:Kr,prep_blob:Jr,bconcat:O,use_zlib:function(e){try{var t=new(0,e.InflateRaw);if(t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag),!t.bytesRead)throw new Error("zlib does not expose bytesRead");m=e}catch(r){console.error("cannot use native zlib: "+(r.message||r))}},_deflateRaw:ne,_inflateRaw:he,consts:C},t}();let Oe;function Re(e){Oe=e}function Ie(e){return"string"===typeof e?A(e):Array.isArray(e)?function(e){if("undefined"===typeof Uint8Array)throw new Error("Unsupported");return new Uint8Array(e)}(e):e}function Ne(e,t,r){if("undefined"!==typeof Oe&&Oe.writeFileSync)return r?Oe.writeFileSync(e,t,r):Oe.writeFileSync(e,t);if("undefined"!==typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=A(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var a="utf8"==r?Mt(t):t;if("undefined"!==typeof IE_SaveFile)return IE_SaveFile(a,e);if("undefined"!==typeof Blob){var n=new Blob([Ie(a)],{type:"application/octet-stream"});if("undefined"!==typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(n,e);if("undefined"!==typeof saveAs)return saveAs(n,e);if("undefined"!==typeof URL&&"undefined"!==typeof document&&document.createElement&&URL.createObjectURL){var s=URL.createObjectURL(n);if("object"===typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!==typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(s)}),6e4),chrome.downloads.download({url:s,filename:e,saveAs:!0});var i=document.createElement("a");if(null!=i.download)return i.download=e,i.href=s,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL&&"undefined"!==typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(s)}),6e4),s}}if("undefined"!==typeof $&&"undefined"!==typeof File&&"undefined"!==typeof Folder)try{var o=File(e);return o.open("w"),o.encoding="binary",Array.isArray(t)&&(t=x(t)),o.write(t),o.close(),t}catch(c){if(!c.message||!c.message.match(/onstruct/))throw c}throw new Error("cannot save file "+e)}function Fe(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function De(e,t){for(var r=[],a=Fe(e),n=0;n!==a.length;++n)null==r[e[a[n]][t]]&&(r[e[a[n]][t]]=a[n]);return r}function Pe(e){for(var t=[],r=Fe(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}function Le(e){for(var t=[],r=Fe(e),a=0;a!==r.length;++a)t[e[r[a]]]=parseInt(r[a],10);return t}var Me=new Date(1899,11,30,0,0,0);function Ue(e,t){var r=e.getTime();return t&&(r-=1263168e5),(r-(Me.getTime()+6e4*(e.getTimezoneOffset()-Me.getTimezoneOffset())))/864e5}var Be=new Date,We=Me.getTime()+6e4*(Be.getTimezoneOffset()-Me.getTimezoneOffset()),He=Be.getTimezoneOffset();function ze(e){var t=new Date;return t.setTime(24*e*60*60*1e3+We),t.getTimezoneOffset()!==He&&t.setTime(t.getTime()+6e4*(t.getTimezoneOffset()-He)),t}function Ve(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(r=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(!a)throw new Error("Unsupported ISO Duration Field: M");r*=60}t+=r*parseInt(n[s],10)}return t}var Ge=new Date("2017-02-19T19:06:09.000Z"),je=isNaN(Ge.getFullYear())?new Date("2/19/17"):Ge,Xe=2017==je.getFullYear();function $e(e,t){var r=new Date(e);if(Xe)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==je.getFullYear()&&!isNaN(r.getFullYear())){var a=r.getFullYear();return e.indexOf(""+a)>-1||r.setFullYear(r.getFullYear()+100),r}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-60*s.getTimezoneOffset()*1e3)),s}function Ye(e,t){if(E&&Buffer.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return Mt(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return Mt(u(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!==typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return Mt(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return Mt(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"\u20ac":"\x80","\u201a":"\x82","\u0192":"\x83","\u201e":"\x84","\u2026":"\x85","\u2020":"\x86","\u2021":"\x87","\u02c6":"\x88","\u2030":"\x89","\u0160":"\x8a","\u2039":"\x8b","\u0152":"\x8c","\u017d":"\x8e","\u2018":"\x91","\u2019":"\x92","\u201c":"\x93","\u201d":"\x94","\u2022":"\x95","\u2013":"\x96","\u2014":"\x97","\u02dc":"\x98","\u2122":"\x99","\u0161":"\x9a","\u203a":"\x9b","\u0153":"\x9c","\u017e":"\x9e","\u0178":"\x9f"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[\u20ac\u201a\u0192\u201e\u2026\u2020\u2021\u02c6\u2030\u0160\u2039\u0152\u017d\u2018\u2019\u201c\u201d\u2022\u2013\u2014\u02dc\u2122\u0161\u203a\u0153\u017e\u0178]/g,(function(e){return r[e]||e}))}catch(s){}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function Ke(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=Ke(e[r]));return t}function Je(e,t){for(var r="";r.length<t;)r+=e;return r}function qe(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,(function(){return r*=100,""}));return isNaN(t=Number(a))?(a=a.replace(/[(](.*)[)]/,(function(e,t){return r=-r,t})),isNaN(t=Number(a))?t:t/r):t/r}var Ze=["january","february","march","april","may","june","july","august","september","october","november","december"];function Qe(e){var t=new Date(e),r=new Date(NaN),a=t.getYear(),n=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==Ze.indexOf(i))return r}else if(i.match(/[a-z]/))return r;return a<0||a>8099?r:(n>0||s>1)&&101!=a?t:e.match(/[^-0-9:,\/\\]/)?r:t}var et=function(){var e=5=="abacaba".split(/(:?b)/i).length;return function(t,r,a){if(e||"string"==typeof r)return t.split(r);for(var n=t.split(r),s=[n[0]],i=1;i<n.length;++i)s.push(a),s.push(n[i]);return s}}();function tt(e){return e?e.content&&e.type?Ye(e.content,!0):e.data?p(e.data):e.asNodeBuffer&&E?p(e.asNodeBuffer().toString("binary")):e.asBinary?p(e.asBinary()):e._data&&e._data.getContent?p(Ye(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function rt(e){if(!e)return null;if(e.data)return h(e.data);if(e.asNodeBuffer&&E)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return"string"==typeof t?h(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function at(e,t){for(var r=e.FullPaths||Fe(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function nt(e,t){var r=at(e,t);if(null==r)throw new Error("Cannot find file "+t+" in zip");return r}function st(e,t,r){if(!r)return(a=nt(e,t))&&".bin"===a.name.slice(-4)?rt(a):tt(a);var a;if(!t)return null;try{return st(e,t)}catch(n){return null}}function it(e,t,r){if(!r)return tt(nt(e,t));if(!t)return null;try{return it(e,t)}catch(a){return null}}function ot(e,t,r){if(!r)return rt(nt(e,t));if(!t)return null;try{return ot(e,t)}catch(a){return null}}function ct(e){for(var t=e.FullPaths||Fe(e.files),r=[],a=0;a<t.length;++a)"/"!=t[a].slice(-1)&&r.push(t[a].replace(/^Root Entry[\/]/,""));return r.sort()}function lt(e,t,r){if(e.FullPaths){var a;if("string"==typeof r)return a=E?y(r):function(e){for(var t=[],r=0,a=e.length+250,n=S(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[r++]=i;else if(i<2048)n[r++]=192|i>>6&31,n[r++]=128|63&i;else if(i>=55296&&i<57344){i=64+(1023&i);var o=1023&e.charCodeAt(++s);n[r++]=240|i>>8&7,n[r++]=128|i>>2&63,n[r++]=128|o>>6&15|(3&i)<<4,n[r++]=128|63&o}else n[r++]=224|i>>12&15,n[r++]=128|i>>6&63,n[r++]=128|63&i;r>a&&(t.push(n.slice(0,r)),r=0,n=S(65535),a=65530)}return t.push(n.slice(0,r)),O(t)}(r),Ce.utils.cfb_add(e,t,a);Ce.utils.cfb_add(e,t,r)}else e.file(t,r)}function ft(){return Ce.utils.cfb_new()}function ht(e,t){switch(t.type){case"base64":return Ce.read(e,{type:"base64"});case"binary":return Ce.read(e,{type:"binary"});case"buffer":case"array":return Ce.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+t.type)}function ut(e,t){if("/"==e.charAt(0))return e.slice(1);var r=t.split("/");"/"!=t.slice(-1)&&r.pop();for(var a=e.split("/");0!==a.length;){var n=a.shift();".."===n?r.pop():"."!==n&&r.push(n)}return r.join("/")}var dt='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',pt=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,mt=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/gm,vt=dt.match(mt)?mt:/<[^>]*>/g,gt=/<\w*:/,bt=/<(\/?)\w+:/;function wt(e,t,r){for(var a={},n=0,s=0;n!==e.length&&(32!==(s=e.charCodeAt(n))&&10!==s&&13!==s);++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(pt),o=0,c="",l=0,f="",h="",u=1;if(i)for(l=0;l!=i.length;++l){for(h=i[l],s=0;s!=h.length&&61!==h.charCodeAt(s);++s);for(f=h.slice(0,s).trim();32==h.charCodeAt(s+1);)++s;for(u=34==(n=h.charCodeAt(s+1))||39==n?1:0,c=h.slice(s+1+u,h.length-u),o=0;o!=f.length&&58!==f.charCodeAt(o);++o);if(o===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=c,r||(a[f.toLowerCase()]=c);else{var d=(5===o&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(o+1);if(a[d]&&"ext"==f.slice(o-3,o))continue;a[d]=c,r||(a[d.toLowerCase()]=c)}}return a}function Tt(e){return e.replace(bt,"<$1")}var Et={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},yt=Pe(Et),St=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/gi,t=/_x([\da-fA-F]{4})_/gi;return function r(a){var n=a+"",s=n.indexOf("<![CDATA[");if(-1==s)return n.replace(e,(function(e,t){return Et[e]||String.fromCharCode(parseInt(t,e.indexOf("x")>-1?16:10))||e})).replace(t,(function(e,t){return String.fromCharCode(parseInt(t,16))}));var i=n.indexOf("]]>");return r(n.slice(0,s))+n.slice(s+9,i)+r(n.slice(i+3))}}(),kt=/[&<>'"]/g,_t=/[\u0000-\u0008\u000b-\u001f]/g;function At(e){return(e+"").replace(kt,(function(e){return yt[e]})).replace(_t,(function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"}))}function xt(e){return At(e).replace(/ /g,"_x0020_")}var Ct=/[\u0000-\u001f]/g;function Ot(e){return(e+"").replace(kt,(function(e){return yt[e]})).replace(/\n/g,"<br/>").replace(Ct,(function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"}))}var Rt=function(){var e=/&#(\d+);/g;function t(e,t){return String.fromCharCode(parseInt(t,10))}return function(r){return r.replace(e,t)}}();function It(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Nt(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0;r<e.length;)(a=e.charCodeAt(r++))<128?t+=String.fromCharCode(a):(n=e.charCodeAt(r++),a>191&&a<224?(i=(31&a)<<6,i|=63&n,t+=String.fromCharCode(i)):(s=e.charCodeAt(r++),a<240?t+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&s):(o=((7&a)<<18|(63&n)<<12|(63&s)<<6|63&(i=e.charCodeAt(r++)))-65536,t+=String.fromCharCode(55296+(o>>>10&1023)),t+=String.fromCharCode(56320+(1023&o)))));return t}function Ft(e){var t,r,a,n=S(2*e.length),s=1,i=0,o=0;for(r=0;r<e.length;r+=s)s=1,(a=e.charCodeAt(r))<128?t=a:a<224?(t=64*(31&a)+(63&e.charCodeAt(r+1)),s=2):a<240?(t=4096*(15&a)+64*(63&e.charCodeAt(r+1))+(63&e.charCodeAt(r+2)),s=3):(s=4,t=262144*(7&a)+4096*(63&e.charCodeAt(r+1))+64*(63&e.charCodeAt(r+2))+(63&e.charCodeAt(r+3)),o=55296+((t-=65536)>>>10&1023),t=56320+(1023&t)),0!==o&&(n[i++]=255&o,n[i++]=o>>>8,o=0),n[i++]=t%256,n[i++]=t>>>8;return n.slice(0,i).toString("ucs2")}function Dt(e){return y(e,"binary").toString("utf8")}var Pt="foo bar baz\xe2\x98\x83\xf0\x9f\x8d\xa3",Lt=E&&(Dt(Pt)==Nt(Pt)&&Dt||Ft(Pt)==Nt(Pt)&&Ft)||Nt,Mt=E?function(e){return y(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(63&a)));break;case a>=55296&&a<57344:a-=55296,n=e.charCodeAt(r++)-56320+(a<<10),t.push(String.fromCharCode(240+(n>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)))}return t.join("")},Ut=function(){var e={};return function(t,r){var a=t+"|"+(r||"");return e[a]?e[a]:e[a]=new RegExp("<(?:\\w+:)?"+t+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+t+">",r||"")}}(),Bt=function(){var e=[["nbsp"," "],["middot","\xb7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map((function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]}));return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),a=0;a<e.length;++a)r=r.replace(e[a][0],e[a][1]);return r}}(),Wt=function(){var e={};return function(t){return void 0!==e[t]?e[t]:e[t]=new RegExp("<(?:vt:)?"+t+">([\\s\\S]*?)</(?:vt:)?"+t+">","g")}}(),Ht=/<\/?(?:vt:)?variant>/g,zt=/<(?:vt:)([^>]*)>([\s\S]*)</;function Vt(e,t){var r=wt(e),a=e.match(Wt(r.baseType))||[],n=[];if(a.length!=r.size){if(t.WTF)throw new Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach((function(e){var t=e.replace(Ht,"").match(zt);t&&n.push({v:Lt(t[2]),t:t[1]})})),n}var Gt=/(^\s|\s$|\n)/;function jt(e,t){return"<"+e+(t.match(Gt)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Xt(e){return Fe(e).map((function(t){return" "+t+'="'+e[t]+'"'})).join("")}function $t(e,t,r){return"<"+e+(null!=r?Xt(r):"")+(null!=t?(t.match(Gt)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function Yt(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function Kt(e){if(E&&Buffer.isBuffer(e))return e.toString("utf8");if("string"===typeof e)return e;if("undefined"!==typeof Uint8Array&&e instanceof Uint8Array)return Lt(x(C(e)));throw new Error("Bad input format: expected Buffer or string")}var Jt=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/gm,qt="http://schemas.openxmlformats.org/package/2006/metadata/core-properties",Zt="http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",Qt="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",er="http://schemas.openxmlformats.org/package/2006/content-types",tr="http://schemas.openxmlformats.org/package/2006/relationships",rr="http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",ar="http://purl.org/dc/elements/1.1/",nr="http://purl.org/dc/terms/",sr="http://purl.org/dc/dcmitype/",ir="http://schemas.openxmlformats.org/officeDocument/2006/relationships",or="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",cr="http://www.w3.org/2001/XMLSchema-instance",lr="http://www.w3.org/2001/XMLSchema",fr=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],hr="urn:schemas-microsoft-com:office:office",ur="urn:schemas-microsoft-com:office:excel",dr="urn:schemas-microsoft-com:office:spreadsheet",pr="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mr="http://macVmlSchemaUri",vr="urn:schemas-microsoft-com:vml",gr="http://www.w3.org/TR/REC-html40";var br=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var a=0,n=e[0][r].length;a<n;a+=10240)t.push.apply(t,e[0][r].slice(a,a+10240));return t},wr=E?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map((function(e){return Buffer.isBuffer(e)?e:y(e)}))):br(e)}:br,Tr=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(Hr(e,n)));return a.join("").replace(R,"")},Er=E?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(R,""):Tr(e,t,r)}:Tr,yr=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},Sr=E?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):yr(e,t,r)}:yr,kr=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(Wr(e,n)));return a.join("")},_r=E?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):kr(e,t,r)}:kr,Ar=function(e,t){var r=Vr(e,t);return r>0?_r(e,t+4,t+4+r-1):""},xr=Ar,Cr=function(e,t){var r=Vr(e,t);return r>0?_r(e,t+4,t+4+r-1):""},Or=Cr,Rr=function(e,t){var r=2*Vr(e,t);return r>0?_r(e,t+4,t+4+r-1):""},Ir=Rr,Nr=function(e,t){var r=Vr(e,t);return r>0?Er(e,t+4,t+4+r):""},Fr=Nr,Dr=function(e,t){var r=Vr(e,t);return r>0?_r(e,t+4,t+4+r):""},Pr=Dr,Lr=function(e,t){return function(e,t){for(var r=1-2*(e[t+7]>>>7),a=((127&e[t+7])<<4)+(e[t+6]>>>4&15),n=15&e[t+6],s=5;s>=0;--s)n=256*n+e[t+s];return 2047==a?0==n?r*(1/0):NaN:(0==a?a=-1022:(a-=1023,n+=Math.pow(2,52)),r*Math.pow(2,a-52)*n)}(e,t)},Mr=Lr,Ur=function(e){return Array.isArray(e)||"undefined"!==typeof Uint8Array&&e instanceof Uint8Array};function Br(){Er=function(e,t,r){return d.utils.decode(1200,e.slice(t,r)).replace(R,"")},_r=function(e,t,r){return d.utils.decode(65001,e.slice(t,r))},xr=function(e,t){var r=Vr(e,t);return r>0?d.utils.decode(s,e.slice(t+4,t+4+r-1)):""},Or=function(e,t){var r=Vr(e,t);return r>0?d.utils.decode(n,e.slice(t+4,t+4+r-1)):""},Ir=function(e,t){var r=2*Vr(e,t);return r>0?d.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},Fr=function(e,t){var r=Vr(e,t);return r>0?d.utils.decode(1200,e.slice(t+4,t+4+r)):""},Pr=function(e,t){var r=Vr(e,t);return r>0?d.utils.decode(65001,e.slice(t+4,t+4+r)):""}}E&&(xr=function(e,t){if(!Buffer.isBuffer(e))return Ar(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},Or=function(e,t){if(!Buffer.isBuffer(e))return Cr(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},Ir=function(e,t){if(!Buffer.isBuffer(e))return Rr(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},Fr=function(e,t){if(!Buffer.isBuffer(e))return Nr(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},Pr=function(e,t){if(!Buffer.isBuffer(e))return Dr(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},Mr=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):Lr(e,t)},Ur=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!==typeof Uint8Array&&e instanceof Uint8Array}),"undefined"!==typeof d&&Br();var Wr=function(e,t){return e[t]},Hr=function(e,t){return 256*e[t+1]+e[t]},zr=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-1*(65535-r+1)},Vr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},Gr=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},jr=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Xr(e,t){var r,a,s,i,o,c,l="",f=[];switch(t){case"dbcs":if(c=this.l,E&&Buffer.isBuffer(this))l=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)l+=String.fromCharCode(Hr(this,c)),c+=2;e*=2;break;case"utf8":l=_r(this,this.l,this.l+e);break;case"utf16le":e*=2,l=Er(this,this.l,this.l+e);break;case"wstr":if("undefined"===typeof d)return Xr.call(this,e,"dbcs");l=d.utils.decode(n,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":l=xr(this,this.l),e=4+Vr(this,this.l);break;case"lpstr-cp":l=Or(this,this.l),e=4+Vr(this,this.l);break;case"lpwstr":l=Ir(this,this.l),e=4+2*Vr(this,this.l);break;case"lpp4":e=4+Vr(this,this.l),l=Fr(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+Vr(this,this.l),l=Pr(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,l="";0!==(s=Wr(this,this.l+e++));)f.push(m(s));l=f.join("");break;case"_wstr":for(e=0,l="";0!==(s=Hr(this,this.l+e));)f.push(m(s)),e+=2;e+=2,l=f.join("");break;case"dbcs-cont":for(l="",c=this.l,o=0;o<e;++o){if(this.lens&&-1!==this.lens.indexOf(c))return s=Wr(this,c),this.l=c+1,i=Xr.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),f.join("")+i;f.push(m(Hr(this,c))),c+=2}l=f.join(""),e*=2;break;case"cpstr":if("undefined"!==typeof d){l=d.utils.decode(n,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(l="",c=this.l,o=0;o!=e;++o){if(this.lens&&-1!==this.lens.indexOf(c))return s=Wr(this,c),this.l=c+1,i=Xr.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),f.join("")+i;f.push(m(Wr(this,c))),c+=1}l=f.join("");break;default:switch(e){case 1:return r=Wr(this,this.l),this.l++,r;case 2:return r=("i"===t?zr:Hr)(this,this.l),this.l+=2,r;case 4:case-4:return"i"===t||0===(128&this[this.l+3])?(r=(e>0?Gr:jr)(this,this.l),this.l+=4,r):(a=Vr(this,this.l),this.l+=4,a);case 8:case-8:if("f"===t)return a=8==e?Mr(this,this.l):Mr([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:l=Sr(this,this.l,e)}}return this.l+=e,l}var $r=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function Yr(e,t,r){var a=0,n=0;if("dbcs"===r){for(n=0;n!=t.length;++n)$r(this,t.charCodeAt(n),this.l+2*n);a=2*t.length}else if("sbcs"===r){if("undefined"!==typeof d&&874==s)for(n=0;n!=t.length;++n){var i=d.utils.encode(s,t.charAt(n));this[this.l+n]=i[0]}else for(t=t.replace(/[^\x00-\x7F]/g,"_"),n=0;n!=t.length;++n)this[this.l+n]=255&t.charCodeAt(n);a=t.length}else{if("hex"===r){for(;n<e;++n)this[this.l++]=parseInt(t.slice(2*n,2*n+2),16)||0;return this}if("utf16le"===r){var o=Math.min(this.l+e,this.length);for(n=0;n<Math.min(t.length,e);++n){var c=t.charCodeAt(n);this[this.l++]=255&c,this[this.l++]=c>>8}for(;this.l<o;)this[this.l++]=0;return this}switch(e){case 1:a=1,this[this.l]=255&t;break;case 2:a=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:a=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:a=4,function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255}(this,t,this.l);break;case 8:if(a=8,"f"===r){!function(e,t,r){var a=(t<0||1/t==-1/0?1:0)<<7,n=0,s=0,i=a?-t:t;isFinite(i)?0==i?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?n=-1022:(s-=Math.pow(2,52),n+=1023)):(n=2047,s=isNaN(t)?26985:0);for(var o=0;o<=5;++o,s/=256)e[r+o]=255&s;e[r+6]=(15&n)<<4|15&s,e[r+7]=n>>4|a}(this,t,this.l);break}case 16:break;case-4:a=4,function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255}(this,t,this.l)}}return this.l+=a,this}function Kr(e,t){var r=Sr(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function Jr(e,t){e.l=t,e.read_shift=Xr,e.chk=Kr,e.write_shift=Yr}function qr(e,t){e.l+=t}function Zr(e){var t=S(e);return Jr(t,0),t}function Qr(e,t,r){if(e){var a,n,s;Jr(e,e.l||0);for(var i=e.length,o=0,c=0;e.l<i;){128&(o=e.read_shift(1))&&(o=(127&o)+((127&e.read_shift(1))<<7));var l=af[o]||af[65535];for(s=127&(a=e.read_shift(1)),n=1;n<4&&128&a;++n)s+=(127&(a=e.read_shift(1)))<<7*n;c=e.l+s;var f=l.f&&l.f(e,s,r);if(e.l=c,t(f,l,o))return}}}function ea(){var e=[],t=E?256:2048,r=function(e){var t=Zr(e);return Jr(t,0),t},a=r(t),n=function(){a&&(a.length>a.l&&((a=a.slice(0,a.l)).l=a.length),a.length>0&&e.push(a),a=null)},s=function(e){return a&&e<a.length-a.l?a:(n(),a=r(Math.max(e+1,t)))};return{next:s,push:function(e){n(),null==(a=e).l&&(a.l=a.length),s(t)},end:function(){return n(),O(e)},_bufs:e}}function ta(e,t,r,a){var n,s=+t;if(!isNaN(s)){a||(a=af[s].p||(r||[]).length||0),n=1+(s>=128?1:0)+1,a>=128&&++n,a>=16384&&++n,a>=2097152&&++n;var i=e.next(n);s<=127?i.write_shift(1,s):(i.write_shift(1,128+(127&s)),i.write_shift(1,s>>7));for(var o=0;4!=o;++o){if(!(a>=128)){i.write_shift(1,a);break}i.write_shift(1,128+(127&a)),a>>=7}a>0&&Ur(r)&&e.push(r)}}function ra(e,t,r){var a=Ke(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function aa(e,t,r){var a=Ke(e);return a.s=ra(a.s,t.s,r),a.e=ra(a.e,t.s,r),a}function na(e,t){if(e.cRel&&e.c<0)for(e=Ke(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=Ke(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=ha(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=function(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}(r)),r}function sa(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?na(e.s,t.biff)+":"+na(e.e,t.biff):(e.s.rRel?"":"$")+oa(e.s.r)+":"+(e.e.rRel?"":"$")+oa(e.e.r):(e.s.cRel?"":"$")+la(e.s.c)+":"+(e.e.cRel?"":"$")+la(e.e.c)}function ia(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function oa(e){return""+(e+1)}function ca(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function la(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function fa(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function ha(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function ua(e){var t=e.indexOf(":");return-1==t?{s:fa(e),e:fa(e)}:{s:fa(e.slice(0,t)),e:fa(e.slice(t+1))}}function da(e,t){return"undefined"===typeof t||"number"===typeof t?da(e.s,e.e):("string"!==typeof e&&(e=ha(e)),"string"!==typeof t&&(t=ha(t)),e==t?e:e+":"+t)}function pa(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,s=e.length;for(r=0;a<s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;if(t.s.r=--r,a===s||10!=n)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;return t.e.r=--r,t}function ma(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=Te(e.z,r?Ue(t):t)}catch(a){}try{return e.w=Te((e.XF||{}).numFmtId||(r?14:0),r?Ue(t):t)}catch(a){return""+t}}function va(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t?tn[e.v]||e.v:ma(e,void 0==t?e.v:t))}function ga(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function ba(e,t,r){var a=r||{},n=e?Array.isArray(e):a.dense;var s=e||(n?[]:{}),i=0,o=0;if(s&&null!=a.origin){if("number"==typeof a.origin)i=a.origin;else{var c="string"==typeof a.origin?fa(a.origin):a.origin;i=c.r,o=c.c}s["!ref"]||(s["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=pa(s["!ref"]);l.s.c=f.s.c,l.s.r=f.s.r,l.e.c=Math.max(l.e.c,f.e.c),l.e.r=Math.max(l.e.r,f.e.r),-1==i&&(l.e.r=i=f.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if("undefined"!==typeof t[h][u]){var d={v:t[h][u]},p=i+h,m=o+u;if(l.s.r>p&&(l.s.r=p),l.s.c>m&&(l.s.c=m),l.e.r<p&&(l.e.r=p),l.e.c<m&&(l.e.c=m),!t[h][u]||"object"!==typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=t[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else{if(!a.sheetStubs)continue;d.t="z"}else"number"===typeof d.v?d.t="n":"boolean"===typeof d.v?d.t="b":d.v instanceof Date?(d.z=a.dateNF||H[14],a.cellDates?(d.t="d",d.w=Te(d.z,Ue(d.v))):(d.t="n",d.v=Ue(d.v),d.w=Te(d.z,d.v))):d.t="s";else d=t[h][u];if(n)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var v=ha({c:m,r:p});s[v]&&s[v].z&&(d.z=s[v].z),s[v]=d}}}return l.s.c<1e7&&(s["!ref"]=da(l)),s}function wa(e,t){return ba(null,e,t)}function Ta(e,t){return t||(t=Zr(4)),t.write_shift(4,e),t}function Ea(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function ya(e,t){var r=!1;return null==t&&(r=!0,t=Zr(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function Sa(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function ka(e,t){var r=e.l,a=e.read_shift(1),n=Ea(e),s=[],i={t:n,h:n};if(0!==(1&a)){for(var o=e.read_shift(4),c=0;c!=o;++c)s.push(Sa(e));i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}var _a=ka;function Aa(e,t){var r=!1;return null==t&&(r=!0,t=Zr(23+4*e.t.length)),t.write_shift(1,1),ya(e.t,t),t.write_shift(4,1),function(e,t){t||(t=Zr(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0)}({ich:0,ifnt:0},t),r?t.slice(0,t.l):t}function xa(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function Ca(e,t){return null==t&&(t=Zr(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function Oa(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function Ra(e,t){return null==t&&(t=Zr(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var Ia=Ea,Na=ya;function Fa(e){var t=e.read_shift(4);return 0===t||4294967295===t?"":e.read_shift(t,"dbcs")}function Da(e,t){var r=!1;return null==t&&(r=!0,t=Zr(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var Pa=Ea,La=Fa,Ma=Da;function Ua(e){var t=e.slice(e.l,e.l+4),r=1&t[0],a=2&t[0];e.l+=4;var n=0===a?Mr([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):Gr(t,0)>>2;return r?n/100:n}function Ba(e,t){null==t&&(t=Zr(4));var r=0,a=0,n=100*e;if(e==(0|e)&&e>=-(1<<29)&&e<1<<29?a=1:n==(0|n)&&n>=-(1<<29)&&n<1<<29&&(a=1,r=1),!a)throw new Error("unsupported RkNumber "+e);t.write_shift(-4,((r?n:e)<<2)+(r+2))}function Wa(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var Ha=Wa,za=function(e,t){return t||(t=Zr(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function Va(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Ga(e,t){return(t||Zr(8)).write_shift(8,e,"f")}function ja(e,t){if(t||(t=Zr(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var a=e.rgb||"FFFFFF";"number"==typeof a&&(a=("000000"+a.toString(16)).slice(-6)),t.write_shift(1,parseInt(a.slice(0,2),16)),t.write_shift(1,parseInt(a.slice(2,4),16)),t.write_shift(1,parseInt(a.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}function Xa(e,t){var r=e.read_shift(4);switch(r){case 0:return"";case 4294967295:case 4294967294:return{2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"}[e.read_shift(4)]||""}if(r>400)throw new Error("Unsupported Clipboard: "+r.toString(16));return e.l-=4,e.read_shift(0,1==t?"lpstr":"lpwstr")}var $a=80,Ya=[$a,81],Ka={1:{n:"CodePage",t:2},2:{n:"Category",t:$a},3:{n:"PresentationFormat",t:$a},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:$a},15:{n:"Company",t:$a},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:$a},27:{n:"ContentStatus",t:$a},28:{n:"Language",t:$a},29:{n:"Version",t:$a},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},Ja={1:{n:"CodePage",t:2},2:{n:"Title",t:$a},3:{n:"Subject",t:$a},4:{n:"Author",t:$a},5:{n:"Keywords",t:$a},6:{n:"Comments",t:$a},7:{n:"Template",t:$a},8:{n:"LastAuthor",t:$a},9:{n:"RevNumber",t:$a},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:$a},19:{n:"DocSecurity",t:3},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},qa={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},Za=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function Qa(e){return e.map((function(e){return[e>>16&255,e>>8&255,255&e]}))}var en=Ke(Qa([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),tn={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},rn={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},an={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},nn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function sn(e,t){var r,a=function(e){for(var t=[],r=Fe(e),a=0;a!==r.length;++a)null==t[e[r[a]]]&&(t[e[r[a]]]=[]),t[e[r[a]]].push(r[a]);return t}(an),n=[];n[n.length]=dt,n[n.length]=$t("Types",null,{xmlns:er,"xmlns:xsd":lr,"xmlns:xsi":cr}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map((function(e){return $t("Default",null,{Extension:e[0],ContentType:e[1]})})));var s=function(a){e[a]&&e[a].length>0&&(r=e[a][0],n[n.length]=$t("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:nn[a][t.bookType]||nn[a].xlsx}))},i=function(r){(e[r]||[]).forEach((function(e){n[n.length]=$t("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:nn[r][t.bookType]||nn[r].xlsx})}))},o=function(t){(e[t]||[]).forEach((function(e){n[n.length]=$t("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:a[t][0]})}))};return s("workbooks"),i("sheets"),i("charts"),o("themes"),["strs","styles"].forEach(s),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),i("metadata"),o("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var on={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function cn(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function ln(e,t){var r={"!id":{}};if(!e)return r;"/"!==t.charAt(0)&&(t="/"+t);var a={};return(e.match(vt)||[]).forEach((function(e){var n=wt(e);if("<Relationship"===n[0]){var s={};s.Type=n.Type,s.Target=n.Target,s.Id=n.Id,n.TargetMode&&(s.TargetMode=n.TargetMode);var i="External"===n.TargetMode?n.Target:ut(n.Target,t);r[i]=s,a[n.Id]=s}})),r["!id"]=a,r}function fn(e){var t=[dt,$t("Relationships",null,{xmlns:tr})];return Fe(e["!id"]).forEach((function(r){t[t.length]=$t("Relationship",null,e["!id"][r])})),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function hn(e,t,r,a,n,s){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,n.Id="rId"+t,n.Type=a,n.Target=r,s?n.TargetMode=s:[on.HLINK,on.XPATH,on.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,t}function un(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function dn(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+a.version+"</meta:generator></office:meta></office:document-meta>"}var pn=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],mn=function(){for(var e=new Array(pn.length),t=0;t<pn.length;++t){var r=pn[t],a="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function vn(e){var t={};e=Lt(e);for(var r=0;r<pn.length;++r){var a=pn[r],n=e.match(mn[r]);null!=n&&n.length>0&&(t[a[1]]=St(n[1])),"date"===a[2]&&t[a[1]]&&(t[a[1]]=$e(t[a[1]]))}return t}function gn(e,t,r,a,n){null==n[e]&&null!=t&&""!==t&&(n[e]=t,t=At(t),a[a.length]=r?$t(e,t,r):jt(e,t))}function bn(e,t){var r=t||{},a=[dt,$t("cp:coreProperties",null,{"xmlns:cp":qt,"xmlns:dc":ar,"xmlns:dcterms":nr,"xmlns:dcmitype":sr,"xmlns:xsi":cr})],n={};if(!e&&!r.Props)return a.join("");e&&(null!=e.CreatedDate&&gn("dcterms:created","string"===typeof e.CreatedDate?e.CreatedDate:Yt(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),null!=e.ModifiedDate&&gn("dcterms:modified","string"===typeof e.ModifiedDate?e.ModifiedDate:Yt(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var s=0;s!=pn.length;++s){var i=pn[s],o=r.Props&&null!=r.Props[i[1]]?r.Props[i[1]]:e?e[i[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&gn(i[0],o,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var wn=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],Tn=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function En(e,t,r,a){var n=[];if("string"==typeof e)n=Vt(e,a);else for(var s=0;s<e.length;++s)n=n.concat(e[s].map((function(e){return{v:e}})));var i="string"==typeof t?Vt(t,a).map((function(e){return e.v})):t,o=0,c=0;if(i.length>0)for(var l=0;l!==n.length;l+=2){switch(c=+n[l+1].v,n[l].v){case"Worksheets":case"\u5de5\u4f5c\u8868":case"\u041b\u0438\u0441\u0442\u044b":case"\u0623\u0648\u0631\u0627\u0642 \u0627\u0644\u0639\u0645\u0644":case"\u30ef\u30fc\u30af\u30b7\u30fc\u30c8":case"\u05d2\u05dc\u05d9\u05d5\u05e0\u05d5\u05ea \u05e2\u05d1\u05d5\u05d3\u05d4":case"Arbeitsbl\xe4tter":case"\xc7al\u0131\u015fma Sayfalar\u0131":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de c\xe1lculo":case"Planilhas":case"Regneark":case"Hojas de c\xe1lculo":case"Werkbladen":r.Worksheets=c,r.SheetNames=i.slice(o,o+c);break;case"Named Ranges":case"Rangos con nombre":case"\u540d\u524d\u4ed8\u304d\u4e00\u89a7":case"Benannte Bereiche":case"Navngivne omr\xe5der":r.NamedRanges=c,r.DefinedNames=i.slice(o,o+c);break;case"Charts":case"Diagramme":r.Chartsheets=c,r.ChartNames=i.slice(o,o+c)}o+=c}}function yn(e){var t=[],r=$t;return e||(e={}),e.Application="SheetJS",t[t.length]=dt,t[t.length]=$t("Properties",null,{xmlns:Qt,"xmlns:vt":or}),wn.forEach((function(a){if(void 0!==e[a[1]]){var n;switch(a[2]){case"string":n=At(String(e[a[1]]));break;case"bool":n=e[a[1]]?"true":"false"}void 0!==n&&(t[t.length]=r(a[0],n))}})),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map((function(e){return"<vt:lpstr>"+At(e)+"</vt:lpstr>"})).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var Sn=/<[^>]+>[^<]*/g;function kn(e){var t=[dt,$t("Properties",null,{xmlns:Zt,"xmlns:vt":or})];if(!e)return t.join("");var r=1;return Fe(e).forEach((function(a){++r,t[t.length]=$t("property",function(e,t){switch(typeof e){case"string":var r=$t("vt:lpwstr",At(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return $t((0|e)==e?"vt:i4":"vt:r8",At(String(e)));case"boolean":return $t("vt:bool",e?"true":"false")}if(e instanceof Date)return $t("vt:filetime",Yt(e));throw new Error("Unable to serialize "+e)}(e[a],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:At(a)})})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var _n,An={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function xn(e,t,r){_n||(_n=Pe(An)),e[t=_n[t]||t]=r}function Cn(e){var t=e.read_shift(4),r=e.read_shift(4);return new Date(1e3*(r/1e7*Math.pow(2,32)+t/1e7-11644473600)).toISOString().replace(/\.000/,"")}function On(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function Rn(e,t,r){var a=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(a.length+1&3)&3),a}function In(e,t,r){return 31===t?Rn(e):On(e,0,r)}function Nn(e,t,r){return In(e,t,!1===r?0:4)}function Fn(e){var t=e.l,r=Ln(e,81);return 0==e[e.l]&&0==e[e.l+1]&&e.l-t&2&&(e.l+=2),[r,Ln(e,3)]}function Dn(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,1200===t?"utf16le":"utf8").replace(R,"").replace(I,"!"),1200===t&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),a}function Pn(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(3&t)>0&&(e.l+=4-(3&t)&3),r}function Ln(e,t,r){var a,n=e.read_shift(2),s=r||{};if(e.l+=2,12!==t&&n!==t&&-1===Ya.indexOf(t)&&(4126!=(65534&t)||4126!=(65534&n)))throw new Error("Expected type "+t+" saw "+n);switch(12===t?n:t){case 2:return a=e.read_shift(2,"i"),s.raw||(e.l+=2),a;case 3:return a=e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return a=e.read_shift(4);case 30:return On(e,0,4).replace(R,"");case 31:return Rn(e);case 64:return Cn(e);case 65:return Pn(e);case 71:return function(e){var t={};return t.Size=e.read_shift(4),e.l+=t.Size+3-(t.Size-1)%4,t}(e);case 80:return Nn(e,n,!s.raw).replace(R,"");case 81:return function(e,t){if(!t)throw new Error("VtUnalignedString must have positive length");return In(e,t,0)}(e,n).replace(R,"");case 4108:return function(e){for(var t=e.read_shift(4),r=[],a=0;a<t/2;++a)r.push(Fn(e));return r}(e);case 4126:case 4127:return 4127==n?function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(R,""),e.l-n&2&&(e.l+=2)}return r}(e):function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(R,"");return r}(e);default:throw new Error("TypedPropertyValue unrecognized type "+t+" "+n)}}function Mn(e,t){var r=Zr(4),a=Zr(4);switch(r.write_shift(4,80==e?31:e),e){case 3:a.write_shift(-4,t);break;case 5:(a=Zr(8)).write_shift(8,t,"f");break;case 11:a.write_shift(4,t?1:0);break;case 64:a=function(e){var t=("string"==typeof e?new Date(Date.parse(e)):e).getTime()/1e3+11644473600,r=t%Math.pow(2,32),a=(t-r)/Math.pow(2,32);a*=1e7;var n=(r*=1e7)/Math.pow(2,32)|0;n>0&&(r%=Math.pow(2,32),a+=n);var s=Zr(8);return s.write_shift(4,r),s.write_shift(4,a),s}(t);break;case 31:case 80:for((a=Zr(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),a.write_shift(0,t,"dbcs");a.l!=a.length;)a.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return O([r,a])}function Un(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,o=0,c=-1,f={};for(i=0;i!=n;++i){var h=e.read_shift(4),u=e.read_shift(4);s[i]=[h,u+r]}s.sort((function(e,t){return e[1]-t[1]}));var d={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var p=!0;if(i>0&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,p=!1);break;case 80:case 4108:e.l<=s[i][1]&&(e.l=s[i][1],p=!1)}if((!t||0==i)&&e.l<=s[i][1]&&(p=!1,e.l=s[i][1]),p)throw new Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){var m=t[s[i][0]];if(d[m.n]=Ln(e,m.t,{raw:!0}),"version"===m.p&&(d[m.n]=String(d[m.n]>>16)+"."+("0000"+String(65535&d[m.n])).slice(-4)),"CodePage"==m.n)switch(d[m.n]){case 0:d[m.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:l(o=d[m.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+d[m.n])}}else if(1===s[i][0]){if(o=d.CodePage=Ln(e,2),l(o),-1!==c){var v=e.l;e.l=s[c][1],f=Dn(e,o),e.l=v}}else if(0===s[i][0]){if(0===o){c=i,e.l=s[i+1][1];continue}f=Dn(e,o)}else{var g,b=f[s[i][0]];switch(e[e.l]){case 65:e.l+=4,g=Pn(e);break;case 30:case 31:e.l+=4,g=Nn(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,g=e.read_shift(4,"i");break;case 19:e.l+=4,g=e.read_shift(4);break;case 5:e.l+=4,g=e.read_shift(8,"f");break;case 11:e.l+=4,g=jn(e,4);break;case 64:e.l+=4,g=$e(Cn(e));break;default:throw new Error("unparsed value: "+e[e.l])}d[b]=g}}return e.l=r+a,d}var Bn=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function Wn(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return-1}function Hn(e,t,r){var a=Zr(8),n=[],s=[],i=8,o=0,c=Zr(8),l=Zr(8);if(c.write_shift(4,2),c.write_shift(4,1200),l.write_shift(4,1),s.push(c),n.push(l),i+=8+c.length,!t){(l=Zr(8)).write_shift(4,0),n.unshift(l);var f=[Zr(4)];for(f[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];for((c=Zr(8+2*(h.length+1)+(h.length%2?0:2))).write_shift(4,o+2),c.write_shift(4,h.length+1),c.write_shift(0,h,"dbcs");c.l!=c.length;)c.write_shift(1,0);f.push(c)}c=O(f),s.unshift(c),i+=8+c.length}for(o=0;o<e.length;++o)if((!t||t[e[o][0]])&&!(Bn.indexOf(e[o][0])>-1||Tn.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(t){var p=r[d=+t[e[o][0]]];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(+m[0]<<16)+(+m[1]||0)}c=Mn(p.t,u)}else{var v=Wn(u);-1==v&&(v=31,u=String(u)),c=Mn(v,u)}s.push(c),(l=Zr(8)).write_shift(4,t?d:2+o),n.push(l),i+=8+c.length}var g=8*(s.length+1);for(o=0;o<s.length;++o)n[o].write_shift(4,g),g+=s[o].length;return a.write_shift(4,i),a.write_shift(4,s.length),O([a].concat(n).concat(s))}function zn(e,t,r){var a=e.content;if(!a)return{};Jr(a,0);var n,s,i,o,c=0;a.chk("feff","Byte Order: "),a.read_shift(2);var l=a.read_shift(4),f=a.read_shift(16);if(f!==Ce.utils.consts.HEADER_CLSID&&f!==r)throw new Error("Bad PropertySet CLSID "+f);if(1!==(n=a.read_shift(4))&&2!==n)throw new Error("Unrecognized #Sets: "+n);if(s=a.read_shift(16),o=a.read_shift(4),1===n&&o!==a.l)throw new Error("Length mismatch: "+o+" !== "+a.l);2===n&&(i=a.read_shift(16),c=a.read_shift(4));var h,u=Un(a,t),d={SystemIdentifier:l};for(var p in u)d[p]=u[p];if(d.FMTID=s,1===n)return d;if(c-a.l==2&&(a.l+=2),a.l!==c)throw new Error("Length mismatch 2: "+a.l+" !== "+c);try{h=Un(a,null)}catch(m){}for(p in h)d[p]=h[p];return d.FMTID=[s,i],d}function Vn(e,t,r,a,n,s){var i=Zr(n?68:48),o=[i];i.write_shift(2,65534),i.write_shift(2,0),i.write_shift(4,842412599),i.write_shift(16,Ce.utils.consts.HEADER_CLSID,"hex"),i.write_shift(4,n?2:1),i.write_shift(16,t,"hex"),i.write_shift(4,n?68:48);var c=Hn(e,r,a);if(o.push(c),n){var l=Hn(n,null,null);i.write_shift(16,s,"hex"),i.write_shift(4,68+c.length),o.push(l)}return O(o)}function Gn(e,t){return e.read_shift(t),null}function jn(e,t){return 1===e.read_shift(t)}function Xn(e,t){return t||(t=Zr(2)),t.write_shift(2,+!!e),t}function $n(e){return e.read_shift(2,"u")}function Yn(e,t){return t||(t=Zr(2)),t.write_shift(2,e),t}function Kn(e,t){return function(e,t,r){for(var a=[],n=e.l+t;e.l<n;)a.push(r(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}(e,t,$n)}function Jn(e,t,r){return r||(r=Zr(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,"e"==t?1:0),r}function qn(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),s="sbcs-cont",i=n;(r&&r.biff>=8&&(n=1200),r&&8!=r.biff)?12==r.biff&&(s="wstr"):e.read_shift(1)&&(s="dbcs-cont");r.biff>=2&&r.biff<=5&&(s="cpstr");var o=a?e.read_shift(a,s):"";return n=i,o}function Zn(e){var t=n;n=1200;var r,a=e.read_shift(2),s=e.read_shift(1),i=4&s,o=8&s,c=1+(1&s),l=0,f={};o&&(l=e.read_shift(2)),i&&(r=e.read_shift(4));var h=2==c?"dbcs-cont":"sbcs-cont",u=0===a?"":e.read_shift(a,h);return o&&(e.l+=4*l),i&&(e.l+=r),f.t=u,o||(f.raw="<t>"+f.t+"</t>",f.r=f.t),n=t,f}function Qn(e){var t=e.t||"",r=Zr(3);r.write_shift(2,t.length),r.write_shift(1,1);var a=Zr(2*t.length);return a.write_shift(2*t.length,t,"utf16le"),O([r,a])}function es(e,t,r){if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function ts(e,t,r){var a=e.read_shift(r&&2==r.biff?1:2);return 0===a?(e.l++,""):es(e,a,r)}function rs(e,t,r){if(r.biff>5)return ts(e,0,r);var a=e.read_shift(1);return 0===a?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function as(e,t,r){return r||(r=Zr(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function ns(e,t){var r=e.read_shift(16);switch(16,r){case"e0c9ea79f9bace118c8200aa004ba90b":return function(e){var t=e.read_shift(4),r=e.l,a=!1;t>24&&(e.l+=t-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(a=!0),e.l=r);var n=e.read_shift((a?t-24:t)>>1,"utf16le").replace(R,"");return a&&(e.l+=24),n}(e);case"0303000000000000c000000000000046":return function(e){for(var t=e.read_shift(2),r="";t-- >0;)r+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw new Error("Bad FileMoniker");if(0===e.read_shift(4))return r+a.replace(/\\/g,"/");var n=e.read_shift(4);if(3!=e.read_shift(2))throw new Error("Bad FileMoniker");return r+e.read_shift(n>>1,"utf16le").replace(R,"")}(e);default:throw new Error("Unsupported Moniker "+r)}}function ss(e){var t=e.read_shift(4);return t>0?e.read_shift(t,"utf16le").replace(R,""):""}function is(e,t){t||(t=Zr(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function os(e){var t=Zr(512),r=0,a=e.Target;"file://"==a.slice(0,7)&&(a=a.slice(7));var n=a.indexOf("#"),s=n>-1?31:23;switch(a.charAt(0)){case"#":s=28;break;case".":s&=-3}t.write_shift(4,2),t.write_shift(4,s);var i=[8,6815827,6619237,4849780,83];for(r=0;r<i.length;++r)t.write_shift(4,i[r]);if(28==s)is(a=a.slice(1),t);else if(2&s){for(i="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<i.length;++r)t.write_shift(1,parseInt(i[r],16));var o=n>-1?a.slice(0,n):a;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&s&&is(n>-1?a.slice(n+1):"",t)}else{for(i="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<i.length;++r)t.write_shift(1,parseInt(i[r],16));for(var c=0;"../"==a.slice(3*c,3*c+3)||"..\\"==a.slice(3*c,3*c+3);)++c;for(t.write_shift(2,c),t.write_shift(4,a.length-3*c+1),r=0;r<a.length-3*c;++r)t.write_shift(1,255&a.charCodeAt(r+3*c));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function cs(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function ls(e,t){var r=cs(e);return r[3]=0,r}function fs(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function hs(e,t,r,a){return a||(a=Zr(6)),a.write_shift(2,e),a.write_shift(2,t),a.write_shift(2,r||0),a}function us(e,t,r){var a=r.biff>8?4:2;return[e.read_shift(a),e.read_shift(a,"i"),e.read_shift(a,"i")]}function ds(e){return[e.read_shift(2),Ua(e)]}function ps(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function ms(e,t){return t||(t=Zr(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function vs(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(1),r:t},e:{c:e.read_shift(1),r:r}}}var gs=vs;function bs(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function ws(e){e.l+=2,e.l+=e.read_shift(2)}var Ts={0:ws,4:ws,5:ws,6:ws,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:ws,9:ws,10:ws,11:ws,12:ws,13:function(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t},14:ws,15:ws,16:ws,17:ws,18:ws,19:ws,20:ws,21:bs};function Es(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),(t-=2)>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function ys(e,t,r){var a=1536,n=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;default:throw new Error("unsupported BIFF version")}var s=Zr(n);return s.write_shift(2,a),s.write_shift(2,t),n>4&&s.write_shift(2,29282),n>6&&s.write_shift(2,1997),n>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function Ss(e,t){var r=!t||t.biff>=8?2:1,a=Zr(8+r*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),t.biff>=8&&a.write_shift(1,1),a.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}function ks(e,t,r){var a=0;r&&2==r.biff||(a=e.read_shift(2));var n=e.read_shift(2);return r&&2==r.biff&&(a=1-(n>>15),n&=32767),[{Unsynced:1&a,DyZero:(2&a)>>1,ExAsc:(4&a)>>2,ExDsc:(8&a)>>3},n]}function _s(e,t,r,a){var n=r&&5==r.biff;a||(a=Zr(n?3+t.length:5+2*t.length)),a.write_shift(2,e),a.write_shift(n?1:2,t.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*t.length,t,n?"sbcs":"utf16le");var s=a.length>a.l?a.slice(0,a.l):a;return null==s.l&&(s.l=s.length),s}var As=rs;function xs(e,t,r){var a=e.l+t,n=8!=r.biff&&r.biff?2:4,s=e.read_shift(n),i=e.read_shift(n),o=e.read_shift(2),c=e.read_shift(2);return e.l=a,{s:{r:s,c:o},e:{r:i,c:c}}}function Cs(e,t,r,a){var n=r&&5==r.biff;a||(a=Zr(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,t<<4));var s=0;return e.numFmtId>0&&n&&(s|=1024),a.write_shift(4,s),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function Os(e,t,r){var a=fs(e);2!=r.biff&&9!=t||++e.l;var n=function(e){var t=e.read_shift(1);return 1===e.read_shift(1)?t:1===t}(e);return a.val=n,a.t=!0===n||!1===n?"b":"e",a}var Rs=function(e,t,r){return 0===t?"":rs(e,0,r)};function Is(e,t,r){var a,n=e.read_shift(2),s={fBuiltIn:1&n,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return 14849===r.sbcch&&(a=function(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=qn(e,0,r),s=e.read_shift(2);if(s!==(a-=e.l))throw new Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}(e,t-2,r)),s.body=a||e.read_shift(t-2),"string"===typeof a&&(s.Name=a),s}var Ns=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function Fs(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(r&&2==r.biff?1:2),c=0;(!r||r.biff>=5)&&(5!=r.biff&&(e.l+=2),c=e.read_shift(2),5==r.biff&&(e.l+=2),e.l+=4);var l=es(e,i,r);32&n&&(l=Ns[l.charCodeAt(0)]);var f=a-e.l;r&&2==r.biff&&--f;var h=a!=e.l&&0!==o&&f>0?function(e,t,r,a){var n,s=e.l+t,i=$o(e,a,r);s!==e.l&&(n=Xo(e,s-e.l,i,r));return[i,n]}(e,f,r,o):[];return{chKey:s,Name:l,itab:c,rgce:h}}function Ds(e,t,r){if(r.biff<8)return function(e,t,r){3==e[e.l+1]&&e[e.l]++;var a=qn(e,0,r);return 3==a.charCodeAt(0)?a.slice(1):a}(e,0,r);for(var a=[],n=e.l+t,s=e.read_shift(r.biff>8?4:2);0!==s--;)a.push(us(e,r.biff,r));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function Ps(e,t,r){var a=gs(e,6);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,Qo(e,t,r)]}var Ls={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function Ms(e){var t=Zr(24),r=fa(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)t.write_shift(1,parseInt(a[n],16));return O([t,os(e[1])])}function Us(e){var t=e[1].Tooltip,r=Zr(10+2*(t.length+1));r.write_shift(2,2048);var a=fa(e[0]);r.write_shift(2,a.r),r.write_shift(2,a.r),r.write_shift(2,a.c),r.write_shift(2,a.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}function Bs(e,t,r){if(!r.cellStyles)return qr(e,t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),o=e.read_shift(a),c=e.read_shift(2);2==a&&(e.l+=2);var l={s:n,e:s,w:i,ixfe:o,flags:c};return(r.biff>=5||!r.biff)&&(l.level=c>>8&7),l}var Ws=fs,Hs=Kn,zs=ts;var Vs=[2,3,48,49,131,139,140,245],Gs=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Pe({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var a=r||{};a.dateNF||(a.dateNF="yyyymmdd");var n=wa(function(t,r){var a=[],n=S(1);switch(r.type){case"base64":n=_(T(t));break;case"binary":n=_(t);break;case"buffer":case"array":n=t}Jr(n,0);var s=n.read_shift(1),i=!!(136&s),o=!1,c=!1;switch(s){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:o=!0,i=!0;break;case 140:c=!0;break;default:throw new Error("DBF Unsupported Version: "+s.toString(16))}var l=0,f=521;2==s&&(l=n.read_shift(2)),n.l+=3,2!=s&&(l=n.read_shift(4)),l>1048576&&(l=1e6),2!=s&&(f=n.read_shift(2));var h=n.read_shift(2),u=r.codepage||1252;2!=s&&(n.l+=16,n.read_shift(1),0!==n[n.l]&&(u=e[n[n.l]]),n.l+=1,n.l+=2),c&&(n.l+=36);for(var p=[],m={},v=Math.min(n.length,2==s?521:f-10-(o?264:0)),g=c?32:11;n.l<v&&13!=n[n.l];)switch((m={}).name=d.utils.decode(u,n.slice(n.l,n.l+g)).replace(/[\u0000\r\n].*$/g,""),n.l+=g,m.type=String.fromCharCode(n.read_shift(1)),2==s||c||(m.offset=n.read_shift(4)),m.len=n.read_shift(1),2==s&&(m.offset=n.read_shift(2)),m.dec=n.read_shift(1),m.name.length&&p.push(m),2!=s&&(n.l+=c?13:14),m.type){case"B":o&&8==m.len||!r.WTF||console.log("Skipping "+m.name+":"+m.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+m.type)}if(13!==n[n.l]&&(n.l=f-1),13!==n.read_shift(1))throw new Error("DBF Terminator not found "+n.l+" "+n[n.l]);n.l=f;var b=0,w=0;for(a[0]=[],w=0;w!=p.length;++w)a[0][w]=p[w].name;for(;l-- >0;)if(42!==n[n.l])for(++n.l,a[++b]=[],w=0,w=0;w!=p.length;++w){var E=n.slice(n.l,n.l+p[w].len);n.l+=p[w].len,Jr(E,0);var y=d.utils.decode(u,E);switch(p[w].type){case"C":y.trim().length&&(a[b][w]=y.replace(/\s+$/,""));break;case"D":8===y.length?a[b][w]=new Date(+y.slice(0,4),+y.slice(4,6)-1,+y.slice(6,8)):a[b][w]=y;break;case"F":a[b][w]=parseFloat(y.trim());break;case"+":case"I":a[b][w]=c?2147483648^E.read_shift(-4,"i"):E.read_shift(4,"i");break;case"L":switch(y.trim().toUpperCase()){case"Y":case"T":a[b][w]=!0;break;case"N":case"F":a[b][w]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+y+"|")}break;case"M":if(!i)throw new Error("DBF Unexpected MEMO for type "+s.toString(16));a[b][w]="##MEMO##"+(c?parseInt(y.trim(),10):E.read_shift(4));break;case"N":(y=y.replace(/\u0000/g,"").trim())&&"."!=y&&(a[b][w]=+y||0);break;case"@":a[b][w]=new Date(E.read_shift(-8,"f")-621356832e5);break;case"T":a[b][w]=new Date(864e5*(E.read_shift(4)-2440588)+E.read_shift(4));break;case"Y":a[b][w]=E.read_shift(4,"i")/1e4+E.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":a[b][w]=-E.read_shift(-8,"f");break;case"B":if(o&&8==p[w].len){a[b][w]=E.read_shift(8,"f");break}case"G":case"P":E.l+=p[w].len;break;case"0":if("_NullFlags"===p[w].name)break;default:throw new Error("DBF Unsupported data type "+p[w].type)}}else n.l+=h;if(2!=s&&n.l<n.length&&26!=n[n.l++])throw new Error("DBF EOF Marker missing "+(n.l-1)+" of "+n.length+" "+n[n.l-1].toString(16));return r&&r.sheetRows&&(a=a.slice(0,r.sheetRows)),r.DBF=p,a}(t,a),a);return n["!cols"]=a.DBF.map((function(e){return{wch:e.len,DBF:e}})),delete a.DBF,n}var a={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return ga(r(e,t),t)}catch(a){if(t&&t.WTF)throw a}return{SheetNames:[],Sheets:{}}},to_sheet:r,from_sheet:function(e,r){var n=r||{};if(+n.codepage>=0&&l(+n.codepage),"string"==n.type)throw new Error("Cannot write DBF to JS string");var i=ea(),o=Ih(e,{header:1,raw:!0,cellDates:!0}),c=o[0],f=o.slice(1),h=e["!cols"]||[],u=0,d=0,p=0,m=1;for(u=0;u<c.length;++u)if(((h[u]||{}).DBF||{}).name)c[u]=h[u].DBF.name,++p;else if(null!=c[u]){if(++p,"number"===typeof c[u]&&(c[u]=c[u].toString(10)),"string"!==typeof c[u])throw new Error("DBF Invalid column name "+c[u]+" |"+typeof c[u]+"|");if(c.indexOf(c[u])!==u)for(d=0;d<1024;++d)if(-1==c.indexOf(c[u]+"_"+d)){c[u]+="_"+d;break}}var v=pa(e["!ref"]),g=[],b=[],w=[];for(u=0;u<=v.e.c-v.s.c;++u){var T="",E="",y=0,S=[];for(d=0;d<f.length;++d)null!=f[d][u]&&S.push(f[d][u]);if(0!=S.length&&null!=c[u]){for(d=0;d<S.length;++d){switch(typeof S[d]){case"number":E="B";break;case"string":default:E="C";break;case"boolean":E="L";break;case"object":E=S[d]instanceof Date?"D":"C"}y=Math.max(y,String(S[d]).length),T=T&&T!=E?"C":E}y>250&&(y=250),"C"==(E=((h[u]||{}).DBF||{}).type)&&h[u].DBF.len>y&&(y=h[u].DBF.len),"B"==T&&"N"==E&&(T="N",w[u]=h[u].DBF.dec,y=h[u].DBF.len),b[u]="C"==T||"N"==E?y:a[T]||0,m+=b[u],g[u]=T}else g[u]="?"}var k=i.next(32);for(k.write_shift(4,318902576),k.write_shift(4,f.length),k.write_shift(2,296+32*p),k.write_shift(2,m),u=0;u<4;++u)k.write_shift(4,0);for(k.write_shift(4,0|(+t[s]||3)<<8),u=0,d=0;u<c.length;++u)if(null!=c[u]){var _=i.next(32),A=(c[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);_.write_shift(1,A,"sbcs"),_.write_shift(1,"?"==g[u]?"C":g[u],"sbcs"),_.write_shift(4,d),_.write_shift(1,b[u]||a[g[u]]||0),_.write_shift(1,w[u]||0),_.write_shift(1,2),_.write_shift(4,0),_.write_shift(1,0),_.write_shift(4,0),_.write_shift(4,0),d+=b[u]||a[g[u]]||0}var x=i.next(264);for(x.write_shift(4,13),u=0;u<65;++u)x.write_shift(4,0);for(u=0;u<f.length;++u){var C=i.next(m);for(C.write_shift(1,0),d=0;d<c.length;++d)if(null!=c[d])switch(g[d]){case"L":C.write_shift(1,null==f[u][d]?63:f[u][d]?84:70);break;case"B":C.write_shift(8,f[u][d]||0,"f");break;case"N":var O="0";for("number"==typeof f[u][d]&&(O=f[u][d].toFixed(w[d]||0)),p=0;p<b[d]-O.length;++p)C.write_shift(1,32);C.write_shift(1,O,"sbcs");break;case"D":f[u][d]?(C.write_shift(4,("0000"+f[u][d].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(f[u][d].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+f[u][d].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var R=String(null!=f[u][d]?f[u][d]:"").slice(0,b[d]);for(C.write_shift(1,R,"sbcs"),p=0;p<b[d]-R.length;++p)C.write_shift(1,32)}}return i.next(1).write_shift(1,26),i.end()}}}(),js=function(){var e={AA:"\xc0",BA:"\xc1",CA:"\xc2",DA:195,HA:"\xc4",JA:197,AE:"\xc8",BE:"\xc9",CE:"\xca",HE:"\xcb",AI:"\xcc",BI:"\xcd",CI:"\xce",HI:"\xcf",AO:"\xd2",BO:"\xd3",CO:"\xd4",DO:213,HO:"\xd6",AU:"\xd9",BU:"\xda",CU:"\xdb",HU:"\xdc",Aa:"\xe0",Ba:"\xe1",Ca:"\xe2",Da:227,Ha:"\xe4",Ja:229,Ae:"\xe8",Be:"\xe9",Ce:"\xea",He:"\xeb",Ai:"\xec",Bi:"\xed",Ci:"\xee",Hi:"\xef",Ao:"\xf2",Bo:"\xf3",Co:"\xf4",Do:245,Ho:"\xf6",Au:"\xf9",Bu:"\xfa",Cu:"\xfb",Hu:"\xfc",KC:"\xc7",Kc:"\xe7",q:"\xe6",z:"\u0153",a:"\xc6",j:"\u0152",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1bN("+Fe(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var a=e[r];return"number"==typeof a?v(a):a},a=function(e,t,r){var a=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==a?e:v(a)};function n(e,n){var s,i=e.split(/[\n\r]+/),o=-1,c=-1,f=0,h=0,u=[],p=[],m=null,v={},g=[],b=[],w=[],T=0;for(+n.codepage>=0&&l(+n.codepage);f!==i.length;++f){T=0;var E,y=i[f].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(t,r),S=y.replace(/;;/g,"\0").split(";").map((function(e){return e.replace(/\u0000/g,";")})),k=S[0];if(y.length>0)switch(k){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==S[1].charAt(0)&&p.push(y.slice(3).replace(/;;/g,";"));break;case"C":var _=!1,A=!1,x=!1,C=!1,O=-1,R=-1;for(h=1;h<S.length;++h)switch(S[h].charAt(0)){case"A":case"G":break;case"X":c=parseInt(S[h].slice(1))-1,A=!0;break;case"Y":for(o=parseInt(S[h].slice(1))-1,A||(c=0),s=u.length;s<=o;++s)u[s]=[];break;case"K":'"'===(E=S[h].slice(1)).charAt(0)?E=E.slice(1,E.length-1):"TRUE"===E?E=!0:"FALSE"===E?E=!1:isNaN(qe(E))?isNaN(Qe(E).getDate())||(E=$e(E)):(E=qe(E),null!==m&&ve(m)&&(E=ze(E))),"undefined"!==typeof d&&"string"==typeof E&&"string"!=(n||{}).type&&(n||{}).codepage&&(E=d.utils.decode(n.codepage,E)),_=!0;break;case"E":C=!0;var I=Eo(S[h].slice(1),{r:o,c:c});u[o][c]=[u[o][c],I];break;case"S":x=!0,u[o][c]=[u[o][c],"S5S"];break;case"R":O=parseInt(S[h].slice(1))-1;break;case"C":R=parseInt(S[h].slice(1))-1;break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+y)}if(_&&(u[o][c]&&2==u[o][c].length?u[o][c][0]=E:u[o][c]=E,m=null),x){if(C)throw new Error("SYLK shared formula cannot have own formula");var N=O>-1&&u[O][R];if(!N||!N[1])throw new Error("SYLK shared formula cannot find base");u[o][c][1]=ko(N[1],{r:o-O,c:c-R})}break;case"F":var F=0;for(h=1;h<S.length;++h)switch(S[h].charAt(0)){case"X":c=parseInt(S[h].slice(1))-1,++F;break;case"Y":for(o=parseInt(S[h].slice(1))-1,s=u.length;s<=o;++s)u[s]=[];break;case"M":T=parseInt(S[h].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":m=p[parseInt(S[h].slice(1))];break;case"W":for(w=S[h].slice(1).split(" "),s=parseInt(w[0],10);s<=parseInt(w[1],10);++s)T=parseInt(w[2],10),b[s-1]=0===T?{hidden:!0}:{wch:T},Ii(b[s-1]);break;case"C":b[c=parseInt(S[h].slice(1))-1]||(b[c]={});break;case"R":g[o=parseInt(S[h].slice(1))-1]||(g[o]={}),T>0?(g[o].hpt=T,g[o].hpx=Fi(T)):0===T&&(g[o].hidden=!0);break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+y)}F<1&&(m=null);break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+y)}}return g.length>0&&(v["!rows"]=g),b.length>0&&(v["!cols"]=b),n&&n.sheetRows&&(u=u.slice(0,n.sheetRows)),[u,v]}function s(e,t){var r=function(e,t){switch(t.type){case"base64":return n(T(e),t);case"binary":return n(e,t);case"buffer":return n(E&&Buffer.isBuffer(e)?e.toString("binary"):x(e),t);case"array":return n(Ye(e),t)}throw new Error("Unrecognized type "+t.type)}(e,t),a=r[0],s=r[1],i=wa(a,t);return Fe(s).forEach((function(e){i[e]=s[e]})),i}function i(e,t,r,a){var n="C;Y"+(r+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0,e.f&&!e.F&&(n+=";E"+So(e.f,{r:r,c:a}));break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return n}return e["|"]=254,{to_workbook:function(e,t){return ga(s(e,t),t)},to_sheet:s,from_sheet:function(e,t){var r,a,n=["ID;PWXL;N;E"],s=[],o=pa(e["!ref"]),c=Array.isArray(e),l="\r\n";n.push("P;PGeneral"),n.push("F;P0;DG0G8;M255"),e["!cols"]&&(a=n,e["!cols"].forEach((function(e,t){var r="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?r+="0":("number"!=typeof e.width||e.wpx||(e.wpx=Ai(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=xi(e.wpx)),"number"==typeof e.wch&&(r+=Math.round(e.wch)))," "!=r.charAt(r.length-1)&&a.push(r)}))),e["!rows"]&&function(e,t){t.forEach((function(t,r){var a="F;";t.hidden?a+="M0;":t.hpt?a+="M"+20*t.hpt+";":t.hpx&&(a+="M"+20*Ni(t.hpx)+";"),a.length>2&&e.push(a+"R"+(r+1))}))}(n,e["!rows"]),n.push("B;Y"+(o.e.r-o.s.r+1)+";X"+(o.e.c-o.s.c+1)+";D"+[o.s.c,o.s.r,o.e.c,o.e.r].join(" "));for(var f=o.s.r;f<=o.e.r;++f)for(var h=o.s.c;h<=o.e.c;++h){var u=ha({r:f,c:h});(r=c?(e[f]||[])[h]:e[u])&&(null!=r.v||r.f&&!r.F)&&s.push(i(r,0,f,h))}return n.join(l)+l+s.join(l)+l+"E"+l}}}(),Xs=function(){function e(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s)if("BOT"!==r[s].trim()){if(!(a<0)){for(var o=r[s].trim().split(","),c=o[0],l=o[1],f=r[++s]||"";1&(f.match(/["]/g)||[]).length&&s<r.length-1;)f+="\n"+r[++s];switch(f=f.trim(),+c){case-1:if("BOT"===f){i[++a]=[],n=0;continue}if("EOD"!==f)throw new Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[a][n]=!0:"FALSE"===f?i[a][n]=!1:isNaN(qe(l))?isNaN(Qe(l).getDate())?i[a][n]=l:i[a][n]=$e(l):i[a][n]=qe(l),++n;break;case 1:(f=(f=f.slice(1,f.length-1)).replace(/""/g,'"'))&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),i[a][n++]=""!==f?f:null}if("EOD"===f)break}}else i[++a]=[],n=0;return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}function t(t,r){return wa(function(t,r){switch(r.type){case"base64":return e(T(t),r);case"binary":return e(t,r);case"buffer":return e(E&&Buffer.isBuffer(t)?t.toString("binary"):x(t),r);case"array":return e(Ye(t),r)}throw new Error("Unrecognized type "+r.type)}(t,r),r)}return{to_workbook:function(e,r){return ga(t(e,r),r)},to_sheet:t,from_sheet:function(){var e=function(e,t,r,a,n){e.push(t),e.push(r+","+a),e.push('"'+n.replace(/"/g,'""')+'"')},t=function(e,t,r,a){e.push(t+","+r),e.push(1==t?'"'+a.replace(/"/g,'""')+'"':a)};return function(r){var a,n=[],s=pa(r["!ref"]),i=Array.isArray(r);e(n,"TABLE",0,1,"sheetjs"),e(n,"VECTORS",0,s.e.r-s.s.r+1,""),e(n,"TUPLES",0,s.e.c-s.s.c+1,""),e(n,"DATA",0,0,"");for(var o=s.s.r;o<=s.e.r;++o){t(n,-1,0,"BOT");for(var c=s.s.c;c<=s.e.c;++c){var l=ha({r:o,c:c});if(a=i?(r[o]||[])[c]:r[l])switch(a.t){case"n":var f=a.w;f||null==a.v||(f=a.v),null==f?a.f&&!a.F?t(n,1,0,"="+a.f):t(n,1,0,""):t(n,0,f,"V");break;case"b":t(n,0,a.v?1:0,a.v?"TRUE":"FALSE");break;case"s":t(n,1,0,isNaN(a.v)?a.v:'="'+a.v+'"');break;case"d":a.w||(a.w=Te(a.z||H[14],Ue($e(a.v)))),t(n,0,a.w,"V");break;default:t(n,1,0,"")}else t(n,1,0,"")}}t(n,-1,0,"EOD");return n.join("\r\n")}}()}}(),$s=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return wa(function(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){var o=r[s].trim().split(":");if("cell"===o[0]){var c=fa(o[1]);if(i.length<=c.r)for(a=i.length;a<=c.r;++a)i[a]||(i[a]=[]);switch(a=c.r,n=c.c,o[2]){case"t":i[a][n]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[a][n]=+o[3];break;case"vtf":var l=o[o.length-1];case"vtc":"nl"===o[3]?i[a][n]=!!+o[4]:i[a][n]=+o[4],"vtf"==o[2]&&(i[a][n]=[i[a][n],l])}}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}(e,t),t)}var r=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),a=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",n=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),s="--SocialCalcSpreadsheetControlSave--";function i(t){if(!t||!t["!ref"])return"";for(var r,a=[],n=[],s="",i=ua(t["!ref"]),o=Array.isArray(t),c=i.s.r;c<=i.e.r;++c)for(var l=i.s.c;l<=i.e.c;++l)if(s=ha({r:c,c:l}),(r=o?(t[c]||[])[l]:t[s])&&null!=r.v&&"z"!==r.t){switch(n=["cell",s,"t"],r.t){case"s":case"str":n.push(e(r.v));break;case"n":r.f?(n[2]="vtf",n[3]="n",n[4]=r.v,n[5]=e(r.f)):(n[2]="v",n[3]=r.v);break;case"b":n[2]="vt"+(r.f?"f":"c"),n[3]="nl",n[4]=r.v?"1":"0",n[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var f=Ue($e(r.v));n[2]="vtc",n[3]="nd",n[4]=""+f,n[5]=r.w||Te(r.z||H[14],f);break;case"e":continue}a.push(n.join(":"))}return a.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),a.push("valueformat:1:text-wiki"),a.join("\n")}return{to_workbook:function(e,r){return ga(t(e,r),r)},to_sheet:t,from_sheet:function(e){return[r,a,n,a,i(e),s].join("\n")}}}(),Ys=function(){function e(e,t,r,a,n){n.raw?t[r][a]=e:""===e||("TRUE"===e?t[r][a]=!0:"FALSE"===e?t[r][a]=!1:isNaN(qe(e))?isNaN(Qe(e).getDate())?t[r][a]=e:t[r][a]=$e(e):t[r][a]=qe(e))}var t={44:",",9:"\t",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function a(e){for(var a={},n=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?n=!n:!n&&i in t&&(a[i]=(a[i]||0)+1);for(s in i=[],a)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);if(!i.length)for(s in a=r)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);return i.sort((function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]})),t[i.pop()[1]]||44}function n(e,t){var r=t||{},n="";var s=r.dense?[]:{},i={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(n=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(n=e.charAt(4),e=e.slice(6)):n=a(e.slice(0,1024)):n=r&&r.FS?r.FS:a(e.slice(0,1024));var o=0,c=0,l=0,f=0,h=0,u=n.charCodeAt(0),d=!1,p=0,m=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var v=null!=r.dateNF?function(e){var t="number"==typeof e?H[e]:e;return t=t.replace(Ae,"(\\d+)"),new RegExp("^"+t+"$")}(r.dateNF):null;function g(){var t=e.slice(f,h),a={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)a.t="z";else if(r.raw)a.t="s",a.v=t;else if(0===t.trim().length)a.t="s",a.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(a.t="s",a.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(a.t="n",a.f=t.slice(1)):(a.t="s",a.v=t);else if("TRUE"==t)a.t="b",a.v=!0;else if("FALSE"==t)a.t="b",a.v=!1;else if(isNaN(l=qe(t)))if(!isNaN(Qe(t).getDate())||v&&t.match(v)){a.z=r.dateNF||H[14];var n=0;v&&t.match(v)&&(t=function(e,t,r){var a=-1,n=-1,s=-1,i=-1,o=-1,c=-1;(t.match(Ae)||[]).forEach((function(e,t){var l=parseInt(r[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":a=l;break;case"d":s=l;break;case"h":i=l;break;case"s":c=l;break;case"m":i>=0?o=l:n=l}})),c>=0&&-1==o&&n>=0&&(o=n,n=-1);var l=(""+(a>=0?a:(new Date).getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);7==l.length&&(l="0"+l),8==l.length&&(l="20"+l);var f=("00"+(i>=0?i:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2);return-1==i&&-1==o&&-1==c?l:-1==a&&-1==n&&-1==s?f:l+"T"+f}(0,r.dateNF,t.match(v)||[]),n=1),r.cellDates?(a.t="d",a.v=$e(t,n)):(a.t="n",a.v=Ue($e(t,n))),!1!==r.cellText&&(a.w=Te(a.z,a.v instanceof Date?Ue(a.v):a.v)),r.cellNF||delete a.z}else a.t="s",a.v=t;else a.t="n",!1!==r.cellText&&(a.w=t),a.v=l;if("z"==a.t||(r.dense?(s[o]||(s[o]=[]),s[o][c]=a):s[ha({c:c,r:o})]=a),f=h+1,m=e.charCodeAt(f),i.e.c<c&&(i.e.c=c),i.e.r<o&&(i.e.r=o),p==u)++c;else if(c=0,++o,r.sheetRows&&r.sheetRows<=o)return!0}e:for(;h<e.length;++h)switch(p=e.charCodeAt(h)){case 34:34===m&&(d=!d);break;case u:case 10:case 13:if(!d&&g())break e}return h-f>0&&g(),s["!ref"]=da(i),s}function s(t,r){return r&&r.PRN?r.FS||"sep="==t.slice(0,4)||t.indexOf("\t")>=0||t.indexOf(",")>=0||t.indexOf(";")>=0?n(t,r):wa(function(t,r){var a=r||{},n=[];if(!t||0===t.length)return n;for(var s=t.split(/[\r\n]/),i=s.length-1;i>=0&&0===s[i].length;)--i;for(var o=10,c=0,l=0;l<=i;++l)-1==(c=s[l].indexOf(" "))?c=s[l].length:c++,o=Math.max(o,c);for(l=0;l<=i;++l){n[l]=[];var f=0;for(e(s[l].slice(0,o).trim(),n,l,f,a),f=1;f<=(s[l].length-o)/10+1;++f)e(s[l].slice(o+10*(f-1),o+10*f).trim(),n,l,f,a)}return a.sheetRows&&(n=n.slice(0,a.sheetRows)),n}(t,r),r):n(t,r)}function i(e,t){var r="",a="string"==t.type?[0,0,0,0]:mh(e,t);switch(t.type){case"base64":r=T(e);break;case"binary":case"string":r=e;break;case"buffer":r=65001==t.codepage?e.toString("utf8"):t.codepage&&"undefined"!==typeof d?d.utils.decode(t.codepage,e):E&&Buffer.isBuffer(e)?e.toString("binary"):x(e);break;case"array":r=Ye(e);break;default:throw new Error("Unrecognized type "+t.type)}return 239==a[0]&&187==a[1]&&191==a[2]?r=Lt(r.slice(3)):"string"!=t.type&&"buffer"!=t.type&&65001==t.codepage?r=Lt(r):"binary"==t.type&&"undefined"!==typeof d&&t.codepage&&(r=d.utils.decode(t.codepage,d.utils.encode(28591,r))),"socialcalc:version:"==r.slice(0,19)?$s.to_sheet("string"==t.type?r:Lt(r),t):s(r,t)}return{to_workbook:function(e,t){return ga(i(e,t),t)},to_sheet:i,from_sheet:function(e){for(var t,r=[],a=pa(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){for(var i=[],o=a.s.c;o<=a.e.c;++o){var c=ha({r:s,c:o});if((t=n?(e[s]||[])[o]:e[c])&&null!=t.v){for(var l=(t.w||(va(t),t.w)||"").slice(0,10);l.length<10;)l+=" ";i.push(l+(0===o?" ":""))}else i.push("          ")}r.push(i.join(""))}return r.join("\n")}}}();var Ks=function(){function e(e,t,r){if(e){Jr(e,e.l||0);for(var a=r.Enum||b;e.l<e.length;){var n=e.read_shift(2),s=a[n]||a[65535],i=e.read_shift(2),o=e.l+i,c=s.f&&s.f(e,i,r);if(e.l=o,t(c,s,n))return}}}function t(t,r){if(!t)return t;var a=r||{};var n=a.dense?[]:{},s="Sheet1",i="",o=0,c={},l=[],f=[],h={s:{r:0,c:0},e:{r:0,c:0}},u=a.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw new Error("Unsupported Works 3 for Mac file");if(2==t[2])a.Enum=b,e(t,(function(e,t,r){switch(r){case 0:a.vers=e,e>=4096&&(a.qpro=!0);break;case 6:h=e;break;case 204:e&&(i=e);break;case 222:i=e;break;case 15:case 51:a.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&112==(112&e[2])&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=a.dateNF||H[14],a.cellDates&&(e[1].t="d",e[1].v=ze(e[1].v))),a.qpro&&e[3]>o&&(n["!ref"]=da(h),c[s]=n,l.push(s),n=a.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s=i||"Sheet"+(o+1),i="");var f=a.dense?(n[e[0].r]||[])[e[0].c]:n[ha(e[0])];if(f){f.t=e[1].t,f.v=e[1].v,null!=e[1].z&&(f.z=e[1].z),null!=e[1].f&&(f.f=e[1].f);break}a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[ha(e[0])]=e[1]}}),a);else{if(26!=t[2]&&14!=t[2])throw new Error("Unrecognized LOTUS BOF "+t[2]);a.Enum=w,14==t[2]&&(a.qpro=!0,t.l=0),e(t,(function(e,t,r){switch(r){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(n["!ref"]=da(h),c[s]=n,l.push(s),n=a.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s="Sheet"+(o+1)),u>0&&e[0].r>=u)break;a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[ha(e[0])]=e[1],h.e.c<e[0].c&&(h.e.c=e[0].c),h.e.r<e[0].r&&(h.e.r=e[0].r);break;case 27:e[14e3]&&(f[e[14e3][0]]=e[14e3][1]);break;case 1537:f[e[0]]=e[1],e[0]==o&&(s=e[1])}}),a)}if(n["!ref"]=da(h),c[i||s]=n,l.push(i||s),!f.length)return{SheetNames:l,Sheets:c};for(var d={},p=[],m=0;m<f.length;++m)c[l[m]]?(p.push(f[m]||l[m]),d[f[m]]=c[f[m]]||c[l[m]]):(p.push(f[m]),d[f[m]]={"!ref":"A1"});return{SheetNames:p,Sheets:d}}function r(e,t,r){var a=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(a[0].c=e.read_shift(1),a[3]=e.read_shift(1),a[0].r=e.read_shift(2),e.l+=2):(a[2]=e.read_shift(1),a[0].c=e.read_shift(2),a[0].r=e.read_shift(2)),a}function a(e,t,a){var n=e.l+t,s=r(e,0,a);if(s[1].t="s",20768==a.vers){e.l++;var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return a.qpro&&e.l++,s[1].v=e.read_shift(n-e.l,"cstr"),s}function n(e,t,r){var a=Zr(7+r.length);a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(1,39);for(var n=0;n<a.length;++n){var s=r.charCodeAt(n);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}function s(e,t,r){var a=Zr(7);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(2,r,"i"),a}function i(e,t,r){var a=Zr(13);return a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(8,r,"f"),a}function o(e,t,r){var a=32768&t;return t=(a?e:0)+((t&=-32769)>=8192?t-16384:t),(a?"":"$")+(r?la(t):oa(t))}var c={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},f=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function h(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function u(e,t,r,a){var n=Zr(6+a.length);n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),n.write_shift(1,39);for(var s=0;s<a.length;++s){var i=a.charCodeAt(s);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}function d(e,t){var r=h(e),a=e.read_shift(4),n=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===a&&3221225472===n?(r[1].t="e",r[1].v=15):0===a&&3489660928===n?(r[1].t="e",r[1].v=42):r[1].v=0,r;var i=32768&s;return s=(32767&s)-16446,r[1].v=(1-2*i)*(n*Math.pow(2,s+32)+a*Math.pow(2,s)),r}function p(e,t,r,a){var n=Zr(14);if(n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),0==a)return n.write_shift(4,0),n.write_shift(4,0),n.write_shift(2,65535),n;var s,i=0,o=0,c=0;return a<0&&(i=1,a=-a),o=0|Math.log2(a),0==(2147483648&(c=(a/=Math.pow(2,o-31))>>>0))&&(++o,c=(a/=2)>>>0),a-=c,c|=2147483648,c>>>=0,s=(a*=Math.pow(2,32))>>>0,n.write_shift(4,s),n.write_shift(4,c),o+=16383+(i?32768:0),n.write_shift(2,o),n}function m(e,t){var r=h(e),a=e.read_shift(8,"f");return r[1].v=a,r}function v(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}function g(e,t){var r=Zr(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);r[r.l++]=n>127?95:n}return r[r.l++]=0,r}var b={0:{n:"BOF",f:$n},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var a={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(a.s.c=e.read_shift(1),e.l++,a.s.r=e.read_shift(2),a.e.c=e.read_shift(1),e.l++,a.e.r=e.read_shift(2),a):(a.s.c=e.read_shift(2),a.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),a.e.c=e.read_shift(2),a.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==a.s.c&&(a.s.c=a.e.c=a.s.r=a.e.r=0),a)}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,a){var n=r(e,0,a);return n[1].v=e.read_shift(2,"i"),n}},14:{n:"NUMBER",f:function(e,t,a){var n=r(e,0,a);return n[1].v=e.read_shift(8,"f"),n}},15:{n:"LABEL",f:a},16:{n:"FORMULA",f:function(e,t,a){var n=e.l+t,s=r(e,0,a);if(s[1].v=e.read_shift(8,"f"),a.qpro)e.l=n;else{var i=e.read_shift(2);!function(e,t){Jr(e,0);var r=[],a=0,n="",s="",i="",l="";for(;e.l<e.length;){var h=e[e.l++];switch(h){case 0:r.push(e.read_shift(8,"f"));break;case 1:s=o(t[0].c,e.read_shift(2),!0),n=o(t[0].r,e.read_shift(2),!1),r.push(s+n);break;case 2:var u=o(t[0].c,e.read_shift(2),!0),d=o(t[0].r,e.read_shift(2),!1);s=o(t[0].c,e.read_shift(2),!0),n=o(t[0].r,e.read_shift(2),!1),r.push(u+d+":"+s+n);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var p="";h=e[e.l++];)p+=String.fromCharCode(h);r.push('"'+p.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:l=r.pop(),i=r.pop(),r.push(["AND","OR"][h-20]+"("+i+","+l+")");break;default:if(h<32&&f[h])l=r.pop(),i=r.pop(),r.push(i+f[h]+l);else{if(!c[h])return h<=7?console.error("WK1 invalid opcode "+h.toString(16)):h<=24?console.error("WK1 unsupported op "+h.toString(16)):h<=30?console.error("WK1 invalid opcode "+h.toString(16)):h<=115?console.error("WK1 unsupported function opcode "+h.toString(16)):console.error("WK1 unrecognized opcode "+h.toString(16));if(69==(a=c[h][1])&&(a=e[e.l++]),a>r.length)return void console.error("WK1 bad formula parse 0x"+h.toString(16)+":|"+r.join("|")+"|");var m=r.slice(-a);r.length-=a,r.push(c[h][0]+"("+m.join(",")+")")}}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")}(e.slice(e.l,e.l+i),s),e.l+=i}return s}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:a},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:v},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var a="";a.length<r;)a+=String.fromCharCode(e[e.l++]);return a}},65535:{n:""}},w={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=h(e);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:d},24:{n:"NUMBER18",f:function(e,t){var r=h(e);r[1].v=e.read_shift(2);var a=r[1].v>>1;if(1&r[1].v)switch(7&a){case 0:a=5e3*(a>>3);break;case 1:a=500*(a>>3);break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64}return r[1].v=a,r}},25:{n:"FORMULA19",f:function(e,t){var r=d(e);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},a=e.l+t;e.l<a;){var n=e.read_shift(2);if(14e3==n){for(r[n]=[0,""],r[n][0]=e.read_shift(2);e[e.l];)r[n][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=h(e),a=e.read_shift(4);return r[1].v=a>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:m},40:{n:"FORMULA28",f:function(e,t){var r=m(e);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:v},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var a=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[a,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r=t||{};if(+r.codepage>=0&&l(+r.codepage),"string"==r.type)throw new Error("Cannot write WK1 to JS string");var a=ea(),o=pa(e["!ref"]),c=Array.isArray(e),f=[];sf(a,0,function(e){var t=Zr(2);return t.write_shift(2,e),t}(1030)),sf(a,6,function(e){var t=Zr(8);return t.write_shift(2,e.s.c),t.write_shift(2,e.s.r),t.write_shift(2,e.e.c),t.write_shift(2,e.e.r),t}(o));for(var h=Math.min(o.e.r,8191),u=o.s.r;u<=h;++u)for(var d=oa(u),p=o.s.c;p<=o.e.c;++p){u===o.s.r&&(f[p]=la(p));var m=f[p]+d,v=c?(e[u]||[])[p]:e[m];if(v&&"z"!=v.t)if("n"==v.t)(0|v.v)==v.v&&v.v>=-32768&&v.v<=32767?sf(a,13,s(u,p,v.v)):sf(a,14,i(u,p,v.v));else sf(a,15,n(u,p,va(v).slice(0,239)))}return sf(a,1),a.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&l(+r.codepage),"string"==r.type)throw new Error("Cannot write WK3 to JS string");var a=ea();sf(a,0,function(e){var t=Zr(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,a=0,n=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],o=e.Sheets[i];if(o&&o["!ref"]){++n;var c=ua(o["!ref"]);r<c.e.r&&(r=c.e.r),a<c.e.c&&(a=c.e.c)}}r>8191&&(r=8191);return t.write_shift(2,r),t.write_shift(1,n),t.write_shift(1,a),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var n=0,s=0;n<e.SheetNames.length;++n)(e.Sheets[e.SheetNames[n]]||{})["!ref"]&&sf(a,27,g(e.SheetNames[n],s++));var i=0;for(n=0;n<e.SheetNames.length;++n){var o=e.Sheets[e.SheetNames[n]];if(o&&o["!ref"]){for(var c=pa(o["!ref"]),f=Array.isArray(o),h=[],d=Math.min(c.e.r,8191),m=c.s.r;m<=d;++m)for(var v=oa(m),b=c.s.c;b<=c.e.c;++b){m===c.s.r&&(h[b]=la(b));var w=h[b]+v,T=f?(o[m]||[])[b]:o[w];if(T&&"z"!=T.t)if("n"==T.t)sf(a,23,p(m,b,i,T.v));else sf(a,22,u(m,b,i,va(T).slice(0,239)))}++i}}return sf(a,1),a.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(_(T(e)),r);case"binary":return t(_(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}();var Js=function(){var e=Ut("t"),t=Ut("rPr");function r(r){var a=r.match(e);if(!a)return{t:"s",v:""};var n={t:"s",v:St(a[1])},s=r.match(t);return s&&(n.s=function(e){var t={},r=e.match(vt),a=0,n=!1;if(r)for(;a!=r.length;++a){var s=wt(r[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==s.val)break;t.cp=o[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=s.val;break;case"<sz":t.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting"}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if("0"==s.val)break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if("0"==s.val)break;case"<i>":case"<i/>":t.i=1;break;case"</i>":break;case"<color":s.rgb&&(t.color=s.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":t.family=s.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":t.valign=s.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":case"<scheme":case"<scheme>":case"<scheme/>":case"</scheme>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(47!==s[0].charCodeAt(1)&&!n)throw new Error("Unrecognized rich format "+s[0])}}return t}(s[1])),n}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(e){return e.replace(a,"").split(n).map(r).filter((function(e){return e.v}))}}(),qs=function(){var e=/(\r\n|\n)/g;function t(t){var r=[[],t.v,[]];return t.v?(t.s&&function(e,t,r){var a=[];e.u&&a.push("text-decoration: underline;"),e.uval&&a.push("text-underline-style:"+e.uval+";"),e.sz&&a.push("font-size:"+e.sz+"pt;"),e.outline&&a.push("text-effect: outline;"),e.shadow&&a.push("text-shadow: auto;"),t.push('<span style="'+a.join("")+'">'),e.b&&(t.push("<b>"),r.push("</b>")),e.i&&(t.push("<i>"),r.push("</i>")),e.strike&&(t.push("<s>"),r.push("</s>"));var n=e.valign||"";"superscript"==n||"super"==n?n="sup":"subscript"==n&&(n="sub"),""!=n&&(t.push("<"+n+">"),r.push("</"+n+">")),r.push("</span>")}(t.s,r[0],r[2]),r[0].join("")+r[1].replace(e,"<br/>")+r[2].join("")):""}return function(e){return e.map(t).join("")}}(),Zs=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Qs=/<(?:\w+:)?r>/,ei=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function ti(e,t){var r=!t||t.cellHTML,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=St(Lt(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=Lt(e),r&&(a.h=Ot(a.t))):e.match(Qs)&&(a.r=Lt(e),a.t=St(Lt((e.replace(ei,"").match(Zs)||[]).join("").replace(vt,""))),r&&(a.h=qs(Js(a.r)))),a):{t:""}}var ri=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,ai=/<(?:\w+:)?(?:si|sstItem)>/g,ni=/<\/(?:\w+:)?(?:si|sstItem)>/;var si=/^\s|\s$|[\t\n\r]/;function ii(e,t){if(!t.bookSST)return"";var r=[dt];r[r.length]=$t("sst",null,{xmlns:fr[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(null!=e[a]){var n=e[a],s="<si>";n.r?s+=n.r:(s+="<t",n.t||(n.t=""),n.t.match(si)&&(s+=' xml:space="preserve"'),s+=">"+At(n.t)+"</t>"),s+="</si>",r[r.length]=s}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}var oi=function(e,t){var r=!1;return null==t&&(r=!0,t=Zr(15+4*e.t.length)),t.write_shift(1,0),ya(e.t,t),r?t.slice(0,t.l):t};function ci(e){var t=ea();ta(t,159,function(e,t){return t||(t=Zr(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}(e));for(var r=0;r<e.length;++r)ta(t,19,oi(e[r]));return ta(t,160),t.end()}function li(e){if("undefined"!==typeof d)return d.utils.encode(s,e);for(var t=[],r=e.split(""),a=0;a<r.length;++a)t[a]=r[a].charCodeAt(0);return t}function fi(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function hi(e){for(var t=e.read_shift(4),r=e.l+t-4,a={},n=e.read_shift(4),s=[];n-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=s,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return a}function ui(e){var t=function(e){var t={};return e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=fi(e,4),t.U=fi(e,4),t.W=fi(e,4),t}(e);if(t.ename=e.read_shift(0,"8lpp4"),t.blksz=e.read_shift(4),t.cmode=e.read_shift(4),4!=e.read_shift(4))throw new Error("Bad !Primary record");return t}function di(e,t){var r=e.l+t,a={};a.Flags=63&e.read_shift(4),e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=36==a.Flags;break;case 26625:n=4==a.Flags;break;case 0:n=16==a.Flags||4==a.Flags||36==a.Flags;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}function pi(e,t){var r={},a=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,a),e.l=a,r}function mi(e){if(36!=(63&e.read_shift(4)))throw new Error("EncryptionInfo mismatch");var t=e.read_shift(4);return{t:"Std",h:di(e,t),v:pi(e,e.length-e.l)}}function vi(){throw new Error("File is password-protected: ECMA-376 Extensible")}function gi(e){var t=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),a={};return r.replace(vt,(function(e){var r=wt(e);switch(Tt(r[0])){case"<?xml":case"<encryption":case"</encryption>":case"</keyEncryptors>":case"</keyEncryptor>":break;case"<keyData":t.forEach((function(e){a[e]=r[e]}));break;case"<dataIntegrity":a.encryptedHmacKey=r.encryptedHmacKey,a.encryptedHmacValue=r.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":a.encs=[];break;case"<keyEncryptor":a.uri=r.uri;break;case"<encryptedKey":a.encs.push(r);break;default:throw r[0]}})),a}function bi(e){var t,r,a=0,n=li(e),s=n.length+1;for((t=S(s))[0]=n.length,r=1;r!=s;++r)t[r]=n[r-1];for(r=s-1;r>=0;--r)a=((0===(16384&a)?0:1)|a<<1&32767)^t[r];return 52811^a}var wi=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(e,t){return 255&((r=e^t)/2|128*r);var r};return function(n){for(var s,i,o,c=li(n),l=function(e){for(var a=t[e.length-1],n=104,s=e.length-1;s>=0;--s)for(var i=e[s],o=0;7!=o;++o)64&i&&(a^=r[n]),i*=2,--n;return a}(c),f=c.length,h=S(16),u=0;16!=u;++u)h[u]=0;for(1===(1&f)&&(s=l>>8,h[f]=a(e[0],s),--f,s=255&l,i=c[c.length-1],h[f]=a(i,s));f>0;)s=l>>8,h[--f]=a(c[f],s),s=255&l,h[--f]=a(c[f],s);for(f=15,o=15-c.length;o>0;)s=l>>8,h[f]=a(e[o],s),--o,s=255&l,h[--f]=a(c[f],s),--f,--o;return h}}(),Ti=function(e){var t=0,r=wi(e);return function(e){var a=function(e,t,r,a,n){var s,i;for(n||(n=t),a||(a=wi(e)),s=0;s!=t.length;++s)i=t[s],i=255&((i^=a[r])>>5|i<<3),n[s]=i,++r;return[n,r,a]}("",e,t,r);return t=a[1],a[0]}};function Ei(e,t,r){var a=r||{};return a.Info=e.read_shift(2),e.l-=2,1===a.Info?a.Data=function(e){var t={},r=t.EncryptionVersionInfo=fi(e,4);if(1!=r.Major||1!=r.Minor)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}(e):a.Data=function(e,t){var r={},a=r.EncryptionVersionInfo=fi(e,4);if(t-=4,2!=a.Minor)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);r.Flags=e.read_shift(4),t-=4;var n=e.read_shift(4);return t-=4,r.EncryptionHeader=di(e,n),t-=n,r.EncryptionVerifier=pi(e,t),r}(e,t),a}var yi=function(){function e(e,r){switch(r.type){case"base64":return t(T(e),r);case"binary":return t(e,r);case"buffer":return t(E&&Buffer.isBuffer(e)?e.toString("binary"):x(e),r);case"array":return t(Ye(e),r)}throw new Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},a=e.match(/\\trowd.*?\\row\b/g);if(!a.length)throw new Error("RTF missing table");var n={s:{c:0,r:0},e:{c:0,r:a.length-1}};return a.forEach((function(e,t){Array.isArray(r)&&(r[t]=[]);for(var a,s=/\\\w+\b/g,i=0,o=-1;a=s.exec(e);){if("\\cell"===a[0]){var c=e.slice(i,s.lastIndex-a[0].length);if(" "==c[0]&&(c=c.slice(1)),++o,c.length){var l={v:c,t:"s"};Array.isArray(r)?r[t][o]=l:r[ha({r:t,c:o})]=l}}i=s.lastIndex}o>n.e.c&&(n.e.c=o)})),r["!ref"]=da(n),r}return{to_workbook:function(t,r){return ga(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],a=pa(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){r.push("\\trowd\\trautofit1");for(var i=a.s.c;i<=a.e.c;++i)r.push("\\cellx"+(i+1));for(r.push("\\pard\\intbl"),i=a.s.c;i<=a.e.c;++i){var o=ha({r:s,c:i});(t=n?(e[s]||[])[i]:e[o])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(va(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function Si(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function ki(e,t){if(0===t)return e;var r=function(e){var t=e[0]/255,r=e[1]/255,a=e[2]/255,n=Math.max(t,r,a),s=Math.min(t,r,a),i=n-s;if(0===i)return[0,0,t];var o,c=0,l=n+s;switch(o=i/(l>1?2-l:l),n){case t:c=((r-a)/i+6)%6;break;case r:c=(a-t)/i+2;break;case a:c=(t-r)/i+4}return[c/6,o,l/2]}(function(e){var t=e.slice("#"===e[0]?1:0).slice(0,6);return[parseInt(t.slice(0,2),16),parseInt(t.slice(2,4),16),parseInt(t.slice(4,6),16)]}(e));return r[2]=t<0?r[2]*(1+t):1-(1-r[2])*(1-t),Si(function(e){var t,r=e[0],a=e[1],n=e[2],s=2*a*(n<.5?n:1-n),i=n-s/2,o=[i,i,i],c=6*r;if(0!==a)switch(0|c){case 0:case 6:t=s*c,o[0]+=s,o[1]+=t;break;case 1:t=s*(2-c),o[0]+=t,o[1]+=s;break;case 2:t=s*(c-2),o[1]+=s,o[2]+=t;break;case 3:t=s*(4-c),o[1]+=t,o[2]+=s;break;case 4:t=s*(c-4),o[2]+=s,o[0]+=t;break;case 5:t=s*(6-c),o[2]+=t,o[0]+=s}for(var l=0;3!=l;++l)o[l]=Math.round(255*o[l]);return o}(r))}var _i=6;function Ai(e){return Math.floor((e+Math.round(128/_i)/256)*_i)}function xi(e){return Math.floor((e-5)/_i*100+.5)/100}function Ci(e){return Math.round((e*_i+5)/_i*256)/256}function Oi(e){return Ci(xi(Ai(e)))}function Ri(e){var t=Math.abs(e-Oi(e)),r=_i;if(t>.005)for(_i=1;_i<15;++_i)Math.abs(e-Oi(e))<=t&&(t=Math.abs(e-Oi(e)),r=_i);_i=r}function Ii(e){e.width?(e.wpx=Ai(e.width),e.wch=xi(e.wpx),e.MDW=_i):e.wpx?(e.wch=xi(e.wpx),e.width=Ci(e.wch),e.MDW=_i):"number"==typeof e.wch&&(e.width=Ci(e.wch),e.wpx=Ai(e.width),e.MDW=_i),e.customWidth&&delete e.customWidth}function Ni(e){return 96*e/96}function Fi(e){return 96*e/96}var Di={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};var Pi=["numFmtId","fillId","fontId","borderId","xfId"],Li=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];var Mi=function(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,t=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,r=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,n=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(s,i,c){var l,f={};return s?((l=(s=s.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"")).match(e))&&function(e,t,r){t.NumberFmt=[];for(var a=Fe(H),n=0;n<a.length;++n)t.NumberFmt[a[n]]=H[a[n]];var s=e[0].match(vt);if(s)for(n=0;n<s.length;++n){var i=wt(s[n]);switch(Tt(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":case"</numFmt>":break;case"<numFmt":var o=St(Lt(i.formatCode)),c=parseInt(i.numFmtId,10);if(t.NumberFmt[c]=o,c>0){if(c>392){for(c=392;c>60&&null!=t.NumberFmt[c];--c);t.NumberFmt[c]=o}Ee(o,c)}break;default:if(r.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}(l,f,c),(l=s.match(a))&&function(e,t,r,a){t.Fonts=[];var n={},s=!1;(e[0].match(vt)||[]).forEach((function(e){var i=wt(e);switch(Tt(i[0])){case"<fonts":case"<fonts>":case"</fonts>":case"<font":case"<font>":case"<name/>":case"</name>":case"<sz/>":case"</sz>":case"<vertAlign/>":case"</vertAlign>":case"<family/>":case"</family>":case"<scheme/>":case"</scheme>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"</font>":case"<font/>":t.Fonts.push(n),n={};break;case"<name":i.val&&(n.name=Lt(i.val));break;case"<b":n.bold=i.val?It(i.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=i.val?It(i.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(i.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=i.val?It(i.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=i.val?It(i.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=i.val?It(i.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=i.val?It(i.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=i.val?It(i.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":i.val&&(n.sz=+i.val);break;case"<vertAlign":i.val&&(n.vertAlign=i.val);break;case"<family":i.val&&(n.family=parseInt(i.val,10));break;case"<scheme":i.val&&(n.scheme=i.val);break;case"<charset":if("1"==i.val)break;i.codepage=o[parseInt(i.val,10)];break;case"<color":if(n.color||(n.color={}),i.auto&&(n.color.auto=It(i.auto)),i.rgb)n.color.rgb=i.rgb.slice(-6);else if(i.indexed){n.color.index=parseInt(i.indexed,10);var c=en[n.color.index];81==n.color.index&&(c=en[1]),c||(c=en[1]),n.color.rgb=c[0].toString(16)+c[1].toString(16)+c[2].toString(16)}else i.theme&&(n.color.theme=parseInt(i.theme,10),i.tint&&(n.color.tint=parseFloat(i.tint)),i.theme&&r.themeElements&&r.themeElements.clrScheme&&(n.color.rgb=ki(r.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)));break;case"<AlternateContent":case"<ext":s=!0;break;case"</AlternateContent>":case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+i[0]+" in fonts")}}))}(l,f,i,c),(l=s.match(r))&&function(e,t,r,a){t.Fills=[];var n={},s=!1;(e[0].match(vt)||[]).forEach((function(e){var r=wt(e);switch(Tt(r[0])){case"<fills":case"<fills>":case"</fills>":case"</fill>":case"<gradientFill>":case"<patternFill/>":case"</patternFill>":case"<bgColor/>":case"</bgColor>":case"<fgColor/>":case"</fgColor>":case"<stop":case"<stop/>":case"</stop>":case"<color":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<fill>":case"<fill":case"<fill/>":n={},t.Fills.push(n);break;case"<gradientFill":case"</gradientFill>":t.Fills.push(n),n={};break;case"<patternFill":case"<patternFill>":r.patternType&&(n.patternType=r.patternType);break;case"<bgColor":n.bgColor||(n.bgColor={}),r.indexed&&(n.bgColor.indexed=parseInt(r.indexed,10)),r.theme&&(n.bgColor.theme=parseInt(r.theme,10)),r.tint&&(n.bgColor.tint=parseFloat(r.tint)),r.rgb&&(n.bgColor.rgb=r.rgb.slice(-6));break;case"<fgColor":n.fgColor||(n.fgColor={}),r.theme&&(n.fgColor.theme=parseInt(r.theme,10)),r.tint&&(n.fgColor.tint=parseFloat(r.tint)),null!=r.rgb&&(n.fgColor.rgb=r.rgb.slice(-6));break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+r[0]+" in fills")}}))}(l,f,0,c),(l=s.match(n))&&function(e,t,r,a){t.Borders=[];var n={},s=!1;(e[0].match(vt)||[]).forEach((function(e){var r=wt(e);switch(Tt(r[0])){case"<borders":case"<borders>":case"</borders>":case"</border>":case"<left/>":case"<left":case"<left>":case"</left>":case"<right/>":case"<right":case"<right>":case"</right>":case"<top/>":case"<top":case"<top>":case"</top>":case"<bottom/>":case"<bottom":case"<bottom>":case"</bottom>":case"<diagonal":case"<diagonal>":case"<diagonal/>":case"</diagonal>":case"<horizontal":case"<horizontal>":case"<horizontal/>":case"</horizontal>":case"<vertical":case"<vertical>":case"<vertical/>":case"</vertical>":case"<start":case"<start>":case"<start/>":case"</start>":case"<end":case"<end>":case"<end/>":case"</end>":case"<color":case"<color>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<border":case"<border>":case"<border/>":n={},r.diagonalUp&&(n.diagonalUp=It(r.diagonalUp)),r.diagonalDown&&(n.diagonalDown=It(r.diagonalDown)),t.Borders.push(n);break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+r[0]+" in borders")}}))}(l,f,0,c),(l=s.match(t))&&function(e,t,r){var a;t.CellXf=[];var n=!1;(e[0].match(vt)||[]).forEach((function(e){var s=wt(e),i=0;switch(Tt(s[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":case"</xf>":case"</alignment>":case"<protection":case"</protection>":case"<protection/>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<xf":case"<xf/>":for(delete(a=s)[0],i=0;i<Pi.length;++i)a[Pi[i]]&&(a[Pi[i]]=parseInt(a[Pi[i]],10));for(i=0;i<Li.length;++i)a[Li[i]]&&(a[Li[i]]=It(a[Li[i]]));if(t.NumberFmt&&a.numFmtId>392)for(i=392;i>60;--i)if(t.NumberFmt[a.numFmtId]==t.NumberFmt[i]){a.numFmtId=i;break}t.CellXf.push(a);break;case"<alignment":case"<alignment/>":var o={};s.vertical&&(o.vertical=s.vertical),s.horizontal&&(o.horizontal=s.horizontal),null!=s.textRotation&&(o.textRotation=s.textRotation),s.indent&&(o.indent=s.indent),s.wrapText&&(o.wrapText=It(s.wrapText)),a.alignment=o;break;case"<AlternateContent":case"<ext":n=!0;break;case"</AlternateContent>":case"</ext>":n=!1;break;default:if(r&&r.WTF&&!n)throw new Error("unrecognized "+s[0]+" in cellXfs")}}))}(l,f,c),f):f}}();function Ui(e,t){var r,a=[dt,$t("styleSheet",null,{xmlns:fr[0],"xmlns:vt":or})];return e.SSF&&null!=(r=function(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var a=r[0];a<=r[1];++a)null!=e[a]&&(t[t.length]=$t("numFmt",null,{numFmtId:a,formatCode:At(e[a])}))})),1===t.length?"":(t[t.length]="</numFmts>",t[0]=$t("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(e.SSF))&&(a[a.length]=r),a[a.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',a[a.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',a[a.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',a[a.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(r=function(e){var t=[];return t[t.length]=$t("cellXfs",null),e.forEach((function(e){t[t.length]=$t("xf",null,e)})),t[t.length]="</cellXfs>",2===t.length?"":(t[0]=$t("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(t.cellXfs))&&(a[a.length]=r),a[a.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',a[a.length]='<dxfs count="0"/>',a[a.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',a.length>2&&(a[a.length]="</styleSheet>",a[1]=a[1].replace("/>",">")),a.join("")}function Bi(e,t,r){r||(r=Zr(6+4*t.length)),r.write_shift(2,e),ya(t,r);var a=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),a}function Wi(e,t){t||(t=Zr(153)),t.write_shift(2,20*e.sz),function(e,t){t||(t=Zr(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);t.write_shift(1,r),t.write_shift(1,0)}(e,t),t.write_shift(2,e.bold?700:400);var r=0;"superscript"==e.vertAlign?r=1:"subscript"==e.vertAlign&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),ja(e.color,t);var a=0;return"major"==e.scheme&&(a=1),"minor"==e.scheme&&(a=2),t.write_shift(1,a),ya(e.name,t),t.length>t.l?t.slice(0,t.l):t}var Hi,zi=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Vi=qr;function Gi(e,t){t||(t=Zr(84)),Hi||(Hi=Pe(zi));var r=Hi[e.patternType];null==r&&(r=40),t.write_shift(4,r);var a=0;if(40!=r)for(ja({auto:1},t),ja({auto:1},t);a<12;++a)t.write_shift(4,0);else{for(;a<4;++a)t.write_shift(4,0);for(;a<12;++a)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function ji(e,t,r){r||(r=Zr(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);return r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function Xi(e,t){return t||(t=Zr(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var $i=qr;function Yi(e){var t;ta(e,613,Ta(1)),ta(e,46,(t||(t=Zr(51)),t.write_shift(1,0),Xi(0,t),Xi(0,t),Xi(0,t),Xi(0,t),Xi(0,t),t.length>t.l?t.slice(0,t.l):t)),ta(e,614)}function Ki(e){var t,r;ta(e,619,Ta(1)),ta(e,48,(t={xfId:0,builtinId:0,name:"Normal"},r||(r=Zr(52)),r.write_shift(4,t.xfId),r.write_shift(2,1),r.write_shift(1,+t.builtinId),r.write_shift(1,0),Da(t.name||"",r),r.length>r.l?r.slice(0,r.l):r)),ta(e,620)}function Ji(e){ta(e,508,function(e,t,r){var a=Zr(2052);return a.write_shift(4,e),Da(t,a),Da(r,a),a.length>a.l?a.slice(0,a.l):a}(0,"TableStyleMedium9","PivotStyleMedium4")),ta(e,509)}function qi(e,t){var r=ea();return ta(r,278),function(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach((function(e){for(var a=e[0];a<=e[1];++a)null!=t[a]&&++r})),0!=r&&(ta(e,615,Ta(r)),[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var a=r[0];a<=r[1];++a)null!=t[a]&&ta(e,44,Bi(a,t[a]))})),ta(e,616))}}(r,e.SSF),function(e){ta(e,611,Ta(1)),ta(e,43,Wi({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),ta(e,612)}(r),function(e){ta(e,603,Ta(2)),ta(e,45,Gi({patternType:"none"})),ta(e,45,Gi({patternType:"gray125"})),ta(e,604)}(r),Yi(r),function(e){ta(e,626,Ta(1)),ta(e,47,ji({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),ta(e,627)}(r),function(e,t){ta(e,617,Ta(t.length)),t.forEach((function(t){ta(e,47,ji(t,0))})),ta(e,618)}(r,t.cellXfs),Ki(r),function(e){ta(e,505,Ta(0)),ta(e,506)}(r),Ji(r),ta(r,279),r.end()}var Zi=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function Qi(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(vt)||[]).forEach((function(e){var n=wt(e);switch(n[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===n[0].charAt(1)?(t.themeElements.clrScheme[Zi.indexOf(n[0])]=a,a={}):a.name=n[0].slice(3,n[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+n[0]+" in clrScheme")}}))}function eo(){}function to(){}var ro=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,ao=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,no=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;var so=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function io(e,t){var r;e&&0!==e.length||(e=oo());var a={};if(!(r=e.match(so)))throw new Error("themeElements not found in theme");return function(e,t,r){var a;t.themeElements={},[["clrScheme",ro,Qi],["fontScheme",ao,eo],["fmtScheme",no,to]].forEach((function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,t,r)}))}(r[0],a,t),a.raw=e,a}function oo(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[dt];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uff2d\uff33 \uff30\u30b4\u30b7\u30c3\u30af"/>',r[r.length]='<a:font script="Hang" typeface="\ub9d1\uc740 \uace0\ub515"/>',r[r.length]='<a:font script="Hans" typeface="\u5b8b\u4f53"/>',r[r.length]='<a:font script="Hant" typeface="\u65b0\u7d30\u660e\u9ad4"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uff2d\uff33 \uff30\u30b4\u30b7\u30c3\u30af"/>',r[r.length]='<a:font script="Hang" typeface="\ub9d1\uc740 \uace0\ub515"/>',r[r.length]='<a:font script="Hans" typeface="\u5b8b\u4f53"/>',r[r.length]='<a:font script="Hant" typeface="\u65b0\u7d30\u660e\u9ad4"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function co(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:case 4:e.l+=4;break;case 1:t.xclrValue=function(e,t){return qr(e,t)}(e,4);break;case 2:t.xclrValue=cs(e);break;case 3:t.xclrValue=function(e){return e.read_shift(4)}(e)}return e.l+=8,t}function lo(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=co(e);break;case 6:a[1]=function(e,t){return qr(e,t)}(e,r);break;case 14:case 15:a[1]=e.read_shift(1===r?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+t+" "+r)}return a}function fo(){var e=ea();return ta(e,332),ta(e,334,Ta(1)),ta(e,335,function(e){var t=Zr(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),ya(e.name,t),t.slice(0,t.l)}({name:"XLDAPR",version:12e4,flags:3496657072})),ta(e,336),ta(e,339,function(e,t){var r=Zr(8+2*t.length);return r.write_shift(4,e),ya(t,r),r.slice(0,r.l)}(1,"XLDAPR")),ta(e,52),ta(e,35,Ta(514)),ta(e,4096,Ta(0)),ta(e,4097,Yn(1)),ta(e,36),ta(e,53),ta(e,340),ta(e,337,function(e,t){var r=Zr(8);return r.write_shift(4,e),r.write_shift(4,t?1:0),r}(1,!0)),ta(e,51,function(e){var t=Zr(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}([[1,0]])),ta(e,338),ta(e,333),e.end()}function ho(){var e=[dt];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}var uo=1024;function po(e,t){for(var r=[21600,21600],a=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),n=[$t("xml",null,{"xmlns:v":vr,"xmlns:o":hr,"xmlns:x":ur,"xmlns:mv":mr}).replace(/\/>/,">"),$t("o:shapelayout",$t("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),$t("v:shapetype",[$t("v:stroke",null,{joinstyle:"miter"}),$t("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:a})];uo<1e3*e;)uo+=1e3;return t.forEach((function(e){var t=fa(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var a="gradient"==r.type?$t("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,s=$t("v:fill",a,r);++uo,n=n.concat(["<v:shape"+Xt({id:"_x0000_s"+uo,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",s,$t("v:shadow",null,{on:"t",obscured:"t"}),$t("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",jt("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),jt("x:AutoFill","False"),jt("x:Row",String(t.r)),jt("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])})),n.push("</xml>"),n.join("")}function mo(e,t,r,a){var n,s=Array.isArray(e);t.forEach((function(t){var i=fa(t.ref);if(s?(e[i.r]||(e[i.r]=[]),n=e[i.r][i.c]):n=e[t.ref],!n){n={t:"z"},s?e[i.r][i.c]=n:e[t.ref]=n;var o=pa(e["!ref"]||"BDWGO1000001:A1");o.s.r>i.r&&(o.s.r=i.r),o.e.r<i.r&&(o.e.r=i.r),o.s.c>i.c&&(o.s.c=i.c),o.e.c<i.c&&(o.e.c=i.c);var c=da(o);c!==e["!ref"]&&(e["!ref"]=c)}n.c||(n.c=[]);var l={a:t.author,t:t.t,r:t.r,T:r};t.h&&(l.h=t.h);for(var f=n.c.length-1;f>=0;--f){if(!r&&n.c[f].T)return;r&&!n.c[f].T&&n.c.splice(f,1)}if(r&&a)for(f=0;f<a.length;++f)if(l.a==a[f].id){l.a=a[f].name||l.a;break}n.c.push(l)}))}function vo(e){var t=[dt,$t("comments",null,{xmlns:fr[0]})],r=[];return t.push("<authors>"),e.forEach((function(e){e[1].forEach((function(e){var a=At(e.a);-1==r.indexOf(a)&&(r.push(a),t.push("<author>"+a+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))}))})),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach((function(e){var a=0,n=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?a=r.indexOf("tc="+e[1][0].ID):e[1].forEach((function(e){e.a&&(a=r.indexOf(At(e.a))),n.push(e.t||"")})),t.push('<comment ref="'+e[0]+'" authorId="'+a+'"><text>'),n.length<=1)t.push(jt("t",At(n[0]||"")));else{for(var s="Comment:\n    "+n[0]+"\n",i=1;i<n.length;++i)s+="Reply:\n    "+n[i]+"\n";t.push(jt("t",At(s)))}t.push("</text></comment>")})),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function go(e,t,r){var a=[dt,$t("ThreadedComments",null,{xmlns:rr}).replace(/[\/]>/,">")];return e.forEach((function(e){var n="";(e[1]||[]).forEach((function(s,i){if(s.T){s.a&&-1==t.indexOf(s.a)&&t.push(s.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==i?n=o.id:o.parentId=n,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),a.push($t("threadedComment",jt("text",s.t||""),o))}else delete s.ID}))})),a.push("</ThreadedComments>"),a.join("")}var bo=Ea;function wo(e){var t=ea(),r=[];return ta(t,628),ta(t,630),e.forEach((function(e){e[1].forEach((function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),ta(t,632,function(e){return ya(e.slice(0,54))}(e.a)))}))})),ta(t,631),ta(t,633),e.forEach((function(e){e[1].forEach((function(a){a.iauthor=r.indexOf(a.a);var n={s:fa(e[0]),e:fa(e[0])};ta(t,635,function(e,t){return null==t&&(t=Zr(36)),t.write_shift(4,e[1].iauthor),za(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}([n,a])),a.t&&a.t.length>0&&ta(t,637,Aa(a)),ta(t,636),delete a.iauthor}))})),ta(t,634),ta(t,629),t.end()}var To=["xlsb","xlsm","xlam","biff8","xla"];var Eo=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,a,n){var s=!1,i=!1;0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1)),0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1));var o=a.length>0?0|parseInt(a,10):0,c=n.length>0?0|parseInt(n,10):0;return s?c+=t.c:--c,i?o+=t.r:--o,r+(s?"":"$")+la(c)+(i?"":"$")+oa(o)}return function(a,n){return t=n,a.replace(e,r)}}(),yo=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,So=function(){return function(e,t){return e.replace(yo,(function(e,r,a,n,s,i){var o=ca(n)-(a?0:t.c),c=ia(i)-(s?0:t.r);return r+"R"+(0==c?"":s?c+1:"["+c+"]")+"C"+(0==o?"":a?o+1:"["+o+"]")}))}}();function ko(e,t){return e.replace(yo,(function(e,r,a,n,s,i){return r+("$"==a?a+n:la(ca(n)+t.c))+("$"==s?s+i:oa(ia(i)+t.r))}))}function _o(e,t,r){var a=ua(t).s,n=fa(r);return ko(e,{r:n.r-a.r,c:n.c-a.c})}function Ao(e){return e.replace(/_xlfn\./g,"")}function xo(e){e.l+=1}function Co(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function Oo(e,t,r){var a=2;if(r){if(r.biff>=2&&r.biff<=5)return Ro(e);12==r.biff&&(a=4)}var n=e.read_shift(a),s=e.read_shift(a),i=Co(e,2),o=Co(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:o[0],cRel:o[1],rRel:o[2]}}}function Ro(e){var t=Co(e,2),r=Co(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function Io(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return function(e){var t=Co(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}(e);var a=e.read_shift(r&&12==r.biff?4:2),n=Co(e,2);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function No(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:255&r,fQuoted:!!(16384&r),cRel:r>>15,rRel:r>>15}}function Fo(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function Do(e){return[e.read_shift(1),e.read_shift(1)]}function Po(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=jn(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=tn[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Va(e);break;case 2:r[1]=rs(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function Lo(e,t,r){for(var a=e.read_shift(12==r.biff?4:2),n=[],s=0;s!=a;++s)n.push((12==r.biff?Ha:ps)(e,8));return n}function Mo(e,t,r){var a=0,n=0;12==r.biff?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,0==--n&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var o=0;o!=n;++o)i[s][o]=Po(e,r.biff);return i}function Uo(e,t,r){return e.l+=2,[No(e)]}function Bo(e){return e.l+=6,[]}function Wo(e){return e.l+=2,[$n(e),1&e.read_shift(2)]}var Ho=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];var zo={1:{n:"PtgExp",f:function(e,t,r){return e.l++,r&&12==r.biff?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:qr},3:{n:"PtgAdd",f:xo},4:{n:"PtgSub",f:xo},5:{n:"PtgMul",f:xo},6:{n:"PtgDiv",f:xo},7:{n:"PtgPower",f:xo},8:{n:"PtgConcat",f:xo},9:{n:"PtgLt",f:xo},10:{n:"PtgLe",f:xo},11:{n:"PtgEq",f:xo},12:{n:"PtgGe",f:xo},13:{n:"PtgGt",f:xo},14:{n:"PtgNe",f:xo},15:{n:"PtgIsect",f:xo},16:{n:"PtgUnion",f:xo},17:{n:"PtgRange",f:xo},18:{n:"PtgUplus",f:xo},19:{n:"PtgUminus",f:xo},20:{n:"PtgPercent",f:xo},21:{n:"PtgParen",f:xo},22:{n:"PtgMissArg",f:xo},23:{n:"PtgStr",f:function(e,t,r){return e.l++,qn(e,0,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,tn[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,Va(e)}},32:{n:"PtgArray",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[a]}},33:{n:"PtgFunc",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[fc[n],lc[n],a]}},34:{n:"PtgFuncVar",f:function(e,t,r){var a=e[e.l++],n=e.read_shift(1),s=r&&r.biff<=3?[88==a?-1:0,e.read_shift(1)]:function(e){return[e[e.l+1]>>7,32767&e.read_shift(2)]}(e);return[n,(0===s[0]?lc:cc)[s[1]]]}},35:{n:"PtgName",f:function(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,s=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[a,0,s]}},36:{n:"PtgRef",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,Io(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,Oo(e,r.biff>=2&&r.biff,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[a,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:qr},40:{n:"PtgMemNoMem",f:qr},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[a]}},43:{n:"PtgAreaErr",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}},44:{n:"PtgRefN",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=function(e,t,r){var a=r&&r.biff?r.biff:8;if(a>=2&&a<=5)return function(e){var t=e.read_shift(2),r=e.read_shift(1),a=(32768&t)>>15,n=(16384&t)>>14;return t&=16383,1==a&&t>=8192&&(t-=16384),1==n&&r>=128&&(r-=256),{r:t,c:r,cRel:n,rRel:a}}(e);var n=e.read_shift(a>=12?4:2),s=e.read_shift(2),i=(16384&s)>>14,o=(32768&s)>>15;if(s&=16383,1==o)for(;n>524287;)n-=1048576;if(1==i)for(;s>8191;)s-=16384;return{r:n,c:s,cRel:i,rRel:o}}(e,0,r);return[a,n]}},45:{n:"PtgAreaN",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=function(e,t,r){if(r.biff<8)return Ro(e);var a=e.read_shift(12==r.biff?4:2),n=e.read_shift(12==r.biff?4:2),s=Co(e,2),i=Co(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}(e,0,r);return[a,n]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){return 5==r.biff?function(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var a=e.read_shift(2);return e.l+=12,[t,r,a]}(e):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[a,n,Io(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2,"i");if(r)switch(r.biff){case 5:e.l+=12,6;break;case 12:12}return[a,n,Oo(e,0,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[a,n]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[a,n]}},255:{}},Vo={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Go={1:{n:"PtgElfLel",f:Wo},2:{n:"PtgElfRw",f:Uo},3:{n:"PtgElfCol",f:Uo},6:{n:"PtgElfRwV",f:Uo},7:{n:"PtgElfColV",f:Uo},10:{n:"PtgElfRadical",f:Uo},11:{n:"PtgElfRadicalS",f:Bo},13:{n:"PtgElfColS",f:Bo},15:{n:"PtgElfColSV",f:Bo},16:{n:"PtgElfRadicalLel",f:Wo},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2);return{ixti:t,coltype:3&r,rt:Ho[r>>2&31],idx:a,c:n,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},jo={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[a]}},2:{n:"PtgAttrIf",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var a=e.read_shift(r&&2==r.biff?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(r&&2==r.biff?1:2));return n}},8:{n:"PtgAttrGoto",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:Fo},33:{n:"PtgAttrBaxcel",f:Fo},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),Do(e)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),Do(e)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function Xo(e,t,r,a){if(a.biff<8)return qr(e,t);for(var n=e.l+t,s=[],i=0;i!==r.length;++i)switch(r[i][0]){case"PtgArray":r[i][1]=Mo(e,0,a),s.push(r[i][1]);break;case"PtgMemArea":r[i][2]=Lo(e,r[i][1],a),s.push(r[i][2]);break;case"PtgExp":a&&12==a.biff&&(r[i][1][1]=e.read_shift(4),s.push(r[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[i][0]}return 0!==(t=n-e.l)&&s.push(qr(e,t)),s}function $o(e,t,r){for(var a,n,s=e.l+t,i=[];s!=e.l;)t=s-e.l,n=e[e.l],a=zo[n]||zo[Vo[n]],24!==n&&25!==n||(a=(24===n?Go:jo)[e[e.l+1]]),a&&a.f?i.push([a.n,a.f(e,t,r)]):qr(e,t);return i}function Yo(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],s=0;s<a.length;++s){var i=a[s];if(i)if(2===i[0])n.push('"'+i[1].replace(/"/g,'""')+'"');else n.push(i[1]);else n.push("")}t.push(n.join(","))}return t.join(";")}var Ko={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Jo(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=-1==a[1]?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:return null!=r.SID?e.SheetNames[r.SID]:"SH33TJSSAME"+e[a[0]][0];default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=-1==a[1]?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map((function(e){return e.Name})).join(";;");default:return e[a[0]][0][3]?(n=-1==a[1]?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]):"SH33TJSERR2"}}function qo(e,t,r){var a=Jo(e,t,r);return"#REF"==a?a:function(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(a,r)}function Zo(e,t,r,a,n){var s,i,o,c,l=n&&n.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,v="",g=0,b=e[0].length;g<b;++g){var w=e[0][g];switch(w[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:v=Je(" ",e[0][m][1][1]);break;case 1:v=Je("\r",e[0][m][1][1]);break;default:if(v="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}i+=v,m=-1}h.push(i+Ko[w[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push(i+":"+s);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":o=ra(w[1][1],f,n),h.push(na(o,l));break;case"PtgRefN":o=r?ra(w[1][1],r,n):w[1][1],h.push(na(o,l));break;case"PtgRef3d":u=w[1][1],o=ra(w[1][2],f,n);p=qo(a,u,n);h.push(p+"!"+na(o,l));break;case"PtgFunc":case"PtgFuncVar":var T=w[1][0],E=w[1][1];T||(T=0);var y=0==(T&=127)?[]:h.slice(-T);h.length-=T,"User"===E&&(E=y.shift()),h.push(E+"("+y.join(",")+")");break;case"PtgBool":h.push(w[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(w[1]);break;case"PtgNum":h.push(String(w[1]));break;case"PtgStr":h.push('"'+w[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":c=aa(w[1][1],r?{s:r}:f,n),h.push(sa(c,n));break;case"PtgArea":c=aa(w[1][1],f,n),h.push(sa(c,n));break;case"PtgArea3d":u=w[1][1],c=w[1][2],p=qo(a,u,n),h.push(p+"!"+sa(c,n));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=w[1][2];var S=(a.names||[])[d-1]||(a[0]||[])[d],k=S?S.Name:"SH33TJSNAME"+String(d);k&&"_xlfn."==k.slice(0,6)&&!n.xlfn&&(k=k.slice(6)),h.push(k);break;case"PtgNameX":var _,A=w[1][1];if(d=w[1][2],!(n.biff<=5)){var x="";if(14849==((a[A]||[])[0]||[])[0]||(1025==((a[A]||[])[0]||[])[0]?a[A][d]&&a[A][d].itab>0&&(x=a.SheetNames[a[A][d].itab-1]+"!"):x=a.SheetNames[d-1]+"!"),a[A]&&a[A][d])x+=a[A][d].Name;else if(a[0]&&a[0][d])x+=a[0][d].Name;else{var C=(Jo(a,A,n)||"").split(";;");C[d-1]?x=C[d-1]:x+="SH33TJSERRX"}h.push(x);break}A<0&&(A=-A),a[A]&&(_=a[A][d]),_||(_={Name:"SH33TJSERRY"}),h.push(_.Name);break;case"PtgParen":var O="(",R=")";if(m>=0){switch(v="",e[0][m][1][0]){case 2:O=Je(" ",e[0][m][1][1])+O;break;case 3:O=Je("\r",e[0][m][1][1])+O;break;case 4:R=Je(" ",e[0][m][1][1])+R;break;case 5:R=Je("\r",e[0][m][1][1])+R;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(O+h.pop()+R);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":o={c:w[1][1],r:w[1][0]};var I={c:r.c,r:r.r};if(a.sharedf[ha(o)]){var N=a.sharedf[ha(o)];h.push(Zo(N,f,I,a,n))}else{var F=!1;for(s=0;s!=a.arrayf.length;++s)if(i=a.arrayf[s],!(o.c<i[0].s.c||o.c>i[0].e.c)&&!(o.r<i[0].s.r||o.r>i[0].e.r)){h.push(Zo(i[1],f,I,a,n)),F=!0;break}F||h.push(w[1])}break;case"PtgArray":h.push("{"+Yo(w[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=g;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+w[1].idx+"[#"+w[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");default:throw new Error("Unrecognized Formula Token: "+String(w))}if(3!=n.biff&&m>=0&&-1==["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"].indexOf(e[0][g][0])){var D=!0;switch((w=e[0][m])[1][0]){case 4:D=!1;case 0:v=Je(" ",w[1][1]);break;case 5:D=!1;case 1:v=Je("\r",w[1][1]);break;default:if(v="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+w[1][0])}h.push((D?v:"")+h.pop()+(D?"":v)),m=-1}}if(h.length>1&&n.WTF)throw new Error("bad formula stack");return h[0]}function Qo(e,t,r){var a,n=e.l+t,s=2==r.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],qr(e,t-2)];var o=$o(e,i,r);return t!==i+s&&(a=Xo(e,t-i-s,o,r)),e.l=n,[o,a]}function ec(e,t,r){var a,n=e.l+t,s=e.read_shift(2),i=$o(e,s,r);return 65535==s?[[],qr(e,t-2)]:(t!==s+2&&(a=Xo(e,n-s-2,i,r)),[i,a])}function tc(e,t,r){var a=e.l+t,n=fs(e);2==r.biff&&++e.l;var s=function(e){var t;if(65535!==Hr(e,e.l+6))return[Va(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=1===e[e.l+2],e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}(e),i=e.read_shift(1);2!=r.biff&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var o=function(e,t,r){var a,n=e.l+t,s=2==r.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],qr(e,t-2)];var o=$o(e,i,r);return t!==i+s&&(a=Xo(e,t-i-s,o,r)),e.l=n,[o,a]}(e,a-e.l,r);return{cell:n,val:s[0],formula:o,shared:i>>3&1,tt:s[1]}}function rc(e,t,r,a,n){var s=hs(t,r,n),i=function(e){if(null==e){var t=Zr(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return Ga("number"==typeof e?e:0)}(e.v),o=Zr(6);o.write_shift(2,33),o.write_shift(4,0);for(var c=Zr(e.bf.length),l=0;l<e.bf.length;++l)c[l]=e.bf[l];return O([s,i,o,c])}function ac(e,t,r){var a=e.read_shift(4),n=$o(e,a,r),s=e.read_shift(4);return[n,s>0?Xo(e,s,n,r):null]}var nc=ac,sc=ac,ic=ac,oc=ac,cc={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},lc={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},fc={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function hc(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,(function(e,t){return t.replace(/\./g,"")}))).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function uc(e){var t=e.split(":");return[t[0].split(".")[0],t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}var dc={},pc={},mc="undefined"!==typeof Map;function vc(e,t,r){var a=0,n=e.length;if(r){if(mc?r.has(t):Object.prototype.hasOwnProperty.call(r,t))for(var s=mc?r.get(t):r[t];a<s.length;++a)if(e[s[a]].t===t)return e.Count++,s[a]}else for(;a<n;++a)if(e[a].t===t)return e.Count++,a;return e[n]={t:t},e.Count++,e.Unique++,r&&(mc?(r.has(t)||r.set(t,[]),r.get(t).push(n)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(n))),n}function gc(e,t){var r={min:e+1,max:e+1},a=-1;return t.MDW&&(_i=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?a=xi(t.wpx):null!=t.wch&&(a=t.wch),a>-1?(r.width=Ci(a),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function bc(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function wc(e,t,r){var a=r.revssf[null!=t.z?t.z:"General"],n=60,s=e.length;if(null==a&&r.ssf)for(;n<392;++n)if(null==r.ssf[n]){Ee(t.z,n),r.ssf[n]=t.z,r.revssf[t.z]=a=n;break}for(n=0;n!=s;++n)if(e[n].numFmtId===a)return n;return e[s]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}function Tc(e,t,r,a,n,s){try{a.cellNF&&(e.z=H[t])}catch(o){if(a.WTF)throw o}if("z"!==e.t||a.cellStyles){if("d"===e.t&&"string"===typeof e.v&&(e.v=$e(e.v)),(!a||!1!==a.cellText)&&"z"!==e.t)try{if(null==H[t]&&Ee(_e[t]||"General",t),"e"===e.t)e.w=e.w||tn[e.v];else if(0===t)if("n"===e.t)(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Z(e.v);else if("d"===e.t){var i=Ue(e.v);e.w=(0|i)===i?i.toString(10):Z(i)}else{if(void 0===e.v)return"";e.w=Q(e.v,pc)}else"d"===e.t?e.w=Te(t,Ue(e.v),pc):e.w=Te(t,e.v,pc)}catch(o){if(a.WTF)throw o}if(a.cellStyles&&null!=r)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=ki(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=ki(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(o){if(a.WTF&&s.Fills)throw o}}}function Ec(e,t,r){if(e&&e["!ref"]){var a=pa(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}var yc=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,Sc=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,kc=/<(?:\w:)?hyperlink [^>]*>/gm,_c=/"(\w*:\w*)"/,Ac=/<(?:\w:)?col\b[^>]*[\/]?>/g,xc=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,Cc=/<(?:\w:)?pageMargins[^>]*\/>/g,Oc=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,Rc=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,Ic=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function Nc(e,t,r,a,n,s,i){if(!e)return e;a||(a={"!id":{}});var o=t.dense?[]:{},c={s:{r:2e6,c:2e6},e:{r:0,c:0}},l="",f="",h=e.match(Sc);h?(l=e.slice(0,h.index),f=e.slice(h.index+h[0].length)):l=f=e;var u=l.match(Oc);u?Fc(u[0],o,n,r):(u=l.match(Rc))&&function(e,t,r,a,n){Fc(e.slice(0,e.indexOf(">")),r,a,n)}(u[0],u[1],o,n,r);var d=(l.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var p=l.slice(d,d+50).match(_c);p&&function(e,t){var r=pa(t);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=da(r))}(o,p[1])}var m=l.match(Ic);m&&m[1]&&function(e,t){t.Views||(t.Views=[{}]);(e.match(Lc)||[]).forEach((function(e,r){var a=wt(e);t.Views[r]||(t.Views[r]={}),+a.zoomScale&&(t.Views[r].zoom=+a.zoomScale),It(a.rightToLeft)&&(t.Views[r].RTL=!0)}))}(m[1],n);var v=[];if(t.cellStyles){var g=l.match(Ac);g&&function(e,t){for(var r=!1,a=0;a!=t.length;++a){var n=wt(t[a],!0);n.hidden&&(n.hidden=It(n.hidden));var s=parseInt(n.min,10)-1,i=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!r&&n.width&&(r=!0,Ri(n.width)),Ii(n);s<=i;)e[s++]=Ke(n)}}(v,g)}h&&Uc(h[1],o,t,c,s,i);var b=f.match(xc);b&&(o["!autofilter"]=function(e){var t={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return t}(b[0]));var w=[],T=f.match(yc);if(T)for(d=0;d!=T.length;++d)w[d]=pa(T[d].slice(T[d].indexOf('"')+1));var E=f.match(kc);E&&function(e,t,r){for(var a=Array.isArray(e),n=0;n!=t.length;++n){var s=wt(Lt(t[n]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+St(s.location))):(s.Target="#"+St(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var o=pa(s.ref),c=o.s.r;c<=o.e.r;++c)for(var l=o.s.c;l<=o.e.c;++l){var f=ha({c:l,r:c});a?(e[c]||(e[c]=[]),e[c][l]||(e[c][l]={t:"z",v:void 0}),e[c][l].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}(o,E,a);var y=f.match(Cc);if(y&&(o["!margins"]=function(e){var t={};return["left","right","top","bottom","header","footer"].forEach((function(r){e[r]&&(t[r]=parseFloat(e[r]))})),t}(wt(y[0]))),!o["!ref"]&&c.e.c>=c.s.c&&c.e.r>=c.s.r&&(o["!ref"]=da(c)),t.sheetRows>0&&o["!ref"]){var S=pa(o["!ref"]);t.sheetRows<=+S.e.r&&(S.e.r=t.sheetRows-1,S.e.r>c.e.r&&(S.e.r=c.e.r),S.e.r<S.s.r&&(S.s.r=S.e.r),S.e.c>c.e.c&&(S.e.c=c.e.c),S.e.c<S.s.c&&(S.s.c=S.e.c),o["!fullref"]=o["!ref"],o["!ref"]=da(S))}return v.length>0&&(o["!cols"]=v),w.length>0&&(o["!merges"]=w),o}function Fc(e,t,r,a){var n=wt(e);r.Sheets[a]||(r.Sheets[a]={}),n.codeName&&(r.Sheets[a].CodeName=St(Lt(n.codeName)))}var Dc=["objects","scenarios","selectLockedCells","selectUnlockedCells"],Pc=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];var Lc=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function Mc(e,t,r,a){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!==typeof e.f||"z"===e.t&&!e.f)return"";var n="",s=e.t,i=e.v;if("z"!==e.t)switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=tn[e.v];break;case"d":a&&a.cellDates?n=$e(e.v,-1).toISOString():((e=Ke(e)).t="n",n=""+(e.v=Ue($e(e.v)))),"undefined"===typeof e.z&&(e.z=H[14]);break;default:n=e.v}var o=jt("v",At(n)),c={r:t},l=wc(a.cellXfs,e,a);switch(0!==l&&(c.s=l),e.t){case"n":case"z":break;case"d":c.t="d";break;case"b":c.t="b";break;case"e":c.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){o=jt("v",""+vc(a.Strings,e.v,a.revStrings)),c.t="s";break}c.t="str"}if(e.t!=s&&(e.t=s,e.v=i),"string"==typeof e.f&&e.f){var f=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=$t("f",At(e.f),f)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(c.cm=1),$t("c",o,c)}var Uc=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,s=Ut("v"),i=Ut("f");return function(o,c,l,f,h,u){for(var d,p,m,v,g,b=0,w="",T=[],E=[],y=0,S=0,k=0,_="",A=0,x=0,C=0,O=0,R=Array.isArray(u.CellXf),I=[],N=[],F=Array.isArray(c),D=[],P={},L=!1,M=!!l.sheetStubs,U=o.split(t),B=0,W=U.length;B!=W;++B){var z=(w=U[B].trim()).length;if(0!==z){var V=0;e:for(b=0;b<z;++b)switch(w[b]){case">":if("/"!=w[b-1]){++b;break e}if(l&&l.cellStyles){if(A=null!=(p=wt(w.slice(V,b),!0)).r?parseInt(p.r,10):A+1,x=-1,l.sheetRows&&l.sheetRows<A)continue;P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=Fi(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(D[A-1]=P)}break;case"<":V=b}if(V>=b)break;if(A=null!=(p=wt(w.slice(V,b),!0)).r?parseInt(p.r,10):A+1,x=-1,!(l.sheetRows&&l.sheetRows<A)){f.s.r>A-1&&(f.s.r=A-1),f.e.r<A-1&&(f.e.r=A-1),l&&l.cellStyles&&(P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=Fi(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(D[A-1]=P)),T=w.slice(b).split(e);for(var G=0;G!=T.length&&"<"==T[G].trim().charAt(0);++G);for(T=T.slice(G),b=0;b!=T.length;++b)if(0!==(w=T[b].trim()).length){if(E=w.match(r),y=b,S=0,k=0,w="<c "+("<"==w.slice(0,1)?">":"")+w,null!=E&&2===E.length){for(y=0,_=E[1],S=0;S!=_.length&&!((k=_.charCodeAt(S)-64)<1||k>26);++S)y=26*y+k;x=--y}else++x;for(S=0;S!=w.length&&62!==w.charCodeAt(S);++S);if(++S,(p=wt(w.slice(0,S),!0)).r||(p.r=ha({r:A-1,c:x})),d={t:""},null!=(E=(_=w.slice(S)).match(s))&&""!==E[1]&&(d.v=St(E[1])),l.cellFormula){if(null!=(E=_.match(i))&&""!==E[1]){if(d.f=St(Lt(E[1])).replace(/\r\n/g,"\n"),l.xlfn||(d.f=Ao(d.f)),E[0].indexOf('t="array"')>-1)d.F=(_.match(n)||[])[1],d.F.indexOf(":")>-1&&I.push([pa(d.F),d.F]);else if(E[0].indexOf('t="shared"')>-1){v=wt(E[0]);var j=St(Lt(E[1]));l.xlfn||(j=Ao(j)),N[parseInt(v.si,10)]=[v,j,p.r]}}else(E=_.match(/<f[^>]*\/>/))&&N[(v=wt(E[0])).si]&&(d.f=_o(N[v.si][1],N[v.si][2],p.r));var X=fa(p.r);for(S=0;S<I.length;++S)X.r>=I[S][0].s.r&&X.r<=I[S][0].e.r&&X.c>=I[S][0].s.c&&X.c<=I[S][0].e.c&&(d.F=I[S][1])}if(null==p.t&&void 0===d.v)if(d.f||d.F)d.v=0,d.t="n";else{if(!M)continue;d.t="z"}else d.t=p.t||"n";switch(f.s.c>x&&(f.s.c=x),f.e.c<x&&(f.e.c=x),d.t){case"n":if(""==d.v||null==d.v){if(!M)continue;d.t="z"}else d.v=parseFloat(d.v);break;case"s":if("undefined"==typeof d.v){if(!M)continue;d.t="z"}else m=dc[parseInt(d.v,10)],d.v=m.t,d.r=m.r,l.cellHTML&&(d.h=m.h);break;case"str":d.t="s",d.v=null!=d.v?Lt(d.v):"",l.cellHTML&&(d.h=Ot(d.v));break;case"inlineStr":E=_.match(a),d.t="s",null!=E&&(m=ti(E[1]))?(d.v=m.t,l.cellHTML&&(d.h=m.h)):d.v="";break;case"b":d.v=It(d.v);break;case"d":l.cellDates?d.v=$e(d.v,1):(d.v=Ue($e(d.v,1)),d.t="n");break;case"e":l&&!1===l.cellText||(d.w=d.v),d.v=rn[d.v]}if(C=O=0,g=null,R&&void 0!==p.s&&null!=(g=u.CellXf[p.s])&&(null!=g.numFmtId&&(C=g.numFmtId),l.cellStyles&&null!=g.fillId&&(O=g.fillId)),Tc(d,C,O,l,h,u),l.cellDates&&R&&"n"==d.t&&ve(H[C])&&(d.t="d",d.v=ze(d.v)),p.cm&&l.xlmeta){var $=(l.xlmeta.Cell||[])[+p.cm-1];$&&"XLDAPR"==$.type&&(d.D=!0)}if(F){var Y=fa(p.r);c[Y.r]||(c[Y.r]=[]),c[Y.r][Y.c]=d}else c[p.r]=d}}}}D.length>0&&(c["!rows"]=D)}}();function Bc(e,t,r,a){var n,s=[dt,$t("worksheet",null,{xmlns:fr[0],"xmlns:r":ir})],i=r.SheetNames[e],o="",c=r.Sheets[i];null==c&&(c={});var l=c["!ref"]||"A1",f=pa(l);if(f.e.c>16383||f.e.r>1048575){if(t.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383),f.e.r=Math.min(f.e.c,1048575),l=da(f)}a||(a={}),c["!comments"]=[];var h=[];!function(e,t,r,a,n){var s=!1,i={},o=null;if("xlsx"!==a.bookType&&t.vbaraw){var c=t.SheetNames[r];try{t.Workbook&&(c=t.Workbook.Sheets[r].CodeName||c)}catch(f){}s=!0,i.codeName=Mt(At(c))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),o=(o||"")+$t("outlinePr",null,l)}(s||o)&&(n[n.length]=$t("sheetPr",o,i))}(c,r,e,t,s),s[s.length]=$t("dimension",null,{ref:l}),s[s.length]=function(e,t,r,a){var n={workbookViewId:"0"};return(((a||{}).Workbook||{}).Views||[])[0]&&(n.rightToLeft=a.Workbook.Views[0].RTL?"1":"0"),$t("sheetViews",$t("sheetView",null,n),{})}(0,0,0,r),t.sheetFormat&&(s[s.length]=$t("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=c["!cols"]&&c["!cols"].length>0&&(s[s.length]=function(e,t){for(var r,a=["<cols>"],n=0;n!=t.length;++n)(r=t[n])&&(a[a.length]=$t("col",null,gc(n,r)));return a[a.length]="</cols>",a.join("")}(0,c["!cols"])),s[n=s.length]="<sheetData/>",c["!links"]=[],null!=c["!ref"]&&(o=function(e,t,r,a){var n,s,i=[],o=[],c=pa(e["!ref"]),l="",f="",h=[],u=0,d=0,p=e["!rows"],m=Array.isArray(e),v={r:f},g=-1;for(d=c.s.c;d<=c.e.c;++d)h[d]=la(d);for(u=c.s.r;u<=c.e.r;++u){for(o=[],f=oa(u),d=c.s.c;d<=c.e.c;++d){n=h[d]+f;var b=m?(e[u]||[])[d]:e[n];void 0!==b&&null!=(l=Mc(b,n,e,t))&&o.push(l)}(o.length>0||p&&p[u])&&(v={r:f},p&&p[u]&&((s=p[u]).hidden&&(v.hidden=1),g=-1,s.hpx?g=Ni(s.hpx):s.hpt&&(g=s.hpt),g>-1&&(v.ht=g,v.customHeight=1),s.level&&(v.outlineLevel=s.level)),i[i.length]=$t("row",o.join(""),v))}if(p)for(;u<p.length;++u)p&&p[u]&&(v={r:u+1},(s=p[u]).hidden&&(v.hidden=1),g=-1,s.hpx?g=Ni(s.hpx):s.hpt&&(g=s.hpt),g>-1&&(v.ht=g,v.customHeight=1),s.level&&(v.outlineLevel=s.level),i[i.length]=$t("row","",v));return i.join("")}(c,t,0,0),o.length>0&&(s[s.length]=o)),s.length>n+1&&(s[s.length]="</sheetData>",s[n]=s[n].replace("/>",">")),c["!protect"]&&(s[s.length]=function(e){var t={sheet:1};return Dc.forEach((function(r){null!=e[r]&&e[r]&&(t[r]="1")})),Pc.forEach((function(r){null==e[r]||e[r]||(t[r]="0")})),e.password&&(t.password=bi(e.password).toString(16).toUpperCase()),$t("sheetProtection",null,t)}(c["!protect"])),null!=c["!autofilter"]&&(s[s.length]=function(e,t,r,a){var n="string"==typeof e.ref?e.ref:da(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,i=ua(n);i.s.r==i.e.r&&(i.e.r=ua(t["!ref"]).e.r,n=da(i));for(var o=0;o<s.length;++o){var c=s[o];if("_xlnm._FilterDatabase"==c.Name&&c.Sheet==a){c.Ref="'"+r.SheetNames[a]+"'!"+n;break}}return o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+n}),$t("autoFilter",null,{ref:n})}(c["!autofilter"],c,r,e)),null!=c["!merges"]&&c["!merges"].length>0&&(s[s.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+da(e[r])+'"/>';return t+"</mergeCells>"}(c["!merges"]));var u,d,p=-1,m=-1;return c["!links"].length>0&&(s[s.length]="<hyperlinks>",c["!links"].forEach((function(e){e[1].Target&&(u={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(m=hn(a,-1,At(e[1].Target).replace(/#.*$/,""),on.HLINK),u["r:id"]="rId"+m),(p=e[1].Target.indexOf("#"))>-1&&(u.location=At(e[1].Target.slice(p+1))),e[1].Tooltip&&(u.tooltip=At(e[1].Tooltip)),s[s.length]=$t("hyperlink",null,u))})),s[s.length]="</hyperlinks>"),delete c["!links"],null!=c["!margins"]&&(s[s.length]=(bc(d=c["!margins"]),$t("pageMargins",null,d))),t&&!t.ignoreEC&&void 0!=t.ignoreEC||(s[s.length]=jt("ignoredErrors",$t("ignoredError",null,{numberStoredAsText:1,sqref:l}))),h.length>0&&(m=hn(a,-1,"../drawings/drawing"+(e+1)+".xml",on.DRAW),s[s.length]=$t("drawing",null,{"r:id":"rId"+m}),c["!drawing"]=h),c["!comments"].length>0&&(m=hn(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",on.VML),s[s.length]=$t("legacyDrawing",null,{"r:id":"rId"+m}),c["!legacy"]=m),s.length>1&&(s[s.length]="</worksheet>",s[1]=s[1].replace("/>",">")),s.join("")}function Wc(e,t,r,a){var n=function(e,t,r){var a=Zr(145),n=(r["!rows"]||[])[e]||{};a.write_shift(4,e),a.write_shift(4,0);var s=320;n.hpx?s=20*Ni(n.hpx):n.hpt&&(s=20*n.hpt),a.write_shift(2,s),a.write_shift(1,0);var i=0;n.level&&(i|=n.level),n.hidden&&(i|=16),(n.hpx||n.hpt)&&(i|=32),a.write_shift(1,i),a.write_shift(1,0);var o=0,c=a.l;a.l+=4;for(var l={r:e,c:0},f=0;f<16;++f)if(!(t.s.c>f+1<<10||t.e.c<f<<10)){for(var h=-1,u=-1,d=f<<10;d<f+1<<10;++d)l.c=d,(Array.isArray(r)?(r[l.r]||[])[l.c]:r[ha(l)])&&(h<0&&(h=d),u=d);h<0||(++o,a.write_shift(4,h),a.write_shift(4,u))}var p=a.l;return a.l=c,a.write_shift(4,o),a.l=p,a.length>a.l?a.slice(0,a.l):a}(a,r,t);(n.length>17||(t["!rows"]||[])[a])&&ta(e,0,n)}var Hc=Ha,zc=za;function Vc(e){return[Oa(e),Va(e),"n"]}var Gc=Ha,jc=za;var Xc=["left","right","top","bottom","header","footer"];function $c(e,t,r,a,n,s,i){if(void 0===t.v)return!1;var o="";switch(t.t){case"b":o=t.v?"1":"0";break;case"d":(t=Ke(t)).z=t.z||H[14],t.v=Ue($e(t.v)),t.t="n";break;case"n":case"e":o=""+t.v;break;default:o=t.v}var c={r:r,c:a};switch(c.s=wc(n.cellXfs,t,n),t.l&&s["!links"].push([ha(c),t.l]),t.c&&s["!comments"].push([ha(c),t.c]),t.t){case"s":case"str":return n.bookSST?(o=vc(n.Strings,t.v,n.revStrings),c.t="s",c.v=o,i?ta(e,18,function(e,t,r){return null==r&&(r=Zr(8)),Ra(t,r),r.write_shift(4,t.v),r}(0,c)):ta(e,7,function(e,t,r){return null==r&&(r=Zr(12)),Ca(t,r),r.write_shift(4,t.v),r}(0,c))):(c.t="str",i?ta(e,17,function(e,t,r){return null==r&&(r=Zr(8+4*e.v.length)),Ra(t,r),ya(e.v,r),r.length>r.l?r.slice(0,r.l):r}(t,c)):ta(e,6,function(e,t,r){return null==r&&(r=Zr(12+4*e.v.length)),Ca(t,r),ya(e.v,r),r.length>r.l?r.slice(0,r.l):r}(t,c))),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?i?ta(e,13,function(e,t,r){return null==r&&(r=Zr(8)),Ra(t,r),Ba(e.v,r),r}(t,c)):ta(e,2,function(e,t,r){return null==r&&(r=Zr(12)),Ca(t,r),Ba(e.v,r),r}(t,c)):i?ta(e,16,function(e,t,r){return null==r&&(r=Zr(12)),Ra(t,r),Ga(e.v,r),r}(t,c)):ta(e,5,function(e,t,r){return null==r&&(r=Zr(16)),Ca(t,r),Ga(e.v,r),r}(t,c)),!0;case"b":return c.t="b",i?ta(e,15,function(e,t,r){return null==r&&(r=Zr(5)),Ra(t,r),r.write_shift(1,e.v?1:0),r}(t,c)):ta(e,4,function(e,t,r){return null==r&&(r=Zr(9)),Ca(t,r),r.write_shift(1,e.v?1:0),r}(t,c)),!0;case"e":return c.t="e",i?ta(e,14,function(e,t,r){return null==r&&(r=Zr(8)),Ra(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}(t,c)):ta(e,3,function(e,t,r){return null==r&&(r=Zr(9)),Ca(t,r),r.write_shift(1,e.v),r}(t,c)),!0}return i?ta(e,12,function(e,t,r){return null==r&&(r=Zr(4)),Ra(t,r)}(0,c)):ta(e,1,function(e,t,r){return null==r&&(r=Zr(8)),Ca(t,r)}(0,c)),!0}function Yc(e,t){var r,a;t&&t["!merges"]&&(ta(e,177,(r=t["!merges"].length,null==a&&(a=Zr(4)),a.write_shift(4,r),a)),t["!merges"].forEach((function(t){ta(e,176,jc(t))})),ta(e,178))}function Kc(e,t){t&&t["!cols"]&&(ta(e,390),t["!cols"].forEach((function(t,r){t&&ta(e,60,function(e,t,r){null==r&&(r=Zr(18));var a=gc(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,256*(a.width||10)),r.write_shift(4,0);var n=0;return t.hidden&&(n|=1),"number"==typeof a.width&&(n|=2),t.level&&(n|=t.level<<8),r.write_shift(2,n),r}(r,t))})),ta(e,391))}function Jc(e,t){t&&t["!ref"]&&(ta(e,648),ta(e,649,function(e){var t=Zr(24);return t.write_shift(4,4),t.write_shift(4,1),za(e,t),t}(pa(t["!ref"]))),ta(e,650))}function qc(e,t,r){t["!links"].forEach((function(t){if(t[1].Target){var a=hn(r,-1,t[1].Target.replace(/#.*$/,""),on.HLINK);ta(e,494,function(e,t){var r=Zr(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));za({s:fa(e[0]),e:fa(e[0])},r),Ma("rId"+t,r);var a=e[1].Target.indexOf("#");return ya((-1==a?"":e[1].Target.slice(a+1))||"",r),ya(e[1].Tooltip||"",r),ya("",r),r.slice(0,r.l)}(t,a))}})),delete t["!links"]}function Zc(e,t,r){ta(e,133),ta(e,137,function(e,t,r){null==r&&(r=Zr(30));var a=924;return(((t||{}).Views||[])[0]||{}).RTL&&(a|=32),r.write_shift(2,a),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}(0,r)),ta(e,138),ta(e,134)}function Qc(e,t){var r,a;t["!protect"]&&ta(e,535,(r=t["!protect"],null==a&&(a=Zr(66)),a.write_shift(2,r.password?bi(r.password):0),a.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach((function(e){e[1]?a.write_shift(4,null==r[e[0]]||r[e[0]]?0:1):a.write_shift(4,null!=r[e[0]]&&r[e[0]]?0:1)})),a))}function el(e,t,r,a){var n=ea(),s=r.SheetNames[e],i=r.Sheets[s]||{},o=s;try{r&&r.Workbook&&(o=r.Workbook.Sheets[e].CodeName||o)}catch(h){}var c,l,f=pa(i["!ref"]||"A1");if(f.e.c>16383||f.e.r>1048575){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383),f.e.r=Math.min(f.e.c,1048575)}return i["!links"]=[],i["!comments"]=[],ta(n,129),(r.vbaraw||i["!outline"])&&ta(n,147,function(e,t,r){null==r&&(r=Zr(84+4*e.length));var a=192;t&&(t.above&&(a&=-65),t.left&&(a&=-129)),r.write_shift(1,a);for(var n=1;n<3;++n)r.write_shift(1,0);return ja({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),Na(e,r),r.slice(0,r.l)}(o,i["!outline"])),ta(n,148,zc(f)),Zc(n,0,r.Workbook),Kc(n,i),function(e,t,r,a){var n,s=pa(t["!ref"]||"A1"),i="",o=[];ta(e,145);var c=Array.isArray(t),l=s.e.r;t["!rows"]&&(l=Math.max(s.e.r,t["!rows"].length-1));for(var f=s.s.r;f<=l;++f){i=oa(f),Wc(e,t,s,f);var h=!1;if(f<=s.e.r)for(var u=s.s.c;u<=s.e.c;++u){f===s.s.r&&(o[u]=la(u)),n=o[u]+i;var d=c?(t[f]||[])[u]:t[n];h=!!d&&$c(e,d,f,u,a,t,h)}}ta(e,146)}(n,i,0,t),Qc(n,i),function(e,t,r,a){if(t["!autofilter"]){var n=t["!autofilter"],s="string"===typeof n.ref?n.ref:da(n.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,o=ua(s);o.s.r==o.e.r&&(o.e.r=ua(t["!ref"]).e.r,s=da(o));for(var c=0;c<i.length;++c){var l=i[c];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==a){l.Ref="'"+r.SheetNames[a]+"'!"+s;break}}c==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+s}),ta(e,161,za(pa(s))),ta(e,162)}}(n,i,r,e),Yc(n,i),qc(n,i,a),i["!margins"]&&ta(n,476,(c=i["!margins"],null==l&&(l=Zr(48)),bc(c),Xc.forEach((function(e){Ga(c[e],l)})),l)),t&&!t.ignoreEC&&void 0!=t.ignoreEC||Jc(n,i),function(e,t,r,a){if(t["!comments"].length>0){var n=hn(a,-1,"../drawings/vmlDrawing"+(r+1)+".vml",on.VML);ta(e,551,Ma("rId"+n)),t["!legacy"]=n}}(n,i,e,a),ta(n,130),n.end()}function tl(e,t,r,a,n,s){var i=s||{"!type":"chart"};if(!e)return s;var o=0,c=0,l="A",f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach((function(e){var t=function(e){var t,r=[],a=e.match(/^<c:numCache>/);(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/gm)||[]).forEach((function(e){var t=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);t&&(r[+t[1]]=a?+t[2]:t[2])}));var n=St((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/gm)||[]).forEach((function(e){t=e.replace(/<.*?>/g,"")})),[r,n,t]}(e);f.s.r=f.s.c=0,f.e.c=o,l=la(o),t[0].forEach((function(e,r){i[l+oa(r)]={t:"n",v:e,z:t[1]},c=r})),f.e.r<c&&(f.e.r=c),++o})),o>0&&(i["!ref"]=da(f)),i}var rl=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],al=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],nl=[],sl=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function il(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var s=t[n];if(null==a[s[0]])a[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof a[s[0]]&&(a[s[0]]=It(a[s[0]]));break;case"int":"string"==typeof a[s[0]]&&(a[s[0]]=parseInt(a[s[0]],10))}}}function ol(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(null==e[a[0]])e[a[0]]=a[1];else switch(a[2]){case"bool":"string"==typeof e[a[0]]&&(e[a[0]]=It(e[a[0]]));break;case"int":"string"==typeof e[a[0]]&&(e[a[0]]=parseInt(e[a[0]],10))}}}function cl(e){ol(e.WBProps,rl),ol(e.CalcPr,sl),il(e.WBView,al),il(e.Sheets,nl),pc.date1904=It(e.WBProps.date1904)}var ll="][*?/\\".split("");function fl(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;return ll.forEach((function(a){if(-1!=e.indexOf(a)){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}})),r}function hl(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t,r,a,n=e.Workbook&&e.Workbook.Sheets||[];t=e.SheetNames,r=n,a=!!e.vbaraw,t.forEach((function(e,n){fl(e);for(var s=0;s<n;++s)if(e==t[s])throw new Error("Duplicate Sheet Name: "+e);if(a){var i=r&&r[n]&&r[n].CodeName||e;if(95==i.charCodeAt(0)&&i.length>22)throw new Error("Bad Code Name: Worksheet"+i)}}));for(var s=0;s<e.SheetNames.length;++s)Ec(e.Sheets[e.SheetNames[s]],e.SheetNames[s],s)}var ul=/<\w+:workbook/;function dl(e){var t=[dt];t[t.length]=$t("workbook",null,{xmlns:fr[0],"xmlns:r":ir});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(rl.forEach((function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(a[t[0]]=e.Workbook.WBProps[t[0]])})),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),t[t.length]=$t("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],s=0;if(n&&n[0]&&n[0].Hidden){for(t[t.length]="<bookViews>",s=0;s!=e.SheetNames.length&&n[s]&&n[s].Hidden;++s);s==e.SheetNames.length&&(s=0),t[t.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",s=0;s!=e.SheetNames.length;++s){var i={name:At(e.SheetNames[s].slice(0,31))};if(i.sheetId=""+(s+1),i["r:id"]="rId"+(s+1),n[s])switch(n[s].Hidden){case 1:i.state="hidden";break;case 2:i.state="veryHidden"}t[t.length]=$t("sheet",null,i)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach((function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=$t("definedName",At(e.Ref),r))})),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function pl(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}function ml(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,a,n=t.Workbook.Sheets,s=0,i=-1,o=-1;s<n.length;++s)!n[s]||!n[s].Hidden&&-1==i?i=s:1==n[s].Hidden&&-1==o&&(o=s);if(!(o>i))ta(e,135),ta(e,158,(r=i,a||(a=Zr(29)),a.write_shift(-4,0),a.write_shift(-4,460),a.write_shift(4,28800),a.write_shift(4,17600),a.write_shift(4,500),a.write_shift(4,r),a.write_shift(4,r),a.write_shift(1,120),a.length>a.l?a.slice(0,a.l):a)),ta(e,136)}}function vl(e,t){var r=ea();return ta(r,131),ta(r,128,function(e,t){t||(t=Zr(127));for(var r=0;4!=r;++r)t.write_shift(4,0);return ya("SheetJS",t),ya(a.version,t),ya(a.version,t),ya("7262",t),t.length>t.l?t.slice(0,t.l):t}()),ta(r,153,function(e,t){t||(t=Zr(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),Na(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}(e.Workbook&&e.Workbook.WBProps||null)),ml(r,e),function(e,t){ta(e,143);for(var r=0;r!=t.SheetNames.length;++r){ta(e,156,(a={Hidden:t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]},(n=void 0)||(n=Zr(127)),n.write_shift(4,a.Hidden),n.write_shift(4,a.iTabID),Ma(a.strRelID,n),ya(a.name.slice(0,31),n),n.length>n.l?n.slice(0,n.l):n))}var a,n;ta(e,144)}(r,e),ta(r,132),r.end()}function gl(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},a=[],n=!1;t||(t={}),t.biff=12;var s=[],i=[[]];return i.SheetNames=[],i.XTI=[],af[16]={n:"BrtFRTArchID$",f:pl},Qr(e,(function(e,o,c){switch(c){case 156:i.SheetNames.push(e.name),r.Sheets.push(e);break;case 153:r.WBProps=e;break;case 39:null!=e.Sheet&&(t.SID=e.Sheet),e.Ref=Zo(e.Ptg,0,null,i,t),delete t.SID,delete e.Ptg,s.push(e);break;case 1036:case 361:case 2071:case 158:case 143:case 664:case 353:case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:case 16:break;case 357:case 358:case 355:case 667:i[0].length?i.push([c,e]):i[0]=[c,e],i[i.length-1].XTI=[];break;case 362:0===i.length&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(e),i.XTI=i.XTI.concat(e);break;case 35:case 37:a.push(c),n=!0;break;case 36:case 38:a.pop(),n=!1;break;default:if(o.T);else if(!n||t.WTF&&37!=a[a.length-1]&&35!=a[a.length-1])throw new Error("Unexpected record 0x"+c.toString(16))}}),t),cl(r),r.Names=s,r.supbooks=i,r}(e,r):function(e,t){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",s={},i=0;if(e.replace(vt,(function(o,c){var l=wt(o);switch(Tt(l[0])){case"<?xml":case"</workbook>":case"<fileVersion/>":case"</fileVersion>":case"<fileSharing":case"<fileSharing/>":case"</workbookPr>":case"<workbookProtection":case"<workbookProtection/>":case"<bookViews":case"<bookViews>":case"</bookViews>":case"</workbookView>":case"<sheets":case"<sheets>":case"</sheets>":case"</sheet>":case"<functionGroups":case"<functionGroups/>":case"<functionGroup":case"<externalReferences":case"</externalReferences>":case"<externalReferences>":case"<externalReference":case"<definedNames/>":case"<definedName/>":case"</calcPr>":case"<oleSize":case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":case"<customWorkbookView":case"</customWorkbookView>":case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":case"<pivotCache":case"<smartTagPr":case"<smartTagPr/>":case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":case"<smartTagType":case"<webPublishing":case"<webPublishing/>":case"<fileRecoveryPr":case"<fileRecoveryPr/>":case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":case"<webPublishObject":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":case"<ArchID":case"<revisionPtr":break;case"<workbook":o.match(ul)&&(n="xmlns"+o.match(/<(\w+):/)[1]),r.xmlns=l[n];break;case"<fileVersion":delete l[0],r.AppVersion=l;break;case"<workbookPr":case"<workbookPr/>":rl.forEach((function(e){if(null!=l[e[0]])switch(e[2]){case"bool":r.WBProps[e[0]]=It(l[e[0]]);break;case"int":r.WBProps[e[0]]=parseInt(l[e[0]],10);break;default:r.WBProps[e[0]]=l[e[0]]}})),l.codeName&&(r.WBProps.CodeName=Lt(l.codeName));break;case"<workbookView":case"<workbookView/>":delete l[0],r.WBView.push(l);break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=St(Lt(l.name)),delete l[0],r.Sheets.push(l);break;case"<definedNames>":case"<definedNames":case"<ext":case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</definedNames>":case"</ext>":case"</AlternateContent>":a=!1;break;case"<definedName":(s={}).Name=Lt(l.name),l.comment&&(s.Comment=l.comment),l.localSheetId&&(s.Sheet=+l.localSheetId),It(l.hidden||"0")&&(s.Hidden=!0),i=c+o.length;break;case"</definedName>":s.Ref=St(Lt(e.slice(i,c))),r.Names.push(s);break;case"<calcPr":case"<calcPr/>":delete l[0],r.CalcPr=l;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+l[0]+" in workbook")}return o})),-1===fr.indexOf(r.xmlns))throw new Error("Unknown Namespace: "+r.xmlns);return cl(r),r}(e,r)}function bl(e,t,r,a,n,s,i,o){return".bin"===t.slice(-4)?function(e,t,r,a,n,s,i){if(!e)return e;var o=t||{};a||(a={"!id":{}});var c,l,f,h,u,d,p,m,v,g,b=o.dense?[]:{},w={s:{r:2e6,c:2e6},e:{r:0,c:0}},T=[],E=!1,y=!1,S=[];o.biff=12,o["!row"]=0;var k=0,_=!1,A=[],x={},C=o.supbooks||n.supbooks||[[]];if(C.sharedf=x,C.arrayf=A,C.SheetNames=n.SheetNames||n.Sheets.map((function(e){return e.name})),!o.supbooks&&(o.supbooks=C,n.Names))for(var O=0;O<n.Names.length;++O)C[0][O+1]=n.Names[O];var R,I,N=[],F=[],D=!1;if(af[16]={n:"BrtShortReal",f:Vc},Qr(e,(function(e,t,O){if(!y)switch(O){case 148:c=e;break;case 0:l=e,o.sheetRows&&o.sheetRows<=l.r&&(y=!0),v=oa(u=l.r),o["!row"]=l.r,(e.hidden||e.hpt||null!=e.level)&&(e.hpt&&(e.hpx=Fi(e.hpt)),F[e.r]=e);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(f={t:e[2]},e[2]){case"n":f.v=e[1];break;case"s":m=dc[e[1]],f.v=m.t,f.r=m.r;break;case"b":f.v=!!e[1];break;case"e":f.v=e[1],!1!==o.cellText&&(f.w=tn[f.v]);break;case"str":f.t="s",f.v=e[1];break;case"is":f.t="s",f.v=e[1].t}if((h=i.CellXf[e[0].iStyleRef])&&Tc(f,h.numFmtId,null,o,s,i),d=-1==e[0].c?d+1:e[0].c,o.dense?(b[u]||(b[u]=[]),b[u][d]=f):b[la(d)+v]=f,o.cellFormula){for(_=!1,k=0;k<A.length;++k){var P=A[k];l.r>=P[0].s.r&&l.r<=P[0].e.r&&d>=P[0].s.c&&d<=P[0].e.c&&(f.F=da(P[0]),_=!0)}!_&&e.length>3&&(f.f=e[3])}if(w.s.r>l.r&&(w.s.r=l.r),w.s.c>d&&(w.s.c=d),w.e.r<l.r&&(w.e.r=l.r),w.e.c<d&&(w.e.c=d),o.cellDates&&h&&"n"==f.t&&ve(H[h.numFmtId])){var L=j(f.v);L&&(f.t="d",f.v=new Date(L.y,L.m-1,L.d,L.H,L.M,L.S,L.u))}R&&("XLDAPR"==R.type&&(f.D=!0),R=void 0),I&&(I=void 0);break;case 1:case 12:if(!o.sheetStubs||E)break;f={t:"z",v:void 0},d=-1==e[0].c?d+1:e[0].c,o.dense?(b[u]||(b[u]=[]),b[u][d]=f):b[la(d)+v]=f,w.s.r>l.r&&(w.s.r=l.r),w.s.c>d&&(w.s.c=d),w.e.r<l.r&&(w.e.r=l.r),w.e.c<d&&(w.e.c=d),R&&("XLDAPR"==R.type&&(f.D=!0),R=void 0),I&&(I=void 0);break;case 176:S.push(e);break;case 49:R=((o.xlmeta||{}).Cell||[])[e-1];break;case 494:var M=a["!id"][e.relId];for(M?(e.Target=M.Target,e.loc&&(e.Target+="#"+e.loc),e.Rel=M):""==e.relId&&(e.Target="#"+e.loc),u=e.rfx.s.r;u<=e.rfx.e.r;++u)for(d=e.rfx.s.c;d<=e.rfx.e.c;++d)o.dense?(b[u]||(b[u]=[]),b[u][d]||(b[u][d]={t:"z",v:void 0}),b[u][d].l=e):(p=ha({c:d,r:u}),b[p]||(b[p]={t:"z",v:void 0}),b[p].l=e);break;case 426:if(!o.cellFormula)break;A.push(e),(g=o.dense?b[u][d]:b[la(d)+v]).f=Zo(e[1],0,{r:l.r,c:d},C,o),g.F=da(e[0]);break;case 427:if(!o.cellFormula)break;x[ha(e[0].s)]=e[1],(g=o.dense?b[u][d]:b[la(d)+v]).f=Zo(e[1],0,{r:l.r,c:d},C,o);break;case 60:if(!o.cellStyles)break;for(;e.e>=e.s;)N[e.e--]={width:e.w/256,hidden:!!(1&e.flags),level:e.level},D||(D=!0,Ri(e.w/256)),Ii(N[e.e+1]);break;case 161:b["!autofilter"]={ref:da(e)};break;case 476:b["!margins"]=e;break;case 147:n.Sheets[r]||(n.Sheets[r]={}),e.name&&(n.Sheets[r].CodeName=e.name),(e.above||e.left)&&(b["!outline"]={above:e.above,left:e.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),e.RTL&&(n.Views[0].RTL=!0);break;case 485:case 64:case 1053:case 151:case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:E=!0;break;case 36:E=!1;break;case 37:T.push(O),E=!0;break;case 38:T.pop(),E=!1;break;default:if(t.T);else if(!E||o.WTF)throw new Error("Unexpected record 0x"+O.toString(16))}}),o),delete o.supbooks,delete o["!row"],!b["!ref"]&&(w.s.r<2e6||c&&(c.e.r>0||c.e.c>0||c.s.r>0||c.s.c>0))&&(b["!ref"]=da(c||w)),o.sheetRows&&b["!ref"]){var P=pa(b["!ref"]);o.sheetRows<=+P.e.r&&(P.e.r=o.sheetRows-1,P.e.r>w.e.r&&(P.e.r=w.e.r),P.e.r<P.s.r&&(P.s.r=P.e.r),P.e.c>w.e.c&&(P.e.c=w.e.c),P.e.c<P.s.c&&(P.s.c=P.e.c),b["!fullref"]=b["!ref"],b["!ref"]=da(P))}return S.length>0&&(b["!merges"]=S),N.length>0&&(b["!cols"]=N),F.length>0&&(b["!rows"]=F),b}(e,a,r,n,s,i,o):Nc(e,a,r,n,s,i,o)}function wl(e,t,r,a,n,s,i,o){return".bin"===t.slice(-4)?function(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i=[],o=!1;return Qr(e,(function(e,a,c){switch(c){case 550:s["!rel"]=e;break;case 651:n.Sheets[r]||(n.Sheets[r]={}),e.name&&(n.Sheets[r].CodeName=e.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:o=!0;break;case 36:o=!1;break;case 37:i.push(c);break;case 38:i.pop();break;default:if(a.T>0)i.push(c);else if(a.T<0)i.pop();else if(!o||t.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}}),t),a["!id"][s["!rel"]]&&(s["!drawel"]=a["!id"][s["!rel"]]),s}(e,a,r,n,s):function(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s,i={"!type":"chart","!drawel":null,"!rel":""},o=e.match(Oc);return o&&Fc(o[0],0,n,r),(s=e.match(/drawing r:id="(.*?)"/))&&(i["!rel"]=s[1]),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}(e,0,r,n,s)}function Tl(e,t,r,a){return".bin"===t.slice(-4)?function(e,t,r){var a={NumberFmt:[]};for(var n in H)a.NumberFmt[n]=H[n];a.CellXf=[],a.Fonts=[];var s=[],i=!1;return Qr(e,(function(e,n,o){switch(o){case 44:a.NumberFmt[e[0]]=e[1],Ee(e[1],e[0]);break;case 43:a.Fonts.push(e),null!=e.color.theme&&t&&t.themeElements&&t.themeElements.clrScheme&&(e.color.rgb=ki(t.themeElements.clrScheme[e.color.theme].rgb,e.color.tint||0));break;case 1025:case 45:case 46:case 48:case 507:case 572:case 475:case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 47:617==s[s.length-1]&&a.CellXf.push(e);break;case 35:i=!0;break;case 36:i=!1;break;case 37:s.push(o),i=!0;break;case 38:s.pop(),i=!1;break;default:if(n.T>0)s.push(o);else if(n.T<0)s.pop();else if(!i||r.WTF&&37!=s[s.length-1])throw new Error("Unexpected record 0x"+o.toString(16))}})),a}(e,r,a):Mi(e,r,a)}function El(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r=[],a=!1;return Qr(e,(function(e,n,s){switch(s){case 159:r.Count=e[0],r.Unique=e[1];break;case 19:r.push(e);break;case 160:return!0;case 35:a=!0;break;case 36:a=!1;break;default:if(n.T,!a||t.WTF)throw new Error("Unexpected record 0x"+s.toString(16))}})),r}(e,r):function(e,t){var r=[],a="";if(!e)return r;var n=e.match(ri);if(n){a=n[2].replace(ai,"").split(ni);for(var s=0;s!=a.length;++s){var i=ti(a[s].trim(),t);null!=i&&(r[r.length]=i)}n=wt(n[1]),r.Count=n.count,r.Unique=n.uniqueCount}return r}(e,r)}function yl(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r=[],a=[],n={},s=!1;return Qr(e,(function(e,i,o){switch(o){case 632:a.push(e);break;case 635:n=e;break;case 637:n.t=e.t,n.h=e.h,n.r=e.r;break;case 636:if(n.author=a[n.iauthor],delete n.iauthor,t.sheetRows&&n.rfx&&t.sheetRows<=n.rfx.r)break;n.t||(n.t=""),delete n.rfx,r.push(n);break;case 3072:case 37:case 38:break;case 35:s=!0;break;case 36:s=!1;break;default:if(i.T);else if(!s||t.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}})),r}(e,r):function(e,t){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],a=[],n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);n&&n[1]&&n[1].split(/<\/\w*:?author>/).forEach((function(e){if(""!==e&&""!==e.trim()){var t=e.match(/<(?:\w+:)?author[^>]*>(.*)/);t&&r.push(t[1])}}));var s=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return s&&s[1]&&s[1].split(/<\/\w*:?comment>/).forEach((function(e){if(""!==e&&""!==e.trim()){var n=e.match(/<(?:\w+:)?comment[^>]*>/);if(n){var s=wt(n[0]),i={author:s.authorId&&r[s.authorId]||"sheetjsghost",ref:s.ref,guid:s.guid},o=fa(s.ref);if(!(t.sheetRows&&t.sheetRows<=o.r)){var c=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),l=!!c&&!!c[1]&&ti(c[1])||{r:"",t:"",h:""};i.r=l.r,"<t></t>"==l.r&&(l.t=l.h=""),i.t=(l.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),t.cellHTML&&(i.h=l.h),a.push(i)}}}})),a}(e,r)}function Sl(e,t,r){return".bin"===t.slice(-4)?function(e,t,r){var a=[];return Qr(e,(function(e,t,r){if(63===r)a.push(e);else if(!t.T)throw new Error("Unexpected record 0x"+r.toString(16))})),a}(e):function(e){var t=[];if(!e)return t;var r=1;return(e.match(vt)||[]).forEach((function(e){var a=wt(e);switch(a[0]){case"<?xml":case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete a[0],a.i?r=a.i:a.i=r,t.push(a)}})),t}(e)}function kl(e,t,r,a){if(".bin"===r.slice(-4))return function(e,t,r,a){if(!e)return e;var n=a||{},s=!1;Qr(e,(function(e,t,r){switch(r){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:s=!0;break;case 36:s=!1;break;default:if(t.T);else if(!s||n.WTF)throw new Error("Unexpected record 0x"+r.toString(16))}}),n)}(e,0,0,a)}function _l(e,t,r){return".bin"===t.slice(-4)?function(e,t,r){var a={Types:[],Cell:[],Value:[]},n=r||{},s=[],i=!1,o=2;return Qr(e,(function(e,t,r){switch(r){case 335:a.Types.push({name:e.name});break;case 51:e.forEach((function(e){1==o?a.Cell.push({type:a.Types[e[0]-1].name,index:e[1]}):0==o&&a.Value.push({type:a.Types[e[0]-1].name,index:e[1]})}));break;case 337:o=e?1:0;break;case 338:o=2;break;case 35:s.push(r),i=!0;break;case 36:s.pop(),i=!1;break;default:if(t.T);else if(!i||n.WTF&&35!=s[s.length-1])throw new Error("Unexpected record 0x"+r.toString(16))}})),a}(e,0,r):function(e,t,r){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n,s=!1,i=2;return e.replace(vt,(function(e){var t=wt(e);switch(Tt(t[0])){case"<?xml":case"<metadata":case"</metadata>":case"<metadataTypes":case"</metadataTypes>":case"</metadataType>":case"</futureMetadata>":case"<bk>":case"</bk>":case"</rc>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<metadataType":a.Types.push({name:t.name});break;case"<futureMetadata":for(var o=0;o<a.Types.length;++o)a.Types[o].name==t.name&&(n=a.Types[o]);break;case"<rc":1==i?a.Cell.push({type:a.Types[t.t-1].name,index:+t.v}):0==i&&a.Value.push({type:a.Types[t.t-1].name,index:+t.v});break;case"<cellMetadata":i=1;break;case"</cellMetadata>":case"</valueMetadata>":i=2;break;case"<valueMetadata":i=0;break;case"<ext":s=!0;break;case"</ext>":s=!1;break;case"<rvb":if(!n)break;n.offsets||(n.offsets=[]),n.offsets.push(+t.i);break;default:if(!s&&r.WTF)throw new Error("unrecognized "+t[0]+" in metadata")}return e})),a}(e,0,r)}function Al(e,t,r,a,n){return(".bin"===t.slice(-4)?el:Bc)(e,r,a,n)}function xl(e,t,r){return(".bin"===t.slice(-4)?wo:vo)(e,r)}var Cl,Ol=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,Rl=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function Il(e,t){var r=e.split(/\s+/),a=[];if(t||(a[0]=r[0]),1===r.length)return a;var n,s,i,o=e.match(Ol);if(o)for(i=0;i!=o.length;++i)-1===(s=(n=o[i].match(Rl))[1].indexOf(":"))?a[n[1]]=n[2].slice(1,n[2].length-1):a["xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(s+1)]=n[2].slice(1,n[2].length-1);return a}function Nl(e){var t={};if(1===e.split(/\s+/).length)return t;var r,a,n,s=e.match(Ol);if(s)for(n=0;n!=s.length;++n)-1===(a=(r=s[n].match(Rl))[1].indexOf(":"))?t[r[1]]=r[2].slice(1,r[2].length-1):t["xmlns:"===r[1].slice(0,6)?"xmlns"+r[1].slice(6):r[1].slice(a+1)]=r[2].slice(1,r[2].length-1);return t}function Fl(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=It(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=$e(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[St(t)]=n}function Dl(e,t,r){if("z"!==e.t){if(!r||!1!==r.cellText)try{"e"===e.t?e.w=e.w||tn[e.v]:"General"===t?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Z(e.v):e.w=Q(e.v):e.w=function(e,t){var r=Cl[e]||St(e);return"General"===r?Q(t):Te(r,t)}(t||"General",e.v)}catch(s){if(r.WTF)throw s}try{var a=Cl[t]||t||"General";if(r.cellNF&&(e.z=a),r.cellDates&&"n"==e.t&&ve(a)){var n=j(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}catch(s){if(r.WTF)throw s}}}function Pl(e,t,r){if(r.cellStyles&&t.Interior){var a=t.Interior;a.Pattern&&(a.patternType=Di[a.Pattern]||a.Pattern)}e[t.ID]=t}function Ll(e,t,r,a,n,s,i,o,c,l){var f="General",h=a.StyleID,u={};l=l||{};var d=[],p=0;for(void 0===h&&o&&(h=o.StyleID),void 0===h&&i&&(h=i.StyleID);void 0!==s[h]&&(s[h].nf&&(f=s[h].nf),s[h].Interior&&d.push(s[h].Interior),s[h].Parent);)h=s[h].Parent;switch(r.Type){case"Boolean":a.t="b",a.v=It(e);break;case"String":a.t="s",a.r=Rt(St(e)),a.v=e.indexOf("<")>-1?St(t||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),a.v=($e(e)-new Date(Date.UTC(1899,11,30)))/864e5,a.v!==a.v?a.v=St(e):a.v<60&&(a.v=a.v-1),f&&"General"!=f||(f="yyyy-mm-dd");case"Number":void 0===a.v&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=rn[e],!1!==l.cellText&&(a.w=e);break;default:""==e&&""==t?a.t="z":(a.t="s",a.v=Rt(t||e))}if(Dl(a,f,l),!1!==l.cellFormula)if(a.Formula){var m=St(a.Formula);61==m.charCodeAt(0)&&(m=m.slice(1)),a.f=Eo(m,n),delete a.Formula,"RC"==a.ArrayRange?a.F=Eo("RC:RC",n):a.ArrayRange&&(a.F=Eo(a.ArrayRange,n),c.push([pa(a.F),a.F]))}else for(p=0;p<c.length;++p)n.r>=c[p][0].s.r&&n.r<=c[p][0].e.r&&n.c>=c[p][0].s.c&&n.c<=c[p][0].e.c&&(a.F=c[p][1]);l.cellStyles&&(d.forEach((function(e){!u.patternType&&e.patternType&&(u.patternType=e.patternType)})),a.s=u),void 0!==a.StyleID&&(a.ixfe=a.StyleID)}function Ml(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),e.v=e.w=e.ixfe=void 0}function Ul(e,t){var r=t||{};Se();var a=p(Kt(e));"binary"!=r.type&&"array"!=r.type&&"base64"!=r.type||(a="undefined"!==typeof d?d.utils.decode(65001,h(a)):Lt(a));var n,s=a.slice(0,1024).toLowerCase(),i=!1;if((1023&(s=s.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&s.indexOf(","),1023&s.indexOf(";"))){var o=Ke(r);return o.type="string",Ys.to_workbook(a,o)}if(-1==s.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach((function(e){s.indexOf("<"+e)>=0&&(i=!0)})),i)return function(e,t){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||0==r.length)throw new Error("Invalid HTML: could not find <table>");if(1==r.length)return ga(vf(r[0],t),t);var a={SheetNames:[],Sheets:{}};return r.forEach((function(e,r){Bh(a,vf(e,t),"Sheet"+(r+1))})),a}(a,r);Cl={"General Number":"General","General Date":H[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":H[15],"Short Date":H[14],"Long Time":H[19],"Medium Time":H[18],"Short Time":H[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:H[2],Standard:H[4],Percent:H[10],Scientific:H[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var c,l=[];var f,u={},m=[],v=r.dense?[]:{},g="",b={},w={},T=Il('<Data ss:Type="String">'),E=0,y=0,S=0,k={s:{r:2e6,c:2e6},e:{r:0,c:0}},_={},A={},x="",C=0,O=[],R={},I={},N=0,F=[],D=[],P={},L=[],M=!1,U=[],B=[],W={},z=0,V=0,G={Sheets:[],WBProps:{date1904:!1}},j={};Jt.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/gm,"");for(var X="";n=Jt.exec(a);)switch(n[3]=(X=n[3]).toLowerCase()){case"data":if("data"==X){if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&l.push([n[3],!0]);break}if(l[l.length-1][1])break;"/"===n[1]?Ll(a.slice(E,n.index),x,T,"comment"==l[l.length-1][0]?P:b,{c:y,r:S},_,L[y],w,U,r):(x="",T=Il(n[0]),E=n.index+n[0].length);break;case"cell":if("/"===n[1])if(D.length>0&&(b.c=D),(!r.sheetRows||r.sheetRows>S)&&void 0!==b.v&&(r.dense?(v[S]||(v[S]=[]),v[S][y]=b):v[la(y)+oa(S)]=b),b.HRef&&(b.l={Target:St(b.HRef)},b.HRefScreenTip&&(b.l.Tooltip=b.HRefScreenTip),delete b.HRef,delete b.HRefScreenTip),(b.MergeAcross||b.MergeDown)&&(z=y+(0|parseInt(b.MergeAcross,10)),V=S+(0|parseInt(b.MergeDown,10)),O.push({s:{c:y,r:S},e:{c:z,r:V}})),r.sheetStubs)if(b.MergeAcross||b.MergeDown){for(var $=y;$<=z;++$)for(var Y=S;Y<=V;++Y)($>y||Y>S)&&(r.dense?(v[Y]||(v[Y]=[]),v[Y][$]={t:"z"}):v[la($)+oa(Y)]={t:"z"});y=z+1}else++y;else b.MergeAcross?y=z+1:++y;else(b=Nl(n[0])).Index&&(y=+b.Index-1),y<k.s.c&&(k.s.c=y),y>k.e.c&&(k.e.c=y),"/>"===n[0].slice(-2)&&++y,D=[];break;case"row":"/"===n[1]||"/>"===n[0].slice(-2)?(S<k.s.r&&(k.s.r=S),S>k.e.r&&(k.e.r=S),"/>"===n[0].slice(-2)&&(w=Il(n[0])).Index&&(S=+w.Index-1),y=0,++S):((w=Il(n[0])).Index&&(S=+w.Index-1),W={},("0"==w.AutoFitHeight||w.Height)&&(W.hpx=parseInt(w.Height,10),W.hpt=Ni(W.hpx),B[S]=W),"1"==w.Hidden&&(W.hidden=!0,B[S]=W));break;case"worksheet":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"));m.push(g),k.s.r<=k.e.r&&k.s.c<=k.e.c&&(v["!ref"]=da(k),r.sheetRows&&r.sheetRows<=k.e.r&&(v["!fullref"]=v["!ref"],k.e.r=r.sheetRows-1,v["!ref"]=da(k))),O.length&&(v["!merges"]=O),L.length>0&&(v["!cols"]=L),B.length>0&&(v["!rows"]=B),u[g]=v}else k={s:{r:2e6,c:2e6},e:{r:0,c:0}},S=y=0,l.push([n[3],!1]),c=Il(n[0]),g=St(c.Name),v=r.dense?[]:{},O=[],U=[],B=[],j={name:g,Hidden:0},G.Sheets.push(j);break;case"table":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else{if("/>"==n[0].slice(-2))break;l.push([n[3],!1]),L=[],M=!1}break;case"style":"/"===n[1]?Pl(_,A,r):A=Il(n[0]);break;case"numberformat":A.nf=St(Il(n[0]).Format||"General"),Cl[A.nf]&&(A.nf=Cl[A.nf]);for(var K=0;392!=K&&H[K]!=A.nf;++K);if(392==K)for(K=57;392!=K;++K)if(null==H[K]){Ee(A.nf,K);break}break;case"column":if("table"!==l[l.length-1][0])break;if((f=Il(n[0])).Hidden&&(f.hidden=!0,delete f.Hidden),f.Width&&(f.wpx=parseInt(f.Width,10)),!M&&f.wpx>10){M=!0,_i=6;for(var J=0;J<L.length;++J)L[J]&&Ii(L[J])}M&&Ii(f),L[f.Index-1||L.length]=f;for(var q=0;q<+f.Span;++q)L[L.length]=Ke(f);break;case"namedrange":if("/"===n[1])break;G.Names||(G.Names=[]);var Z=wt(n[0]),Q={Name:Z.Name,Ref:Eo(Z.RefersTo.slice(1),{r:0,c:0})};G.Sheets.length>0&&(Q.Sheet=G.Sheets.length-1),G.Names.push(Q);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":if("/>"===n[0].slice(-2))break;"/"===n[1]?x+=a.slice(C,n.index):C=n.index+n[0].length;break;case"interior":if(!r.cellStyles)break;A.Interior=Il(n[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if("/>"===n[0].slice(-2))break;"/"===n[1]?xn(R,X,a.slice(N,n.index)):N=n.index+n[0].length;break;case"styles":case"workbook":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else l.push([n[3],!1]);break;case"comment":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"));Ml(P),D.push(P)}else l.push([n[3],!1]),P={a:(c=Il(n[0])).Author};break;case"autofilter":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else if("/"!==n[0].charAt(n[0].length-2)){var ee=Il(n[0]);v["!autofilter"]={ref:Eo(ee.Range).replace(/\$/g,"")},l.push([n[3],!0])}break;case"datavalidation":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&l.push([n[3],!0]);break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&l.push([n[3],!0]);break;default:if(0==l.length&&"document"==n[3])return Of(a,r);if(0==l.length&&"uof"==n[3])return Of(a,r);var te=!0;switch(l[l.length-1][0]){case"officedocumentsettings":switch(n[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:te=!1}break;case"componentoptions":switch(n[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:te=!1}break;case"excelworkbook":switch(n[3]){case"date1904":G.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:te=!1}break;case"workbookoptions":switch(n[3]){case"owcversion":case"height":case"width":break;default:te=!1}break;case"worksheetoptions":switch(n[3]){case"visible":if("/>"===n[0].slice(-2));else if("/"===n[1])switch(a.slice(N,n.index)){case"SheetHidden":j.Hidden=1;break;case"SheetVeryHidden":j.Hidden=2}else N=n.index+n[0].length;break;case"header":v["!margins"]||bc(v["!margins"]={},"xlml"),isNaN(+wt(n[0]).Margin)||(v["!margins"].header=+wt(n[0]).Margin);break;case"footer":v["!margins"]||bc(v["!margins"]={},"xlml"),isNaN(+wt(n[0]).Margin)||(v["!margins"].footer=+wt(n[0]).Margin);break;case"pagemargins":var re=wt(n[0]);v["!margins"]||bc(v["!margins"]={},"xlml"),isNaN(+re.Top)||(v["!margins"].top=+re.Top),isNaN(+re.Left)||(v["!margins"].left=+re.Left),isNaN(+re.Right)||(v["!margins"].right=+re.Right),isNaN(+re.Bottom)||(v["!margins"].bottom=+re.Bottom);break;case"displayrighttoleft":G.Views||(G.Views=[]),G.Views[0]||(G.Views[0]={}),G.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":v["!outline"]||(v["!outline"]={}),v["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":v["!outline"]||(v["!outline"]={}),v["!outline"].left=!0;break;default:te=!1}break;case"pivottable":case"pivotcache":switch(n[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:te=!1}break;case"pagebreaks":switch(n[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:te=!1}break;case"autofilter":switch(n[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:te=!1}break;case"querytable":switch(n[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:te=!1}break;case"datavalidation":switch(n[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:te=!1}break;case"sorting":case"conditionalformatting":switch(n[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:te=!1}break;case"mapinfo":case"schema":case"data":switch(n[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:te=!1}break;case"smarttags":break;default:te=!1}if(te)break;if(n[3].match(/!\[CDATA/))break;if(!l[l.length-1][1])throw"Unrecognized tag: "+n[3]+"|"+l.join("|");if("customdocumentproperties"===l[l.length-1][0]){if("/>"===n[0].slice(-2))break;"/"===n[1]?Fl(I,X,F,a.slice(N,n.index)):(F=n,N=n.index+n[0].length);break}if(r.WTF)throw"Unrecognized tag: "+n[3]+"|"+l.join("|")}var ae={};return r.bookSheets||r.bookProps||(ae.Sheets=u),ae.SheetNames=m,ae.Workbook=G,ae.SSF=Ke(H),ae.Props=R,ae.Custprops=I,ae}function Bl(e,t){switch(oh(t=t||{}),t.type||"base64"){case"base64":return Ul(T(e),t);case"binary":case"buffer":case"file":return Ul(e,t);case"array":return Ul(x(e),t)}}function Wl(e,t){var r=[];return e.Props&&r.push(function(e,t){var r=[];return Fe(An).map((function(e){for(var t=0;t<pn.length;++t)if(pn[t][1]==e)return pn[t];for(t=0;t<wn.length;++t)if(wn[t][1]==e)return wn[t];throw e})).forEach((function(a){if(null!=e[a[1]]){var n=t&&t.Props&&null!=t.Props[a[1]]?t.Props[a[1]]:e[a[1]];"date"===a[2]&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof n?n=String(n):!0===n||!1===n?n=n?"1":"0":n instanceof Date&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"")),r.push(jt(An[a[1]]||a[1],n))}})),$t("DocumentProperties",r.join(""),{xmlns:hr})}(e.Props,t)),e.Custprops&&r.push(function(e,t){var r=["Worksheets","SheetNames"],a="CustomDocumentProperties",n=[];return e&&Fe(e).forEach((function(t){if(Object.prototype.hasOwnProperty.call(e,t)){for(var a=0;a<pn.length;++a)if(t==pn[a][1])return;for(a=0;a<wn.length;++a)if(t==wn[a][1])return;for(a=0;a<r.length;++a)if(t==r[a])return;var s=e[t],i="string";"number"==typeof s?(i="float",s=String(s)):!0===s||!1===s?(i="boolean",s=s?"1":"0"):s=String(s),n.push($t(xt(t),s,{"dt:dt":i}))}})),t&&Fe(t).forEach((function(r){if(Object.prototype.hasOwnProperty.call(t,r)&&(!e||!Object.prototype.hasOwnProperty.call(e,r))){var a=t[r],s="string";"number"==typeof a?(s="float",a=String(a)):!0===a||!1===a?(s="boolean",a=a?"1":"0"):a instanceof Date?(s="dateTime.tz",a=a.toISOString()):a=String(a),n.push($t(xt(r),a,{"dt:dt":s}))}})),"<"+a+' xmlns="'+hr+'">'+n.join("")+"</"+a+">"}(e.Props,e.Custprops)),r.join("")}function Hl(e){return $t("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+So(e.Ref,{r:0,c:0})})}function zl(e,t,r,a,n,s,i){if(!e||void 0==e.v&&void 0==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+At(So(e.f,i))),e.F&&e.F.slice(0,t.length)==t){var c=fa(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(c.r==i.r?"":"["+(c.r-i.r)+"]")+"C"+(c.c==i.c?"":"["+(c.c-i.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=At(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=At(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],f=0;f!=l.length;++f)l[f].s.c==i.c&&l[f].s.r==i.r&&(l[f].e.c>l[f].s.c&&(o["ss:MergeAcross"]=l[f].e.c-l[f].s.c),l[f].e.r>l[f].s.r&&(o["ss:MergeDown"]=l[f].e.r-l[f].s.r));var h="",u="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=tn[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||H[14]);break;case"s":h="String",u=((e.v||"")+"").replace(kt,(function(e){return yt[e]})).replace(Ct,(function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"}))}var d=wc(a.cellXfs,e,a);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=i.c+1;var p=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+h+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=e.c.map((function(e){var t=$t("ss:Data",(e.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"});return $t("Comment",t,{"ss:Author":e.a})})).join("")),$t("Cell",m,o)}function Vl(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=Fi(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function Gl(e,t,r){var a=[],n=r.SheetNames[e],s=r.Sheets[n],i=s?function(e,t,r,a){if(!e)return"";if(!((a||{}).Workbook||{}).Names)return"";for(var n=a.Workbook.Names,s=[],i=0;i<n.length;++i){var o=n[i];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||s.push(Hl(o)))}return s.join("")}(s,0,e,r):"";return i.length>0&&a.push("<Names>"+i+"</Names>"),i=s?function(e,t,r,a){if(!e["!ref"])return"";var n=pa(e["!ref"]),s=e["!merges"]||[],i=0,o=[];e["!cols"]&&e["!cols"].forEach((function(e,t){Ii(e);var r=!!e.width,a=gc(t,e),n={"ss:Index":t+1};r&&(n["ss:Width"]=Ai(a.width)),e.hidden&&(n["ss:Hidden"]="1"),o.push($t("Column",null,n))}));for(var c=Array.isArray(e),l=n.s.r;l<=n.e.r;++l){for(var f=[Vl(l,(e["!rows"]||[])[l])],h=n.s.c;h<=n.e.c;++h){var u=!1;for(i=0;i!=s.length;++i)if(!(s[i].s.c>h)&&!(s[i].s.r>l)&&!(s[i].e.c<h)&&!(s[i].e.r<l)){s[i].s.c==h&&s[i].s.r==l||(u=!0);break}if(!u){var d={r:l,c:h},p=ha(d),m=c?(e[l]||[])[h]:e[p];f.push(zl(m,p,e,t,0,0,d))}}f.push("</Row>"),f.length>2&&o.push(f.join(""))}return o.join("")}(s,t):"",i.length>0&&a.push("<Table>"+i+"</Table>"),a.push(function(e,t,r,a){if(!e)return"";var n=[];if(e["!margins"]&&(n.push("<PageSetup>"),e["!margins"].header&&n.push($t("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&n.push($t("Footer",null,{"x:Margin":e["!margins"].footer})),n.push($t("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),n.push("</PageSetup>")),a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[r])if(a.Workbook.Sheets[r].Hidden)n.push($t("Visible",1==a.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var s=0;s<r&&(!a.Workbook.Sheets[s]||a.Workbook.Sheets[s].Hidden);++s);s==r&&n.push("<Selected/>")}return((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),e["!protect"]&&(n.push(jt("ProtectContents","True")),e["!protect"].objects&&n.push(jt("ProtectObjects","True")),e["!protect"].scenarios&&n.push(jt("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||n.push(jt("EnableSelection","UnlockedCells")):n.push(jt("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach((function(t){e["!protect"][t[0]]&&n.push("<"+t[1]+"/>")}))),0==n.length?"":$t("WorksheetOptions",n.join(""),{xmlns:ur})}(s,0,e,r)),a.join("")}function jl(e,t){t||(t={}),e.SSF||(e.SSF=Ke(H)),e.SSF&&(Se(),ye(e.SSF),t.revssf=Le(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],wc(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(Wl(e,t)),r.push(""),r.push(""),r.push("");for(var a=0;a<e.SheetNames.length;++a)r.push($t("Worksheet",Gl(a,t,e),{"ss:Name":At(e.SheetNames[a])}));return r[2]=function(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach((function(e,t){var a=[];a.push($t("NumberFormat",null,{"ss:Format":At(H[e.numFmtId])}));var n={"ss:ID":"s"+(21+t)};r.push($t("Style",a.join(""),n))})),$t("Styles",r.join(""))}(0,t),r[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],a=0;a<t.length;++a){var n=t[a];null==n.Sheet&&(n.Name.match(/^_xlfn\./)||r.push(Hl(n)))}return $t("Names",r.join(""))}(e),dt+$t("Workbook",r.join(""),{xmlns:dr,"xmlns:o":hr,"xmlns:x":ur,"xmlns:ss":dr,"xmlns:dt":pr,"xmlns:html":gr})}function Xl(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=function(e){return Xa(e,1)}(r),r.length-r.l<=4)return t;var a=r.read_shift(4);return 0==a||a>40?t:(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4||1907505652!==(a=r.read_shift(4))?t:(t.UnicodeClipboardFormat=function(e){return Xa(e,2)}(r),0==(a=r.read_shift(4))||a>40?t:(r.l-=4,void(t.Reserved2=r.read_shift(0,"lpwstr")))))}var $l=[60,1084,2066,2165,2175];function Yl(e,t,r,a,n){var s=a,i=[],o=r.slice(r.l,r.l+s);if(n&&n.enc&&n.enc.insitu&&o.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:case 133:break;default:n.enc.insitu(o)}i.push(o),r.l+=s;for(var c=Hr(r,r.l),l=nf[c],f=0;null!=l&&$l.indexOf(c)>-1;)s=Hr(r,r.l+2),f=r.l+4,2066==c?f+=4:2165!=c&&2175!=c||(f+=12),o=r.slice(f,r.l+4+s),i.push(o),r.l+=4+s,l=nf[c=Hr(r,r.l)];var h=O(i);Jr(h,0);var u=0;h.lens=[];for(var d=0;d<i.length;++d)h.lens.push(u),u+=i[d].length;if(h.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+a;return t.f(h,h.length,n)}function Kl(e,t,r){if("z"!==e.t&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=H[a])}catch(s){if(t.WTF)throw s}if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||tn[e.v]:0===a||"General"==a?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=Z(e.v):e.w=Q(e.v):e.w=Te(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(s){if(t.WTF)throw s}if(t.cellDates&&a&&"n"==e.t&&ve(H[a]||String(a))){var n=j(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function Jl(e,t,r){return{v:e,ixfe:t,t:r}}function ql(e,t){var r={opts:{}},a={};var n,s,i,o,c,f,h,u,d=t.dense?[]:{},p={},m={},v=null,g=[],b="",w={},T="",E={},y=[],S=[],k=[],_={Sheets:[],WBProps:{date1904:!1},Views:[{}]},A={},x=function(e){return e<8?en[e]:e<64&&k[e-8]||en[e]},C=function(e,t,r){if(!(U>1)&&!(r.sheetRows&&e.r>=r.sheetRows)){if(r.cellStyles&&t.XF&&t.XF.data&&function(e,t,r){var a,n=t.XF.data;n&&n.patternType&&r&&r.cellStyles&&(t.s={},t.s.patternType=n.patternType,(a=Si(x(n.icvFore)))&&(t.s.fgColor={rgb:a}),(a=Si(x(n.icvBack)))&&(t.s.bgColor={rgb:a}))}(0,t,r),delete t.ixfe,delete t.XF,n=e,T=ha(e),m&&m.s&&m.e||(m={s:{r:0,c:0},e:{r:0,c:0}}),e.r<m.s.r&&(m.s.r=e.r),e.c<m.s.c&&(m.s.c=e.c),e.r+1>m.e.r&&(m.e.r=e.r+1),e.c+1>m.e.c&&(m.e.c=e.c+1),r.cellFormula&&t.f)for(var a=0;a<y.length;++a)if(!(y[a][0].s.c>e.c||y[a][0].s.r>e.r)&&!(y[a][0].e.c<e.c||y[a][0].e.r<e.r)){t.F=da(y[a][0]),y[a][0].s.c==e.c&&y[a][0].s.r==e.r||delete t.f,t.f&&(t.f=""+Zo(y[a][1],0,e,P,O));break}r.dense?(d[e.r]||(d[e.r]=[]),d[e.r][e.c]=t):d[T]=t}},O={enc:!1,sbcch:0,snames:[],sharedf:E,arrayf:y,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(O.password=t.password);var R=[],I=[],N=[],F=[],D=!1,P=[];P.SheetNames=O.snames,P.sharedf=O.sharedf,P.arrayf=O.arrayf,P.names=[],P.XTI=[];var L,M=0,U=0,B=0,W=[],z=[];O.codepage=1200,l(1200);for(var V=!1;e.l<e.length-1;){var G=e.l,j=e.read_shift(2);if(0===j&&10===M)break;var X=e.l===e.length?0:e.read_shift(2),$=nf[j];if($&&$.f){if(t.bookSheets&&133===M&&133!==j)break;if(M=j,2===$.r||12==$.r){var Y=e.read_shift(2);if(X-=2,!O.enc&&Y!==j&&((255&Y)<<8|Y>>8)!==j)throw new Error("rt mismatch: "+Y+"!="+j);12==$.r&&(e.l+=10,X-=10)}var K={};if(K=10===j?$.f(e,X,O):Yl(j,$,e,X,O),0==U&&-1===[9,521,1033,2057].indexOf(M))continue;switch(j){case 34:r.opts.Date1904=_.WBProps.date1904=K;break;case 134:r.opts.WriteProtect=!0;break;case 47:if(O.enc||(e.l=0),O.enc=K,!t.password)throw new Error("File is password-protected");if(null==K.valid)throw new Error("Encryption scheme unsupported");if(!K.valid)throw new Error("Password is incorrect");break;case 92:O.lastuser=K;break;case 66:var J=Number(K);switch(J){case 21010:J=1200;break;case 32768:J=1e4;break;case 32769:J=1252}l(O.codepage=J),V=!0;break;case 317:O.rrtabid=K;break;case 25:O.winlocked=K;break;case 439:r.opts.RefreshAll=K;break;case 12:r.opts.CalcCount=K;break;case 16:r.opts.CalcDelta=K;break;case 17:r.opts.CalcIter=K;break;case 13:r.opts.CalcMode=K;break;case 14:r.opts.CalcPrecision=K;break;case 95:r.opts.CalcSaveRecalc=K;break;case 15:O.CalcRefMode=K;break;case 2211:r.opts.FullCalc=K;break;case 129:K.fDialog&&(d["!type"]="dialog"),K.fBelow||((d["!outline"]||(d["!outline"]={})).above=!0),K.fRight||((d["!outline"]||(d["!outline"]={})).left=!0);break;case 224:S.push(K);break;case 430:P.push([K]),P[P.length-1].XTI=[];break;case 35:case 547:P[P.length-1].push(K);break;case 24:case 536:L={Name:K.Name,Ref:Zo(K.rgce,0,null,P,O)},K.itab>0&&(L.Sheet=K.itab-1),P.names.push(L),P[0]||(P[0]=[],P[0].XTI=[]),P[P.length-1].push(K),"_xlnm._FilterDatabase"==K.Name&&K.itab>0&&K.rgce&&K.rgce[0]&&K.rgce[0][0]&&"PtgArea3d"==K.rgce[0][0][0]&&(z[K.itab-1]={ref:da(K.rgce[0][0][1][2])});break;case 22:O.ExternCount=K;break;case 23:0==P.length&&(P[0]=[],P[0].XTI=[]),P[P.length-1].XTI=P[P.length-1].XTI.concat(K),P.XTI=P.XTI.concat(K);break;case 2196:if(O.biff<8)break;null!=L&&(L.Comment=K[1]);break;case 18:d["!protect"]=K;break;case 19:0!==K&&O.WTF&&console.error("Password verifier: "+K);break;case 133:p[K.pos]=K,O.snames.push(K.name);break;case 10:if(--U)break;if(m.e){if(m.e.r>0&&m.e.c>0){if(m.e.r--,m.e.c--,d["!ref"]=da(m),t.sheetRows&&t.sheetRows<=m.e.r){var q=m.e.r;m.e.r=t.sheetRows-1,d["!fullref"]=d["!ref"],d["!ref"]=da(m),m.e.r=q}m.e.r++,m.e.c++}R.length>0&&(d["!merges"]=R),I.length>0&&(d["!objects"]=I),N.length>0&&(d["!cols"]=N),F.length>0&&(d["!rows"]=F),_.Sheets.push(A)}""===b?w=d:a[b]=d,d=t.dense?[]:{};break;case 9:case 521:case 1033:case 2057:if(8===O.biff&&(O.biff={9:2,521:3,1033:4}[j]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[K.BIFFVer]||8),O.biffguess=0==K.BIFFVer,0==K.BIFFVer&&4096==K.dt&&(O.biff=5,V=!0,l(O.codepage=28591)),8==O.biff&&0==K.BIFFVer&&16==K.dt&&(O.biff=2),U++)break;if(d=t.dense?[]:{},O.biff<8&&!V&&(V=!0,l(O.codepage=t.codepage||1252)),O.biff<5||0==K.BIFFVer&&4096==K.dt){""===b&&(b="Sheet1"),m={s:{r:0,c:0},e:{r:0,c:0}};var Z={pos:e.l-X,name:b};p[Z.pos]=Z,O.snames.push(b)}else b=(p[G]||{name:""}).name;32==K.dt&&(d["!type"]="chart"),64==K.dt&&(d["!type"]="macro"),R=[],I=[],O.arrayf=y=[],N=[],F=[],D=!1,A={Hidden:(p[G]||{hs:0}).hs,name:b};break;case 515:case 3:case 2:"chart"==d["!type"]&&(t.dense?(d[K.r]||[])[K.c]:d[ha({c:K.c,r:K.r})])&&++K.c,f={ixfe:K.ixfe,XF:S[K.ixfe]||{},v:K.val,t:"n"},B>0&&(f.z=W[f.ixfe>>8&63]),Kl(f,t,r.opts.Date1904),C({c:K.c,r:K.r},f,t);break;case 5:case 517:f={ixfe:K.ixfe,XF:S[K.ixfe],v:K.val,t:K.t},B>0&&(f.z=W[f.ixfe>>8&63]),Kl(f,t,r.opts.Date1904),C({c:K.c,r:K.r},f,t);break;case 638:f={ixfe:K.ixfe,XF:S[K.ixfe],v:K.rknum,t:"n"},B>0&&(f.z=W[f.ixfe>>8&63]),Kl(f,t,r.opts.Date1904),C({c:K.c,r:K.r},f,t);break;case 189:for(var Q=K.c;Q<=K.C;++Q){var ee=K.rkrec[Q-K.c][0];f={ixfe:ee,XF:S[ee],v:K.rkrec[Q-K.c][1],t:"n"},B>0&&(f.z=W[f.ixfe>>8&63]),Kl(f,t,r.opts.Date1904),C({c:Q,r:K.r},f,t)}break;case 6:case 518:case 1030:if("String"==K.val){v=K;break}if((f=Jl(K.val,K.cell.ixfe,K.tt)).XF=S[f.ixfe],t.cellFormula){var te=K.formula;if(te&&te[0]&&te[0][0]&&"PtgExp"==te[0][0][0]){var re=te[0][0][1][0],ae=te[0][0][1][1],ne=ha({r:re,c:ae});E[ne]?f.f=""+Zo(K.formula,0,K.cell,P,O):f.F=((t.dense?(d[re]||[])[ae]:d[ne])||{}).F}else f.f=""+Zo(K.formula,0,K.cell,P,O)}B>0&&(f.z=W[f.ixfe>>8&63]),Kl(f,t,r.opts.Date1904),C(K.cell,f,t),v=K;break;case 7:case 519:if(!v)throw new Error("String record expects Formula");v.val=K,(f=Jl(K,v.cell.ixfe,"s")).XF=S[f.ixfe],t.cellFormula&&(f.f=""+Zo(v.formula,0,v.cell,P,O)),B>0&&(f.z=W[f.ixfe>>8&63]),Kl(f,t,r.opts.Date1904),C(v.cell,f,t),v=null;break;case 33:case 545:y.push(K);var se=ha(K[0].s);if(s=t.dense?(d[K[0].s.r]||[])[K[0].s.c]:d[se],t.cellFormula&&s){if(!v)break;if(!se||!s)break;s.f=""+Zo(K[1],0,K[0],P,O),s.F=da(K[0])}break;case 1212:if(!t.cellFormula)break;if(T){if(!v)break;E[ha(v.cell)]=K[0],((s=t.dense?(d[v.cell.r]||[])[v.cell.c]:d[ha(v.cell)])||{}).f=""+Zo(K[0],0,n,P,O)}break;case 253:f=Jl(g[K.isst].t,K.ixfe,"s"),g[K.isst].h&&(f.h=g[K.isst].h),f.XF=S[f.ixfe],B>0&&(f.z=W[f.ixfe>>8&63]),Kl(f,t,r.opts.Date1904),C({c:K.c,r:K.r},f,t);break;case 513:t.sheetStubs&&(f={ixfe:K.ixfe,XF:S[K.ixfe],t:"z"},B>0&&(f.z=W[f.ixfe>>8&63]),Kl(f,t,r.opts.Date1904),C({c:K.c,r:K.r},f,t));break;case 190:if(t.sheetStubs)for(var ie=K.c;ie<=K.C;++ie){var oe=K.ixfe[ie-K.c];f={ixfe:oe,XF:S[oe],t:"z"},B>0&&(f.z=W[f.ixfe>>8&63]),Kl(f,t,r.opts.Date1904),C({c:ie,r:K.r},f,t)}break;case 214:case 516:case 4:(f=Jl(K.val,K.ixfe,"s")).XF=S[f.ixfe],B>0&&(f.z=W[f.ixfe>>8&63]),Kl(f,t,r.opts.Date1904),C({c:K.c,r:K.r},f,t);break;case 0:case 512:1===U&&(m=K);break;case 252:g=K;break;case 1054:if(4==O.biff){W[B++]=K[1];for(var ce=0;ce<B+163&&H[ce]!=K[1];++ce);ce>=163&&Ee(K[1],B+163)}else Ee(K[1],K[0]);break;case 30:W[B++]=K;for(var le=0;le<B+163&&H[le]!=K;++le);le>=163&&Ee(K,B+163);break;case 229:R=R.concat(K);break;case 93:I[K.cmo[0]]=O.lastobj=K;break;case 438:O.lastobj.TxO=K;break;case 127:O.lastobj.ImData=K;break;case 440:for(c=K[0].s.r;c<=K[0].e.r;++c)for(o=K[0].s.c;o<=K[0].e.c;++o)(s=t.dense?(d[c]||[])[o]:d[ha({c:o,r:c})])&&(s.l=K[1]);break;case 2048:for(c=K[0].s.r;c<=K[0].e.r;++c)for(o=K[0].s.c;o<=K[0].e.c;++o)(s=t.dense?(d[c]||[])[o]:d[ha({c:o,r:c})])&&s.l&&(s.l.Tooltip=K[1]);break;case 28:if(O.biff<=5&&O.biff>=2)break;s=t.dense?(d[K[0].r]||[])[K[0].c]:d[ha(K[0])];var fe=I[K[2]];s||(t.dense?(d[K[0].r]||(d[K[0].r]=[]),s=d[K[0].r][K[0].c]={t:"z"}):s=d[ha(K[0])]={t:"z"},m.e.r=Math.max(m.e.r,K[0].r),m.s.r=Math.min(m.s.r,K[0].r),m.e.c=Math.max(m.e.c,K[0].c),m.s.c=Math.min(m.s.c,K[0].c)),s.c||(s.c=[]),i={a:K[1],t:fe.TxO.t},s.c.push(i);break;case 2173:S[K.ixfe],K.ext.forEach((function(e){e[0]}));break;case 125:if(!O.cellStyles)break;for(;K.e>=K.s;)N[K.e--]={width:K.w/256,level:K.level||0,hidden:!!(1&K.flags)},D||(D=!0,Ri(K.w/256)),Ii(N[K.e+1]);break;case 520:var he={};null!=K.level&&(F[K.r]=he,he.level=K.level),K.hidden&&(F[K.r]=he,he.hidden=!0),K.hpt&&(F[K.r]=he,he.hpt=K.hpt,he.hpx=Fi(K.hpt));break;case 38:case 39:case 40:case 41:d["!margins"]||bc(d["!margins"]={}),d["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[j]]=K;break;case 161:d["!margins"]||bc(d["!margins"]={}),d["!margins"].header=K.header,d["!margins"].footer=K.footer;break;case 574:K.RTL&&(_.Views[0].RTL=!0);break;case 146:k=K;break;case 2198:u=K;break;case 140:h=K;break;case 442:b?A.CodeName=K||A.name:_.WBProps.CodeName=K||"ThisWorkbook"}}else $||console.error("Missing Info for XLS Record 0x"+j.toString(16)),e.l+=X}return r.SheetNames=Fe(p).sort((function(e,t){return Number(e)-Number(t)})).map((function(e){return p[e].name})),t.bookSheets||(r.Sheets=a),!r.SheetNames.length&&w["!ref"]?(r.SheetNames.push("Sheet1"),r.Sheets&&(r.Sheets.Sheet1=w)):r.Preamble=w,r.Sheets&&z.forEach((function(e,t){r.Sheets[r.SheetNames[t]]["!autofilter"]=e})),r.Strings=g,r.SSF=Ke(H),O.enc&&(r.Encryption=O.enc),u&&(r.Themes=u),r.Metadata={},void 0!==h&&(r.Metadata.Country=h),P.names.length>0&&(_.Names=P.names),r.Workbook=_,r}var Zl="e0859ff2f94f6810ab9108002b27b3d9",Ql="02d5cdd59c2e1b10939708002b2cf9ae",ef="05d5cdd59c2e1b10939708002b2cf9ae";function tf(e,t){var r,a,n,s;if(t||(t={}),oh(t),f(),t.codepage&&c(t.codepage),e.FullPaths){if(Ce.find(e,"/encryption"))throw new Error("File is password-protected");r=Ce.find(e,"!CompObj"),a=Ce.find(e,"/Workbook")||Ce.find(e,"/Book")}else{switch(t.type){case"base64":e=_(T(e));break;case"binary":e=_(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e))}Jr(e,0),a={content:e}}if(r&&Xl(r),t.bookProps&&!t.bookSheets)n={};else{var i=E?"buffer":"array";if(a&&a.content)n=ql(a.content,t);else if((s=Ce.find(e,"PerfectOffice_MAIN"))&&s.content)n=Ks.to_workbook(s.content,(t.type=i,t));else{if(!(s=Ce.find(e,"NativeContent_MAIN"))||!s.content)throw(s=Ce.find(e,"MN0"))&&s.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");n=Ks.to_workbook(s.content,(t.type=i,t))}t.bookVBA&&e.FullPaths&&Ce.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(n.vbaraw=function(e){var t=Ce.utils.cfb_new({root:"R"});return e.FullPaths.forEach((function(r,a){if("/"!==r.slice(-1)&&r.match(/_VBA_PROJECT_CUR/)){var n=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");Ce.utils.cfb_add(t,n,e.FileIndex[a].content)}})),Ce.write(t)}(e))}var o={};return e.FullPaths&&function(e,t,r){var a=Ce.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=zn(a,Ka,Ql);for(var s in n)t[s]=n[s]}catch(l){if(r.WTF)throw l}var i=Ce.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var o=zn(i,Ja,Zl);for(var c in o)null==t[c]&&(t[c]=o[c])}catch(l){if(r.WTF)throw l}t.HeadingPairs&&t.TitlesOfParts&&(En(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}(e,o,t),n.Props=n.Custprops=o,t.bookFiles&&(n.cfb=e),n}function rf(e,t){var r=t||{},a=Ce.utils.cfb_new({root:"R"}),n="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":n="/Workbook",r.biff=8;break;case"biff5":n="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return Ce.utils.cfb_add(a,n,mf(e,r)),8==r.biff&&(e.Props||e.Custprops)&&function(e,t){var r,a=[],n=[],s=[],i=0,o=De(Ka,"n"),c=De(Ja,"n");if(e.Props)for(r=Fe(e.Props),i=0;i<r.length;++i)(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(c,r[i])?n:s).push([r[i],e.Props[r[i]]]);if(e.Custprops)for(r=Fe(e.Custprops),i=0;i<r.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},r[i])||(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(c,r[i])?n:s).push([r[i],e.Custprops[r[i]]]);var l=[];for(i=0;i<s.length;++i)Bn.indexOf(s[i][0])>-1||Tn.indexOf(s[i][0])>-1||null!=s[i][1]&&l.push(s[i]);n.length&&Ce.utils.cfb_add(t,"/\x05SummaryInformation",Vn(n,Zl,c,Ja)),(a.length||l.length)&&Ce.utils.cfb_add(t,"/\x05DocumentSummaryInformation",Vn(a,Ql,o,Ka,l.length?l:null,ef))}(e,a),8==r.biff&&e.vbaraw&&function(e,t){t.FullPaths.forEach((function(r,a){if(0!=a){var n=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==n.slice(-1)&&Ce.utils.cfb_add(e,n,t.FileIndex[a].content)}}))}(a,Ce.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})),a}var af={0:{f:function(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,7&s&&(r.level=7&s),16&s&&(r.hidden=!0),32&s&&(r.hpt=n/20),r}},1:{f:function(e){return[xa(e)]}},2:{f:function(e){return[xa(e),Ua(e),"n"]}},3:{f:function(e){return[xa(e),e.read_shift(1),"e"]}},4:{f:function(e){return[xa(e),e.read_shift(1),"b"]}},5:{f:function(e){return[xa(e),Va(e),"n"]}},6:{f:function(e){return[xa(e),Ea(e),"str"]}},7:{f:function(e){return[xa(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var a=e.l+t,n=xa(e);n.r=r["!row"];var s=[n,Ea(e),"str"];if(r.cellFormula){e.l+=2;var i=sc(e,a-e.l,r);s[3]=Zo(i,0,n,r.supbooks,r)}else e.l=a;return s}},9:{f:function(e,t,r){var a=e.l+t,n=xa(e);n.r=r["!row"];var s=[n,Va(e),"n"];if(r.cellFormula){e.l+=2;var i=sc(e,a-e.l,r);s[3]=Zo(i,0,n,r.supbooks,r)}else e.l=a;return s}},10:{f:function(e,t,r){var a=e.l+t,n=xa(e);n.r=r["!row"];var s=[n,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var i=sc(e,a-e.l,r);s[3]=Zo(i,0,n,r.supbooks,r)}else e.l=a;return s}},11:{f:function(e,t,r){var a=e.l+t,n=xa(e);n.r=r["!row"];var s=[n,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var i=sc(e,a-e.l,r);s[3]=Zo(i,0,n,r.supbooks,r)}else e.l=a;return s}},12:{f:function(e){return[Oa(e)]}},13:{f:function(e){return[Oa(e),Ua(e),"n"]}},14:{f:function(e){return[Oa(e),e.read_shift(1),"e"]}},15:{f:function(e){return[Oa(e),e.read_shift(1),"b"]}},16:{f:Vc},17:{f:function(e){return[Oa(e),Ea(e),"str"]}},18:{f:function(e){return[Oa(e),e.read_shift(4),"s"]}},19:{f:ka},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var a=e.l+t;e.l+=4,e.l+=1;var n=e.read_shift(4),s=Pa(e),i=ic(e,0,r),o=Fa(e);e.l=a;var c={Name:s,Ptg:i};return n<268435455&&(c.Sheet=n),o&&(c.Comment=o),c}},40:{},42:{},43:{f:function(e,t,r){var a={};a.sz=e.read_shift(2)/20;var n=function(e){var t=e.read_shift(1);return e.l++,{fBold:1&t,fItalic:2&t,fUnderline:4&t,fStrikeout:8&t,fOutline:16&t,fShadow:32&t,fCondense:64&t,fExtend:128&t}}(e);switch(n.fItalic&&(a.italic=1),n.fCondense&&(a.condense=1),n.fExtend&&(a.extend=1),n.fShadow&&(a.shadow=1),n.fOutline&&(a.outline=1),n.fStrikeout&&(a.strike=1),700===e.read_shift(2)&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript"}var s=e.read_shift(1);0!=s&&(a.underline=s);var i=e.read_shift(1);i>0&&(a.family=i);var o=e.read_shift(1);switch(o>0&&(a.charset=o),e.l++,a.color=function(e){var t={},r=e.read_shift(1)>>>1,a=e.read_shift(1),n=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r){case 0:t.auto=1;break;case 1:t.index=a;var c=en[a];c&&(t.rgb=Si(c));break;case 2:t.rgb=Si([s,i,o]);break;case 3:t.theme=a}return 0!=n&&(t.tint=n>0?n/32767:n/32768),t}(e),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor"}return a.name=Ea(e),a}},44:{f:function(e,t){return[e.read_shift(2),Ea(e)]}},45:{f:Vi},46:{f:$i},47:{f:function(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:Bs},62:{f:function(e){return[xa(e),ka(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=ha(r);var a=e.read_shift(1);return 2&a&&(t.l="1"),8&a&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:qr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},a=e[e.l];return++e.l,r.above=!(64&a),r.left=!(128&a),e.l+=18,r.name=Ia(e,t-19),r}},148:{f:Hc,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?Ea(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(65536&a),r.backupFile=!!(64&a),r.checkCompatibility=!!(4096&a),r.date1904=!!(1&a),r.filterPrivacy=!!(8&a),r.hidePivotFieldList=!!(1024&a),r.promptedSolutions=!!(16&a),r.publishItems=!!(2048&a),r.refreshAllConnections=!!(262144&a),r.saveExternalLinkValues=!!(128&a),r.showBorderUnselectedTables=!!(4&a),r.showInkAnnotation=!!(32&a),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(32768&a),r.updateLinks=["userSet","never","always"][a>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=La(e,t-8),r.name=Ea(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:Ha},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Gc},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:Ea(e)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:La},357:{},358:{},359:{},360:{T:1},361:{},362:{f:Ds},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var a=e.l+t,n=Wa(e),s=e.read_shift(1),i=[n];if(i[2]=s,r.cellFormula){var o=nc(e,a-e.l,r);i[1]=o}else e.l=a;return i}},427:{f:function(e,t,r){var a=e.l+t,n=[Ha(e,16)];if(r.cellFormula){var s=oc(e,a-e.l,r);n[1]=s,e.l=a}else e.l=a;return n}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return Xc.forEach((function(r){t[r]=Va(e)})),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,a=Ha(e,16),n=Fa(e),s=Ea(e),i=Ea(e),o=Ea(e);e.l=r;var c={rfx:a,relId:n,loc:s,display:o};return i&&(c.Tooltip=i),c}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:La},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:bo},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=Ha(e,16);return t.rfx=r.s,t.ref=ha(r.s),e.l+=16,t}},636:{T:-1},637:{f:_a},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:Ea(e)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},nf={6:{f:tc},10:{f:Gn},12:{f:$n},13:{f:$n},14:{f:jn},15:{f:jn},16:{f:Va},17:{f:jn},18:{f:jn},19:{f:$n},20:{f:Rs},21:{f:Rs},23:{f:Ds},24:{f:Fs},25:{f:jn},26:{},27:{},28:{f:function(e,t,r){return function(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),o=rs(e,0,r);return r.biff<8&&e.read_shift(1),[{r:a,c:n},o,i,s]}}(e,0,r)}},29:{},34:{f:jn},35:{f:Is},38:{f:Va},39:{f:Va},40:{f:Va},41:{f:Va},42:{f:jn},43:{f:jn},47:{f:function(e,t,r){var a={Type:r.biff>=8?e.read_shift(2):0};return a.Type?Ei(e,t-2,a):function(e,t,r,a){var n={key:$n(e),verificationBytes:$n(e)};r.password&&(n.verifier=bi(r.password)),a.valid=n.verificationBytes===n.verifier,a.valid&&(a.insitu=Ti(r.password))}(e,r.biff,r,a),a}},49:{f:function(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return a.name=qn(e,0,r),a}},51:{f:$n},60:{},61:{f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{f:jn},65:{f:function(){}},66:{f:$n},77:{},80:{},81:{},82:{},85:{f:$n},89:{},90:{},91:{},92:{f:function(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=rs(e,0,r);return e.read_shift(t+a-e.l),n}},93:{f:function(e,t,r){if(r&&r.biff<8)return function(e,t,r){e.l+=4;var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,t-=36;var i=[];return i.push((Ls[a]||qr)(e,t,r)),{cmo:[n,a,s],ft:i}}(e,t,r);var a=bs(e),n=function(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a.push(Ts[n](e,r-e.l))}catch(s){return e.l=r,a}}return e.l!=r&&(e.l=r),a}(e,t-22,a[1]);return{cmo:a,ft:n}}},94:{},95:{f:jn},96:{},97:{},99:{f:jn},125:{f:Bs},128:{f:function(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(0!==t[0]&&t[0]--,0!==t[1]&&t[1]--,t[0]>7||t[1]>7)throw new Error("Bad Gutters: "+t.join("|"));return t}},129:{f:function(e,t,r){var a=r&&8==r.biff||2==t?e.read_shift(2):(e.l+=t,0);return{fDialog:16&a,fBelow:64&a,fRight:128&a}}},130:{f:$n},131:{f:jn},132:{f:jn},133:{f:function(e,t,r){var a=e.read_shift(4),n=3&e.read_shift(1),s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule"}var i=qn(e,0,r);return 0===i.length&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}},134:{},140:{f:function(e){var t,r=[0,0];return t=e.read_shift(2),r[0]=qa[t]||t,t=e.read_shift(2),r[1]=qa[t]||t,r}},141:{f:$n},144:{},146:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(ls(e));return r}},151:{},152:{},153:{},154:{},155:{},156:{f:$n},157:{},158:{},160:{f:Hs},161:{f:function(e,t){var r={};return t<32||(e.l+=16,r.header=Va(e),r.footer=Va(e),e.l+=2),r}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(ds(e));if(e.l!==r)throw new Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}},190:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}},193:{f:Gn},197:{},198:{},199:{},200:{},201:{},202:{f:jn},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:$n},220:{},221:{f:jn},222:{},224:{f:function(e,t,r){var a={};return a.ifnt=e.read_shift(2),a.numFmtId=e.read_shift(2),a.flags=e.read_shift(2),a.fStyle=a.flags>>2&1,6,a.data=function(e,t,r,a){var n={},s=e.read_shift(4),i=e.read_shift(4),o=e.read_shift(4),c=e.read_shift(2);return n.patternType=Za[o>>26],a.cellStyles?(n.alc=7&s,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=15&i,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=127&o,n.icvBottom=o>>7&127,n.icvDiag=o>>14&127,n.dgDiag=o>>21&15,n.icvFore=127&c,n.icvBack=c>>7&127,n.fsxButton=c>>14&1,n):n}(e,0,a.fStyle,r),a}},225:{f:function(e,t){return 0===t||e.read_shift(2),1200}},226:{f:Gn},227:{},229:{f:function(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(ps(e));return r}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<r;++i)s.push(Zn(e));return s.Count=a,s.Unique=n,s}},253:{f:function(e){var t=fs(e);return t.isst=e.read_shift(4),t}},255:{f:function(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:Kn},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:jn},353:{f:Gn},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,1025==s||14849==s)return[s,n];if(s<1||s>255)throw new Error("Unexpected SupBook type: "+s);for(var i=es(e,s),o=[];a>e.l;)o.push(ts(e));return[s,n,i,o]}},431:{f:jn},432:{},433:{},434:{},437:{},438:{f:function(e,t,r){var a=e.l,n="";try{e.l+=4;var s=(r.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(s)?e.l+=6:function(e){var t=e.read_shift(1);e.l++;var r=e.read_shift(2);return e.l+=2,[t,r]}(e);var i=e.read_shift(2);e.read_shift(2),$n(e);var o=e.read_shift(2);e.l+=o;for(var c=1;c<e.lens.length-1;++c){if(e.l-a!=e.lens[c])throw new Error("TxO: bad continue record");var l=e[e.l];if((n+=es(e,e.lens[c+1]-e.lens[c]-1)).length>=(l?i:2*i))break}if(n.length!==i&&n.length!==2*i)throw new Error("cchText: "+i+" != "+n.length);return e.l=a+t,{t:n}}catch(f){return e.l=a+t,{t:n}}}},439:{f:jn},440:{f:function(e,t){var r=ps(e);e.l+=16;var a=function(e,t){var r=e.l+t,a=e.read_shift(4);if(2!==a)throw new Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,o,c,l,f,h="";16&n&&(s=ss(e,e.l)),128&n&&(i=ss(e,e.l)),257===(257&n)&&(o=ss(e,e.l)),1===(257&n)&&(c=ns(e,e.l)),8&n&&(h=ss(e,e.l)),32&n&&(l=e.read_shift(16)),64&n&&(f=Cn(e)),e.l=r;var u=i||o||c||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&n&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return l&&(d.guid=l),f&&(d.time=f),s&&(d.Tooltip=s),d}(e,t-24);return[r,a]}},441:{},442:{f:ts},443:{},444:{f:$n},445:{},446:{},448:{f:Gn},449:{f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{f:Gn},512:{f:xs},513:{f:Ws},515:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=fs(e),n=Va(e);return a.val=n,a}},516:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5),e.l;var a=fs(e);2==r.biff&&e.l++;var n=ts(e,e.l,r);return a.val=n,a}},517:{f:Os},519:{f:zs},520:{f:function(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,7&a&&(t.level=7&a),32&a&&(t.hidden=!0),64&a&&(t.hpt=r/20),t}},523:{},545:{f:Ps},549:{f:ks},566:{},574:{f:function(e,t,r){return r&&r.biff>=2&&r.biff<5?{}:{RTL:64&e.read_shift(2)}}},638:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=ds(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}},659:{},1048:{},1054:{f:function(e,t,r){return[e.read_shift(2),rs(e,0,r)]}},1084:{},1212:{f:function(e,t,r){var a=vs(e);e.l++;var n=e.read_shift(1);return[ec(e,t-=8,r),n,a]}},2048:{f:function(e,t){e.read_shift(2);var r=ps(e),a=e.read_shift((t-10)/2,"dbcs-cont");return[r,a=a.replace(R,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:Es},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:Gn},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t},r:12},2173:{f:function(e,t){e.l,e.l+=2;var r=e.read_shift(2);e.l+=2;for(var a=e.read_shift(2),n=[];a-- >0;)n.push(lo(e,e.l));return{ixfe:r,ext:n}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:jn,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2);return[es(e,a,r),es(e,n,r)]}e.l+=t},r:12},2197:{},2198:{f:function(e,t,r){var a=e.l+t;if(124226!==e.read_shift(4))if(r.cellStyles){var n,s=e.slice(e.l);e.l=a;try{n=ht(s,{type:"array"})}catch(o){return}var i=it(n,"theme/theme/theme1.xml",!0);if(i)return io(i,r)}else e.l=a},r:12},2199:{},2200:{},2201:{},2202:{f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{f:Gn},2204:{},2205:{},2206:{},2207:{},2211:{f:function(e){var t=function(e){var t=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:t,flags:r}}(e);if(2211!=t.type)throw new Error("Invalid Future Record "+t.type);return 0!==e.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:$n},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(e,t,r){var a={area:!1};if(5!=r.biff)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,16&n&&(a.area=!0),a}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(ls(e));return r}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:xs},1:{},2:{f:function(e){var t=fs(e);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}},3:{f:function(e){var t=fs(e);++e.l;var r=Va(e);return t.t="n",t.val=r,t}},4:{f:function(e,t,r){r.biffguess&&5==r.biff&&(r.biff=2);var a=fs(e);++e.l;var n=rs(e,0,r);return a.t="str",a.val=n,a}},5:{f:Os},7:{f:function(e){var t=e.read_shift(1);return 0===t?(e.l++,""):e.read_shift(t,"sbcs-cont")}},8:{},9:{f:Es},11:{},22:{f:$n},30:{f:As},31:{},32:{},33:{f:Ps},36:{},37:{f:ks},50:{f:function(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}},62:{},52:{},67:{},68:{f:$n},69:{},86:{},126:{},127:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(e,t,r){var a=e.l+t,n=fs(e),s=e.read_shift(2),i=es(e,s,r);return e.l=a,n.t="str",n.val=i,n}},223:{},234:{},354:{},421:{},518:{f:tc},521:{f:Es},536:{f:Fs},547:{f:Is},561:{},579:{},1030:{f:tc},1033:{f:Es},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function sf(e,t,r,a){var n=t;if(!isNaN(n)){var s=a||(r||[]).length||0,i=e.next(4);i.write_shift(2,n),i.write_shift(2,s),s>0&&Ur(r)&&e.push(r)}}function of(e,t,r){return e||(e=Zr(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function cf(e,t,r,a){if(null!=t.v)switch(t.t){case"d":case"n":var n="d"==t.t?Ue($e(t.v)):t.v;return void(n==(0|n)&&n>=0&&n<65536?sf(e,2,function(e,t,r){var a=Zr(9);return of(a,e,t),a.write_shift(2,r),a}(r,a,n)):sf(e,3,function(e,t,r){var a=Zr(15);return of(a,e,t),a.write_shift(8,r,"f"),a}(r,a,n)));case"b":case"e":return void sf(e,5,function(e,t,r,a){var n=Zr(9);return of(n,e,t),Jn(r,a||"b",n),n}(r,a,t.v,t.t));case"s":case"str":return void sf(e,4,function(e,t,r){var a=Zr(8+2*r.length);return of(a,e,t),a.write_shift(1,r.length),a.write_shift(r.length,r,"sbcs"),a.l<a.length?a.slice(0,a.l):a}(r,a,(t.v||"").slice(0,255)))}sf(e,1,of(null,r,a))}function lf(e,t){var r=t||{};for(var a=ea(),n=0,s=0;s<e.SheetNames.length;++s)e.SheetNames[s]==r.sheet&&(n=s);if(0==n&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return sf(a,4==r.biff?1033:3==r.biff?521:9,ys(0,16,r)),function(e,t,r,a){var n,s=Array.isArray(t),i=pa(t["!ref"]||"A1"),o="",c=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),n=da(i)}for(var l=i.s.r;l<=i.e.r;++l){o=oa(l);for(var f=i.s.c;f<=i.e.c;++f){l===i.s.r&&(c[f]=la(f)),n=c[f]+o;var h=s?(t[l]||[])[f]:t[n];h&&cf(e,h,l,f)}}}(a,e.Sheets[e.SheetNames[n]],0,r),sf(a,10),a.end()}function ff(e,t,r){sf(e,49,function(e,t){var r=e.name||"Arial",a=t&&5==t.biff,n=Zr(a?15+r.length:16+2*r.length);return n.write_shift(2,20*(e.sz||12)),n.write_shift(4,0),n.write_shift(2,400),n.write_shift(4,0),n.write_shift(2,0),n.write_shift(1,r.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*r.length,r,a?"sbcs":"utf16le"),n}({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}function hf(e,t){if(t){var r=0;t.forEach((function(t,a){++r<=256&&t&&sf(e,125,function(e,t){var r=Zr(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,256*e.width),r.write_shift(2,0);var a=0;return e.hidden&&(a|=1),r.write_shift(1,a),a=e.level||0,r.write_shift(1,a),r.write_shift(2,0),r}(gc(a,t),a))}))}}function uf(e,t,r,a,n){var s=16+wc(n.cellXfs,t,n);if(null!=t.v||t.bf)if(t.bf)sf(e,6,rc(t,r,a,0,s));else switch(t.t){case"d":case"n":sf(e,515,function(e,t,r,a){var n=Zr(14);return hs(e,t,a,n),Ga(r,n),n}(r,a,"d"==t.t?Ue($e(t.v)):t.v,s));break;case"b":case"e":sf(e,517,function(e,t,r,a,n,s){var i=Zr(8);return hs(e,t,a,i),Jn(r,s,i),i}(r,a,t.v,s,0,t.t));break;case"s":case"str":if(n.bookSST)sf(e,253,function(e,t,r,a){var n=Zr(10);return hs(e,t,a,n),n.write_shift(4,r),n}(r,a,vc(n.Strings,t.v,n.revStrings),s));else sf(e,516,function(e,t,r,a,n){var s=!n||8==n.biff,i=Zr(+s+8+(1+s)*r.length);return hs(e,t,a,i),i.write_shift(2,r.length),s&&i.write_shift(1,1),i.write_shift((1+s)*r.length,r,s?"utf16le":"sbcs"),i}(r,a,(t.v||"").slice(0,255),s,n));break;default:sf(e,513,hs(r,a,s))}else sf(e,513,hs(r,a,s))}function df(e,t,r){var a,n=ea(),s=r.SheetNames[e],i=r.Sheets[s]||{},o=(r||{}).Workbook||{},c=(o.Sheets||[])[e]||{},l=Array.isArray(i),f=8==t.biff,h="",u=[],d=pa(i["!ref"]||"A1"),p=f?65536:16384;if(d.e.c>255||d.e.r>=p){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");d.e.c=Math.min(d.e.c,255),d.e.r=Math.min(d.e.c,p-1)}sf(n,2057,ys(0,16,t)),sf(n,13,Yn(1)),sf(n,12,Yn(100)),sf(n,15,Xn(!0)),sf(n,17,Xn(!1)),sf(n,16,Ga(.001)),sf(n,95,Xn(!0)),sf(n,42,Xn(!1)),sf(n,43,Xn(!1)),sf(n,130,Yn(1)),sf(n,128,function(e){var t=Zr(8);return t.write_shift(4,0),t.write_shift(2,e[0]?e[0]+1:0),t.write_shift(2,e[1]?e[1]+1:0),t}([0,0])),sf(n,131,Xn(!1)),sf(n,132,Xn(!1)),f&&hf(n,i["!cols"]),sf(n,512,function(e,t){var r=8!=t.biff&&t.biff?2:4,a=Zr(2*r+6);return a.write_shift(r,e.s.r),a.write_shift(r,e.e.r+1),a.write_shift(2,e.s.c),a.write_shift(2,e.e.c+1),a.write_shift(2,0),a}(d,t)),f&&(i["!links"]=[]);for(var m=d.s.r;m<=d.e.r;++m){h=oa(m);for(var v=d.s.c;v<=d.e.c;++v){m===d.s.r&&(u[v]=la(v)),a=u[v]+h;var g=l?(i[m]||[])[v]:i[a];g&&(uf(n,g,m,v,t),f&&g.l&&i["!links"].push([a,g.l]))}}var b=c.CodeName||c.name||s;return f&&sf(n,574,function(e){var t=Zr(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}((o.Views||[])[0])),f&&(i["!merges"]||[]).length&&sf(n,229,function(e){var t=Zr(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)ms(e[r],t);return t}(i["!merges"])),f&&function(e,t){for(var r=0;r<t["!links"].length;++r){var a=t["!links"][r];sf(e,440,Ms(a)),a[1].Tooltip&&sf(e,2048,Us(a))}delete t["!links"]}(n,i),sf(n,442,as(b)),f&&function(e,t){var r=Zr(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),sf(e,2151,r),(r=Zr(39)).write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),ms(pa(t["!ref"]||"A1"),r),r.write_shift(4,4),sf(e,2152,r)}(n,i),sf(n,10),n.end()}function pf(e,t,r){var a=ea(),n=(e||{}).Workbook||{},s=n.Sheets||[],i=n.WBProps||{},o=8==r.biff,c=5==r.biff;(sf(a,2057,ys(0,5,r)),"xla"==r.bookType&&sf(a,135),sf(a,225,o?Yn(1200):null),sf(a,193,function(e,t){t||(t=Zr(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}(2)),c&&sf(a,191),c&&sf(a,192),sf(a,226),sf(a,92,function(e,t){var r=!t||8==t.biff,a=Zr(r?112:54);for(a.write_shift(8==t.biff?2:1,7),r&&a.write_shift(1,0),a.write_shift(4,859007059),a.write_shift(4,5458548|(r?0:536870912));a.l<a.length;)a.write_shift(1,r?0:32);return a}(0,r)),sf(a,66,Yn(o?1200:1252)),o&&sf(a,353,Yn(0)),o&&sf(a,448),sf(a,317,function(e){for(var t=Zr(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),o&&e.vbaraw&&sf(a,211),o&&e.vbaraw)&&sf(a,442,as(i.CodeName||"ThisWorkbook"));sf(a,156,Yn(17)),sf(a,25,Xn(!1)),sf(a,18,Xn(!1)),sf(a,19,Yn(0)),o&&sf(a,431,Xn(!1)),o&&sf(a,444,Yn(0)),sf(a,61,function(){var e=Zr(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}()),sf(a,64,Xn(!1)),sf(a,141,Yn(0)),sf(a,34,Xn("true"==function(e){return e.Workbook&&e.Workbook.WBProps&&It(e.Workbook.WBProps.date1904)?"true":"false"}(e))),sf(a,14,Xn(!0)),o&&sf(a,439,Xn(!1)),sf(a,218,Yn(0)),ff(a,0,r),function(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach((function(a){for(var n=a[0];n<=a[1];++n)null!=t[n]&&sf(e,1054,_s(n,t[n],r))}))}(a,e.SSF,r),function(e,t){for(var r=0;r<16;++r)sf(e,224,Cs({numFmtId:0,style:!0},0,t));t.cellXfs.forEach((function(r){sf(e,224,Cs(r,0,t))}))}(a,r),o&&sf(a,352,Xn(!1));var l,f=a.end(),h=ea();o&&sf(h,140,(l||(l=Zr(4)),l.write_shift(2,1),l.write_shift(2,1),l)),o&&r.Strings&&function(e,t,r,a){var n=a||(r||[]).length||0;if(n<=8224)return sf(e,t,r,n);var s=t;if(!isNaN(s)){for(var i=r.parts||[],o=0,c=0,l=0;l+(i[o]||8224)<=8224;)l+=i[o]||8224,o++;var f=e.next(4);for(f.write_shift(2,s),f.write_shift(2,l),e.push(r.slice(c,c+l)),c+=l;c<n;){for((f=e.next(4)).write_shift(2,60),l=0;l+(i[o]||8224)<=8224;)l+=i[o]||8224,o++;f.write_shift(2,l),e.push(r.slice(c,c+l)),c+=l}}}(h,252,function(e,t){var r=Zr(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var a=[],n=0;n<e.length;++n)a[n]=Qn(e[n]);var s=O([r].concat(a));return s.parts=[r.length].concat(a.map((function(e){return e.length}))),s}(r.Strings)),sf(h,10);var u=h.end(),d=ea(),p=0,m=0;for(m=0;m<e.SheetNames.length;++m)p+=(o?12:11)+(o?2:1)*e.SheetNames[m].length;var v=f.length+p+u.length;for(m=0;m<e.SheetNames.length;++m){sf(d,133,Ss({pos:v,hs:(s[m]||{}).Hidden||0,dt:0,name:e.SheetNames[m]},r)),v+=t[m].length}var g=d.end();if(p!=g.length)throw new Error("BS8 "+p+" != "+g.length);var b=[];return f.length&&b.push(f),g.length&&b.push(g),u.length&&b.push(u),O(b)}function mf(e,t){for(var r=0;r<=e.SheetNames.length;++r){var a=e.Sheets[e.SheetNames[r]];if(a&&a["!ref"])ua(a["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}var n=t||{};switch(n.biff||2){case 8:case 5:return function(e,t){var r=t||{},a=[];e&&!e.SSF&&(e.SSF=Ke(H)),e&&e.SSF&&(Se(),ye(e.SSF),r.revssf=Le(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,ch(r),r.cellXfs=[],wc(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var n=0;n<e.SheetNames.length;++n)a[a.length]=df(n,r,e);return a.unshift(pf(e,a,r)),O(a)}(e,t);case 4:case 3:case 2:return lf(e,t)}throw new Error("invalid type "+n.bookType+" for BIFF")}function vf(e,t){var r=t||{};var a=r.dense?[]:{},n=(e=e.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,o=s&&s.index||e.length,c=et(e.slice(i,o),/(:?<tr[^>]*>)/i,"<tr>"),l=-1,f=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<c.length;++i){var m=c[i].trim(),v=m.slice(0,3).toLowerCase();if("<tr"!=v){if("<td"==v||"<th"==v){var g=m.split(/<\/t[dh]>/i);for(o=0;o<g.length;++o){var b=g[o].trim();if(b.match(/<t[dh]/i)){for(var w=b,T=0;"<"==w.charAt(0)&&(T=w.indexOf(">"))>-1;)w=w.slice(T+1);for(var E=0;E<p.length;++E){var y=p[E];y.s.c==f&&y.s.r<l&&l<=y.e.r&&(f=y.e.c+1,E=-1)}var S=wt(b.slice(0,b.indexOf(">")));u=S.colspan?+S.colspan:1,((h=+S.rowspan)>1||u>1)&&p.push({s:{r:l,c:f},e:{r:l+(h||1)-1,c:f+u-1}});var k=S.t||S["data-t"]||"";if(w.length)if(w=Bt(w),d.s.r>l&&(d.s.r=l),d.e.r<l&&(d.e.r=l),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),w.length){var _={t:"s",v:w};r.raw||!w.trim().length||"s"==k||("TRUE"===w?_={t:"b",v:!0}:"FALSE"===w?_={t:"b",v:!1}:isNaN(qe(w))?isNaN(Qe(w).getDate())||(_={t:"d",v:$e(w)},r.cellDates||(_={t:"n",v:Ue(_.v)}),_.z=r.dateNF||H[14]):_={t:"n",v:qe(w)}),r.dense?(a[l]||(a[l]=[]),a[l][f]=_):a[ha({r:l,c:f})]=_,f+=u}else f+=u;else f+=u}}}}else{if(++l,r.sheetRows&&r.sheetRows<=l){--l;break}f=0}}return a["!ref"]=da(d),p.length&&(a["!merges"]=p),a}function gf(e,t,r,a){for(var n=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var o=0,c=0,l=0;l<n.length;++l)if(!(n[l].s.r>r||n[l].s.c>i)&&!(n[l].e.r<r||n[l].e.c<i)){if(n[l].s.r<r||n[l].s.c<i){o=-1;break}o=n[l].e.r-n[l].s.r+1,c=n[l].e.c-n[l].s.c+1;break}if(!(o<0)){var f=ha({r:r,c:i}),h=a.dense?(e[r]||[])[i]:e[f],u=h&&null!=h.v&&(h.h||Ot(h.w||(va(h),h.w)||""))||"",d={};o>1&&(d.rowspan=o),c>1&&(d.colspan=c),a.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(a.id||"sjs")+"-"+f,s.push($t("td",u,d))}}return"<tr>"+s.join("")+"</tr>"}var bf='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',wf="</body></html>";function Tf(e,t,r){return[].join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Ef(e,t){var r=t||{},a=null!=r.header?r.header:bf,n=null!=r.footer?r.footer:wf,s=[a],i=ua(e["!ref"]);r.dense=Array.isArray(e),s.push(Tf(0,0,r));for(var o=i.s.r;o<=i.e.r;++o)s.push(gf(e,i,o,r));return s.push("</table>"+n),s.join("")}function yf(e,t,r){var a=r||{};var n=0,s=0;if(null!=a.origin)if("number"==typeof a.origin)n=a.origin;else{var i="string"==typeof a.origin?fa(a.origin):a.origin;n=i.r,s=i.c}var o=t.getElementsByTagName("tr"),c=Math.min(a.sheetRows||1e7,o.length),l={s:{r:0,c:0},e:{r:n,c:s}};if(e["!ref"]){var f=ua(e["!ref"]);l.s.r=Math.min(l.s.r,f.s.r),l.s.c=Math.min(l.s.c,f.s.c),l.e.r=Math.max(l.e.r,f.e.r),l.e.c=Math.max(l.e.c,f.e.c),-1==n&&(l.e.r=n=f.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,v=0,g=0,b=0,w=0;for(e["!cols"]||(e["!cols"]=[]);p<o.length&&m<c;++p){var T=o[p];if(kf(T)){if(a.display)continue;d[m]={hidden:!0}}var E=T.children;for(v=g=0;v<E.length;++v){var y=E[v];if(!a.display||!kf(y)){var S=y.hasAttribute("data-v")?y.getAttribute("data-v"):y.hasAttribute("v")?y.getAttribute("v"):Bt(y.innerHTML),k=y.getAttribute("data-z")||y.getAttribute("z");for(u=0;u<h.length;++u){var _=h[u];_.s.c==g+s&&_.s.r<m+n&&m+n<=_.e.r&&(g=_.e.c+1-s,u=-1)}w=+y.getAttribute("colspan")||1,((b=+y.getAttribute("rowspan")||1)>1||w>1)&&h.push({s:{r:m+n,c:g+s},e:{r:m+n+(b||1)-1,c:g+s+(w||1)-1}});var A={t:"s",v:S},x=y.getAttribute("data-t")||y.getAttribute("t")||"";null!=S&&(0==S.length?A.t=x||"z":a.raw||0==S.trim().length||"s"==x||("TRUE"===S?A={t:"b",v:!0}:"FALSE"===S?A={t:"b",v:!1}:isNaN(qe(S))?isNaN(Qe(S).getDate())||(A={t:"d",v:$e(S)},a.cellDates||(A={t:"n",v:Ue(A.v)}),A.z=a.dateNF||H[14]):A={t:"n",v:qe(S)})),void 0===A.z&&null!=k&&(A.z=k);var C="",O=y.getElementsByTagName("A");if(O&&O.length)for(var R=0;R<O.length&&(!O[R].hasAttribute("href")||"#"==(C=O[R].getAttribute("href")).charAt(0));++R);C&&"#"!=C.charAt(0)&&(A.l={Target:C}),a.dense?(e[m+n]||(e[m+n]=[]),e[m+n][g+s]=A):e[ha({c:g+s,r:m+n})]=A,l.e.c<g+s&&(l.e.c=g+s),g+=w}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),l.e.r=Math.max(l.e.r,m-1+n),e["!ref"]=da(l),m>=c&&(e["!fullref"]=da((l.e.r=o.length-p+m-1+n,l))),e}function Sf(e,t){return yf((t||{}).dense?[]:{},e,t)}function kf(e){var t="",r=function(e){return e.ownerDocument.defaultView&&"function"===typeof e.ownerDocument.defaultView.getComputedStyle?e.ownerDocument.defaultView.getComputedStyle:"function"===typeof getComputedStyle?getComputedStyle:null}(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),"none"===t}function _f(e){var t=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,(function(e,t){return Array(parseInt(t,10)+1).join(" ")})).replace(/<text:tab[^>]*\/>/g,"\t").replace(/<text:line-break\/>/g,"\n");return[St(t.replace(/<[^>]*>/g,""))]}var Af={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function xf(e,t){var r=t||{};var a,n,s,i,o,c,l=Kt(e),f=[],h={name:""},u="",d=0,p={},m=[],v=r.dense?[]:{},g={value:""},b="",w=0,T=[],E=-1,y=-1,S={s:{r:1e6,c:1e7},e:{r:0,c:0}},k=0,_={},A=[],x={},C=[],O=1,R=1,I=[],N={Names:[]},F={},D=["",""],P=[],L={},M="",U=0,B=!1,W=!1,H=0;for(Jt.lastIndex=0,l=l.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");o=Jt.exec(l);)switch(o[3]=o[3].replace(/_.*$/,"")){case"table":case"\u5de5\u4f5c\u8868":"/"===o[1]?(S.e.c>=S.s.c&&S.e.r>=S.s.r?v["!ref"]=da(S):v["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=S.e.r&&(v["!fullref"]=v["!ref"],S.e.r=r.sheetRows-1,v["!ref"]=da(S)),A.length&&(v["!merges"]=A),C.length&&(v["!rows"]=C),s.name=s["\u540d\u79f0"]||s.name,"undefined"!==typeof JSON&&JSON.stringify(s),m.push(s.name),p[s.name]=v,W=!1):"/"!==o[0].charAt(o[0].length-2)&&(s=wt(o[0],!1),E=y=-1,S.s.r=S.s.c=1e7,S.e.r=S.e.c=0,v=r.dense?[]:{},A=[],C=[],W=!0);break;case"table-row-group":"/"===o[1]?--k:++k;break;case"table-row":case"\u884c":if("/"===o[1]){E+=O,O=1;break}if((i=wt(o[0],!1))["\u884c\u53f7"]?E=i["\u884c\u53f7"]-1:-1==E&&(E=0),(O=+i["number-rows-repeated"]||1)<10)for(H=0;H<O;++H)k>0&&(C[E+H]={level:k});y=-1;break;case"covered-table-cell":"/"!==o[1]&&++y,r.sheetStubs&&(r.dense?(v[E]||(v[E]=[]),v[E][y]={t:"z"}):v[ha({r:E,c:y})]={t:"z"}),b="",T=[];break;case"table-cell":case"\u6570\u636e":if("/"===o[0].charAt(o[0].length-2))++y,g=wt(o[0],!1),R=parseInt(g["number-columns-repeated"]||"1",10),c={t:"z",v:null},g.formula&&0!=r.cellFormula&&(c.f=hc(St(g.formula))),"string"==(g["\u6570\u636e\u7c7b\u578b"]||g["value-type"])&&(c.t="s",c.v=St(g["string-value"]||""),r.dense?(v[E]||(v[E]=[]),v[E][y]=c):v[ha({r:E,c:y})]=c),y+=R-1;else if("/"!==o[1]){b="",w=0,T=[],R=1;var z=O?E+O-1:E;if(++y>S.e.c&&(S.e.c=y),y<S.s.c&&(S.s.c=y),E<S.s.r&&(S.s.r=E),z>S.e.r&&(S.e.r=z),P=[],L={},c={t:(g=wt(o[0],!1))["\u6570\u636e\u7c7b\u578b"]||g["value-type"],v:null},r.cellFormula)if(g.formula&&(g.formula=St(g.formula)),g["number-matrix-columns-spanned"]&&g["number-matrix-rows-spanned"]&&(x={s:{r:E,c:y},e:{r:E+(parseInt(g["number-matrix-rows-spanned"],10)||0)-1,c:y+(parseInt(g["number-matrix-columns-spanned"],10)||0)-1}},c.F=da(x),I.push([x,c.F])),g.formula)c.f=hc(g.formula);else for(H=0;H<I.length;++H)E>=I[H][0].s.r&&E<=I[H][0].e.r&&y>=I[H][0].s.c&&y<=I[H][0].e.c&&(c.F=I[H][1]);switch((g["number-columns-spanned"]||g["number-rows-spanned"])&&(x={s:{r:E,c:y},e:{r:E+(parseInt(g["number-rows-spanned"],10)||0)-1,c:y+(parseInt(g["number-columns-spanned"],10)||0)-1}},A.push(x)),g["number-columns-repeated"]&&(R=parseInt(g["number-columns-repeated"],10)),c.t){case"boolean":c.t="b",c.v=It(g["boolean-value"]);break;case"float":case"percentage":case"currency":c.t="n",c.v=parseFloat(g.value);break;case"date":c.t="d",c.v=$e(g["date-value"]),r.cellDates||(c.t="n",c.v=Ue(c.v)),c.z="m/d/yy";break;case"time":c.t="n",c.v=Ve(g["time-value"])/86400,r.cellDates&&(c.t="d",c.v=ze(c.v)),c.z="HH:MM:SS";break;case"number":c.t="n",c.v=parseFloat(g["\u6570\u636e\u6570\u503c"]);break;default:if("string"!==c.t&&"text"!==c.t&&c.t)throw new Error("Unsupported value type "+c.t);c.t="s",null!=g["string-value"]&&(b=St(g["string-value"]),T=[])}}else{if(B=!1,"s"===c.t&&(c.v=b||"",T.length&&(c.R=T),B=0==w),F.Target&&(c.l=F),P.length>0&&(c.c=P,P=[]),b&&!1!==r.cellText&&(c.w=b),B&&(c.t="z",delete c.v),(!B||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=E))for(var V=0;V<O;++V){if(R=parseInt(g["number-columns-repeated"]||"1",10),r.dense)for(v[E+V]||(v[E+V]=[]),v[E+V][y]=0==V?c:Ke(c);--R>0;)v[E+V][y+R]=Ke(c);else for(v[ha({r:E+V,c:y})]=c;--R>0;)v[ha({r:E+V,c:y+R})]=Ke(c);S.e.c<=y&&(S.e.c=y)}y+=(R=parseInt(g["number-columns-repeated"]||"1",10))-1,R=0,c={},b="",T=[]}F={};break;case"document":case"document-content":case"\u7535\u5b50\u8868\u683c\u6587\u6863":case"spreadsheet":case"\u4e3b\u4f53":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===o[1]){if((a=f.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&f.push([o[3],!0]);break;case"annotation":if("/"===o[1]){if((a=f.pop())[0]!==o[3])throw"Bad state: "+a;L.t=b,T.length&&(L.R=T),L.a=M,P.push(L)}else"/"!==o[0].charAt(o[0].length-2)&&f.push([o[3],!1]);M="",U=0,b="",w=0,T=[];break;case"creator":"/"===o[1]?M=l.slice(U,o.index):U=o.index+o[0].length;break;case"meta":case"\u5143\u6570\u636e":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===o[1]){if((a=f.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&f.push([o[3],!1]);b="",w=0,T=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"\u7535\u5b50\u8868\u683c":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"\u6807\u9898":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===o[1]){if(_[h.name]=u,(a=f.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&(u="",h=wt(o[0],!1),f.push([o[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(f[f.length-1][0]){case"time-style":case"date-style":n=wt(o[0],!1),u+=Af[o[3]]["long"===n.style?1:0]}break;case"text":if("/>"===o[0].slice(-2))break;if("/"===o[1])switch(f[f.length-1][0]){case"number-style":case"date-style":case"time-style":u+=l.slice(d,o.index)}else d=o.index+o[0].length;break;case"named-range":D=uc((n=wt(o[0],!1))["cell-range-address"]);var G={Name:n.name,Ref:D[0]+"!"+D[1]};W&&(G.Sheet=m.length),N.Names.push(G);break;case"p":case"\u6587\u672c\u4e32":if(["master-styles"].indexOf(f[f.length-1][0])>-1)break;if("/"!==o[1]||g&&g["string-value"])wt(o[0],!1),w=o.index+o[0].length;else{var j=_f(l.slice(w,o.index));b=(b.length>0?b+"\n":"")+j[0]}break;case"database-range":if("/"===o[1])break;try{p[(D=uc(wt(o[0])["target-range-address"]))[0]]["!autofilter"]={ref:D[1]}}catch($){}break;case"a":if("/"!==o[1]){if(!(F=wt(o[0],!1)).href)break;F.Target=St(F.href),delete F.href,"#"==F.Target.charAt(0)&&F.Target.indexOf(".")>-1?(D=uc(F.Target.slice(1)),F.Target="#"+D[0]+"!"+D[1]):F.Target.match(/^\.\.[\\\/]/)&&(F.Target=F.Target.slice(3))}break;default:switch(o[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"\u8868:":case"\u5b57:":break;default:if(r.WTF)throw new Error(o)}}var X={Sheets:p,SheetNames:m,Workbook:N};return r.bookSheets&&delete X.Sheets,X}function Cf(e,t){t=t||{},at(e,"META-INF/manifest.xml")&&function(e,t){for(var r,a,n=Kt(e);r=Jt.exec(n);)switch(r[3]){case"manifest":break;case"file-entry":if("/"==(a=wt(r[0],!1)).path&&"application/vnd.oasis.opendocument.spreadsheet"!==a.type)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw r}}(st(e,"META-INF/manifest.xml"),t);var r=it(e,"content.xml");if(!r)throw new Error("Missing content.xml in ODS / UOF file");var a=xf(Lt(r),t);return at(e,"meta.xml")&&(a.Props=vn(st(e,"meta.xml"))),a}function Of(e,t){return xf(e,t)}var Rf=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Xt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return dt+t}}(),If=function(){var e="          <table:table-cell />\n",t=function(t,r,a){var n=[];n.push('      <table:table table:name="'+At(r.SheetNames[a])+'" table:style-name="ta1">\n');var s=0,i=0,o=ua(t["!ref"]||"A1"),c=t["!merges"]||[],l=0,f=Array.isArray(t);if(t["!cols"])for(i=0;i<=o.e.c;++i)n.push("        <table:table-column"+(t["!cols"][i]?' table:style-name="co'+t["!cols"][i].ods+'"':"")+"></table:table-column>\n");var h="",u=t["!rows"]||[];for(s=0;s<o.s.r;++s)h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",n.push("        <table:table-row"+h+"></table:table-row>\n");for(;s<=o.e.r;++s){for(h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",n.push("        <table:table-row"+h+">\n"),i=0;i<o.s.c;++i)n.push(e);for(;i<=o.e.c;++i){var d=!1,p={},m="";for(l=0;l!=c.length;++l)if(!(c[l].s.c>i)&&!(c[l].s.r>s)&&!(c[l].e.c<i)&&!(c[l].e.r<s)){c[l].s.c==i&&c[l].s.r==s||(d=!0),p["table:number-columns-spanned"]=c[l].e.c-c[l].s.c+1,p["table:number-rows-spanned"]=c[l].e.r-c[l].s.r+1;break}if(d)n.push("          <table:covered-table-cell/>\n");else{var v=ha({r:s,c:i}),g=f?(t[s]||[])[i]:t[v];if(g&&g.f&&(p["table:formula"]=At(("of:="+g.f.replace(yo,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),g.F&&g.F.slice(0,v.length)==v)){var b=ua(g.F);p["table:number-matrix-columns-spanned"]=b.e.c-b.s.c+1,p["table:number-matrix-rows-spanned"]=b.e.r-b.s.r+1}if(g){switch(g.t){case"b":m=g.v?"TRUE":"FALSE",p["office:value-type"]="boolean",p["office:boolean-value"]=g.v?"true":"false";break;case"n":m=g.w||String(g.v||0),p["office:value-type"]="float",p["office:value"]=g.v||0;break;case"s":case"str":m=null==g.v?"":g.v,p["office:value-type"]="string";break;case"d":m=g.w||$e(g.v).toISOString(),p["office:value-type"]="date",p["office:date-value"]=$e(g.v).toISOString(),p["table:style-name"]="ce1";break;default:n.push(e);continue}var w=At(m).replace(/  +/g,(function(e){return'<text:s text:c="'+e.length+'"/>'})).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>");if(g.l&&g.l.Target){var T=g.l.Target;"#"==(T="#"==T.charAt(0)?"#"+T.slice(1).replace(/\./,"!"):T).charAt(0)||T.match(/^\w+:/)||(T="../"+T),w=$t("text:a",w,{"xlink:href":T.replace(/&/g,"&amp;")})}n.push("          "+$t("table:table-cell",$t("text:p",w,{}),p)+"\n")}else n.push(e)}}n.push("        </table:table-row>\n")}return n.push("      </table:table>\n"),n.join("")};return function(e,r){var a=[dt],n=Xt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),s=Xt({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==r.bookType?(a.push("<office:document"+n+s+">\n"),a.push(dn().replace(/office:document-meta/g,"office:meta"))):a.push("<office:document-content"+n+">\n"),function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!cols"])for(var a=0;a<t["!cols"].length;++a)if(t["!cols"][a]){var n=t["!cols"][a];if(null==n.width&&null==n.wpx&&null==n.wch)continue;Ii(n),n.ods=r;var s=t["!cols"][a].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+s+'"/>\n'),e.push("  </style:style>\n"),++r}}));var a=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!rows"])for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=a;var n=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+a+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+n+'"/>\n'),e.push("  </style:style>\n"),++a}})),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")}(a,e),a.push("  <office:body>\n"),a.push("    <office:spreadsheet>\n");for(var i=0;i!=e.SheetNames.length;++i)a.push(t(e.Sheets[e.SheetNames[i]],e,i));return a.push("    </office:spreadsheet>\n"),a.push("  </office:body>\n"),"fods"==r.bookType?a.push("</office:document>"):a.push("</office:document-content>"),a.join("")}}();function Nf(e,t){if("fods"==t.bookType)return If(e,t);var r=ft(),a="",n=[],s=[];return lt(r,a="mimetype","application/vnd.oasis.opendocument.spreadsheet"),lt(r,a="content.xml",If(e,t)),n.push([a,"text/xml"]),s.push([a,"ContentFile"]),lt(r,a="styles.xml",Rf(e,t)),n.push([a,"text/xml"]),s.push([a,"StylesFile"]),lt(r,a="meta.xml",dt+dn()),n.push([a,"text/xml"]),s.push([a,"MetadataFile"]),lt(r,a="manifest.rdf",function(e){var t,r,a=[dt];a.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var n=0;n!=e.length;++n)a.push(un(e[n][0],e[n][1])),a.push((t="",r=e[n][0],['  <rdf:Description rdf:about="'+t+'">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+r+'"/>\n',"  </rdf:Description>\n"].join("")));return a.push(un("","Document","pkg")),a.push("</rdf:RDF>"),a.join("")}(s)),n.push([a,"application/rdf+xml"]),lt(r,a="META-INF/manifest.xml",function(e){var t=[dt];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(n)),r}function Ff(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Df(e){return"undefined"!=typeof TextDecoder?(new TextDecoder).decode(e):Lt(x(e))}function Pf(e){return"undefined"!=typeof TextEncoder?(new TextEncoder).encode(e):_(Mt(e))}function Lf(e){var t=e.reduce((function(e,t){return e+t.length}),0),r=new Uint8Array(t),a=0;return e.forEach((function(e){r.set(e,a),a+=e.length})),r}function Mf(e){return 16843009*((e=(858993459&(e-=e>>1&1431655765))+(e>>2&858993459))+(e>>4)&252645135)>>>24}function Uf(e,t){var r=t?t[0]:0,a=127&e[r];e:if(e[r++]>=128){if(a|=(127&e[r])<<7,e[r++]<128)break e;if(a|=(127&e[r])<<14,e[r++]<128)break e;if(a|=(127&e[r])<<21,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,28),++r,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,35),++r,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,42),++r,e[r++]<128)break e}return t&&(t[0]=r),a}function Bf(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383)break e;if(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)break e;if(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)break e;if(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)break e;if(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103)break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Wf(e){var t=0,r=127&e[t];e:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128)break e;if(r|=(127&e[t])<<14,e[t++]<128)break e;if(r|=(127&e[t])<<21,e[t++]<128)break e;r|=(127&e[t])<<28}return r}function Hf(e){for(var t=[],r=[0];r[0]<e.length;){var a,n=r[0],s=Uf(e,r),i=7&s,o=0;if(0==(s=Math.floor(s/8)))break;switch(i){case 0:for(var c=r[0];e[r[0]++]>=128;);a=e.slice(c,r[0]);break;case 5:o=4,a=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,a=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=Uf(e,r),a=e.slice(r[0],r[0]+o),r[0]+=o;break;default:throw new Error("PB Type ".concat(i," for Field ").concat(s," at offset ").concat(n))}var l={data:a,type:i};null==t[s]?t[s]=[l]:t[s].push(l)}return t}function zf(e){var t=[];return e.forEach((function(e,r){e.forEach((function(e){e.data&&(t.push(Bf(8*r+e.type)),2==e.type&&t.push(Bf(e.data.length)),t.push(e.data))}))})),Lf(t)}function Vf(e,t){return(null==e?void 0:e.map((function(e){return t(e.data)})))||[]}function Gf(e){for(var t,r=[],a=[0];a[0]<e.length;){var n=Uf(e,a),s=Hf(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:Wf(s[1][0].data),messages:[]};s[2].forEach((function(t){var r=Hf(t.data),n=Wf(r[3][0].data);i.messages.push({meta:r,data:e.slice(a[0],a[0]+n)}),a[0]+=n})),(null==(t=s[3])?void 0:t[0])&&(i.merge=Wf(s[3][0].data)>>>0>0),r.push(i)}return r}function jf(e){var t=[];return e.forEach((function(e){var r=[];r[1]=[{data:Bf(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:Bf(+!!e.merge),type:0}]);var a=[];e.messages.forEach((function(e){a.push(e.data),e.meta[3]=[{type:0,data:Bf(e.data.length)}],r[2].push({data:zf(e.meta),type:2})}));var n=zf(r);t.push(Bf(n.length)),t.push(n),a.forEach((function(e){return t.push(e)}))})),Lf(t)}function Xf(e,t){if(0!=e)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],a=Uf(t,r),n=[];r[0]<t.length;){var s=3&t[r[0]];if(0!=s){var i=0,o=0;if(1==s?(o=4+(t[r[0]]>>2&7),i=(224&t[r[0]++])<<3,i|=t[r[0]++]):(o=1+(t[r[0]++]>>2),2==s?(i=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(i=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),n=[Lf(n)],0==i)throw new Error("Invalid offset 0");if(i>n[0].length)throw new Error("Invalid offset beyond length");if(o>=i)for(n.push(n[0].slice(-i)),o-=i;o>=n[n.length-1].length;)n.push(n[n.length-1]),o-=n[n.length-1].length;n.push(n[0].slice(-i,-i+o))}else{var c=t[r[0]++]>>2;if(c<60)++c;else{var l=c-59;c=t[r[0]],l>1&&(c|=t[r[0]+1]<<8),l>2&&(c|=t[r[0]+2]<<16),l>3&&(c|=t[r[0]+3]<<24),c>>>=0,c++,r[0]+=l}n.push(t.slice(r[0],r[0]+c)),r[0]+=c}}var f=Lf(n);if(f.length!=a)throw new Error("Unexpected length: ".concat(f.length," != ").concat(a));return f}function $f(e){for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(Xf(a,e.slice(r,r+n))),r+=n}if(r!==e.length)throw new Error("data is not a valid framed stream!");return Lf(t)}function Yf(e){for(var t=[],r=0;r<e.length;){var a=Math.min(e.length-r,268435455),n=new Uint8Array(4);t.push(n);var s=Bf(a),i=s.length;t.push(s),a<=60?(i++,t.push(new Uint8Array([a-1<<2]))):a<=256?(i+=2,t.push(new Uint8Array([240,a-1&255]))):a<=65536?(i+=3,t.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=16777216?(i+=4,t.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=4294967296&&(i+=5,t.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),t.push(e.slice(r,r+a)),i+=a,n[0]=0,n[1]=255&i,n[2]=i>>8&255,n[3]=i>>16&255,r+=a}return Lf(t)}function Kf(e,t,r){var a,n=Ff(e),s=n.getUint32(8,!0),i=12,o=-1,c=-1,l=NaN,f=NaN,h=new Date(2001,0,1);switch(1&s&&(l=function(e,t){for(var r=(127&e[t+15])<<7|e[t+14]>>1,a=1&e[t+14],n=t+13;n>=t;--n)a=256*a+e[n];return(128&e[t+15]?-a:a)*Math.pow(10,r-6176)}(e,i),i+=16),2&s&&(f=n.getFloat64(i,!0),i+=8),4&s&&(h.setTime(h.getTime()+1e3*n.getFloat64(i,!0)),i+=8),8&s&&(c=n.getUint32(i,!0),i+=4),16&s&&(o=n.getUint32(i,!0),i+=4),e[1]){case 0:break;case 2:case 10:a={t:"n",v:l};break;case 3:a={t:"s",v:t[c]};break;case 5:a={t:"d",v:h};break;case 6:a={t:"b",v:f>0};break;case 7:a={t:"n",v:f/86400};break;case 8:a={t:"e",v:0};break;case 9:if(!(o>-1))throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)));a={t:"s",v:r[o]};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)))}return a}function Jf(e,t){var r=new Uint8Array(32),a=Ff(r),n=12,s=0;switch(r[0]=5,e.t){case"n":r[1]=2,function(e,t,r){var a=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,n=r/Math.pow(10,a-6176);e[t+15]|=a>>7,e[t+14]|=(127&a)<<1;for(var s=0;n>=1;++s,n/=256)e[t+s]=255&n;e[t+15]|=r>=0?0:128}(r,n,e.v),s|=1,n+=16;break;case"b":r[1]=6,a.setFloat64(n,e.v?1:0,!0),s|=2,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=8,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,s,!0),r.slice(0,n)}function qf(e,t){var r=new Uint8Array(32),a=Ff(r),n=12,s=0;switch(r[0]=3,e.t){case"n":r[2]=2,a.setFloat64(n,e.v,!0),s|=32,n+=8;break;case"b":r[2]=6,a.setFloat64(n,e.v?1:0,!0),s|=32,n+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,a.setUint32(n,t.indexOf(e.v),!0),s|=16,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(4,s,!0),r.slice(0,n)}function Zf(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:return function(e,t,r,a){var n,s=Ff(e),i=s.getUint32(4,!0),o=(a>1?12:8)+4*Mf(i&(a>1?3470:398)),c=-1,l=-1,f=NaN,h=new Date(2001,0,1);switch(512&i&&(c=s.getUint32(o,!0),o+=4),o+=4*Mf(i&(a>1?12288:4096)),16&i&&(l=s.getUint32(o,!0),o+=4),32&i&&(f=s.getFloat64(o,!0),o+=8),64&i&&(h.setTime(h.getTime()+1e3*s.getFloat64(o,!0)),o+=8),e[2]){case 0:break;case 2:n={t:"n",v:f};break;case 3:n={t:"s",v:t[l]};break;case 5:n={t:"d",v:h};break;case 6:n={t:"b",v:f>0};break;case 7:n={t:"n",v:f/86400};break;case 8:n={t:"e",v:0};break;case 9:if(c>-1)n={t:"s",v:r[c]};else if(l>-1)n={t:"s",v:t[l]};else{if(isNaN(f))throw new Error("Unsupported cell type ".concat(e.slice(0,4)));n={t:"n",v:f}}break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return n}(e,t,r,e[0]);case 5:return Kf(e,t,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function Qf(e){return Uf(Hf(e)[1][0].data)}function eh(e,t){var r=Hf(t.data),a=Wf(r[1][0].data),n=r[3],s=[];return(n||[]).forEach((function(t){var r=Hf(t.data),n=Wf(r[1][0].data)>>>0;switch(a){case 1:s[n]=Df(r[3][0].data);break;case 8:var i=Hf(e[Qf(r[9][0].data)][0].data),o=e[Qf(i[1][0].data)][0],c=Wf(o.meta[1][0].data);if(2001!=c)throw new Error("2000 unexpected reference to ".concat(c));var l=Hf(o.data);s[n]=l[3].map((function(e){return Df(e.data)})).join("")}})),s}function th(e,t){var r,a=Hf(t.data),n=(null==(r=null==a?void 0:a[7])?void 0:r[0])?Wf(a[7][0].data)>>>0>0?1:0:-1,s=Vf(a[5],(function(e){return function(e,t){var r,a,n,s,i,o,c,l,f,h,u,d,p,m,v,g,b=Hf(e),w=Wf(b[1][0].data)>>>0,T=Wf(b[2][0].data)>>>0,E=(null==(a=null==(r=b[8])?void 0:r[0])?void 0:a.data)&&Wf(b[8][0].data)>0||!1;if((null==(s=null==(n=b[7])?void 0:n[0])?void 0:s.data)&&0!=t)v=null==(o=null==(i=b[7])?void 0:i[0])?void 0:o.data,g=null==(l=null==(c=b[6])?void 0:c[0])?void 0:l.data;else{if(!(null==(h=null==(f=b[4])?void 0:f[0])?void 0:h.data)||1==t)throw"NUMBERS Tile missing ".concat(t," cell storage");v=null==(d=null==(u=b[4])?void 0:u[0])?void 0:d.data,g=null==(m=null==(p=b[3])?void 0:p[0])?void 0:m.data}for(var y=E?4:1,S=Ff(v),k=[],_=0;_<v.length/2;++_){var A=S.getUint16(2*_,!0);A<65535&&k.push([_,A])}if(k.length!=T)throw"Expected ".concat(T," cells, found ").concat(k.length);var x=[];for(_=0;_<k.length-1;++_)x[k[_][0]]=g.subarray(k[_][1]*y,k[_+1][1]*y);return k.length>=1&&(x[k[k.length-1][0]]=g.subarray(k[k.length-1][1]*y)),{R:w,cells:x}}(e,n)}));return{nrows:Wf(a[4][0].data)>>>0,data:s.reduce((function(e,t){return e[t.R]||(e[t.R]=[]),t.cells.forEach((function(r,a){if(e[t.R][a])throw new Error("Duplicate cell r=".concat(t.R," c=").concat(a));e[t.R][a]=r})),e}),[])}}function rh(e,t){var r={"!ref":"A1"},a=e[Qf(Hf(t.data)[2][0].data)],n=Wf(a[0].meta[1][0].data);if(6001!=n)throw new Error("6000 unexpected reference to ".concat(n));return function(e,t,r){var a,n=Hf(t.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(Wf(n[6][0].data)>>>0)-1,s.e.r<0)throw new Error("Invalid row varint ".concat(n[6][0].data));if(s.e.c=(Wf(n[7][0].data)>>>0)-1,s.e.c<0)throw new Error("Invalid col varint ".concat(n[7][0].data));r["!ref"]=da(s);var i=Hf(n[4][0].data),o=eh(e,e[Qf(i[4][0].data)][0]),c=(null==(a=i[17])?void 0:a[0])?eh(e,e[Qf(i[17][0].data)][0]):[],l=Hf(i[3][0].data),f=0;l[1].forEach((function(t){var a=Hf(t.data),n=e[Qf(a[2][0].data)][0],s=Wf(n.meta[1][0].data);if(6002!=s)throw new Error("6001 unexpected reference to ".concat(s));var i=th(0,n);i.data.forEach((function(e,t){e.forEach((function(e,a){var n=ha({r:f+t,c:a}),s=Zf(e,o,c);s&&(r[n]=s)}))})),f+=i.nrows}))}(e,a[0],r),r}function ah(e,t){var r={SheetNames:[],Sheets:{}};if(Vf(Hf(t.data)[1],Qf).forEach((function(t){e[t].forEach((function(t){if(2==Wf(t.meta[1][0].data)){var a=function(e,t){var r,a=Hf(t.data),n={name:(null==(r=a[1])?void 0:r[0])?Df(a[1][0].data):"",sheets:[]};return Vf(a[2],Qf).forEach((function(t){e[t].forEach((function(t){6e3==Wf(t.meta[1][0].data)&&n.sheets.push(rh(e,t))}))})),n}(e,t);a.sheets.forEach((function(e,t){Bh(r,e,0==t?a.name:a.name+"_"+t,!0)}))}}))})),0==r.SheetNames.length)throw new Error("Empty NUMBERS file");return r}function nh(e){var t,r,a,n,s={},i=[];if(e.FullPaths.forEach((function(e){if(e.match(/\.iwpv2/))throw new Error("Unsupported password protection")})),e.FileIndex.forEach((function(e){if(e.name.match(/\.iwa$/)){var t,r;try{t=$f(e.content)}catch(a){return console.log("?? "+e.content.length+" "+(a.message||a))}try{r=Gf(t)}catch(a){return console.log("## "+(a.message||a))}r.forEach((function(e){s[e.id]=e.messages,i.push(e.id)}))}})),!i.length)throw new Error("File has no messages");var o=(null==(n=null==(a=null==(r=null==(t=null==s?void 0:s[1])?void 0:t[0])?void 0:r.meta)?void 0:a[1])?void 0:n[0].data)&&1==Wf(s[1][0].meta[1][0].data)&&s[1][0];if(o||i.forEach((function(e){s[e].forEach((function(e){if(1==Wf(e.meta[1][0].data)>>>0){if(o)throw new Error("Document has multiple roots");o=e}}))})),!o)throw new Error("Cannot find Document root");return ah(s,o)}function sh(e,t,r){var a,n,s,i;if(!(null==(a=e[6])?void 0:a[0])||!(null==(n=e[7])?void 0:n[0]))throw"Mutation only works on post-BNC storages!";if((null==(i=null==(s=e[8])?void 0:s[0])?void 0:i.data)&&Wf(e[8][0].data)>0||!1)throw"Math only works with normal offsets";for(var o=0,c=Ff(e[7][0].data),l=0,f=[],h=Ff(e[4][0].data),u=0,d=[],p=0;p<t.length;++p)if(null!=t[p]){var m,v;switch(c.setUint16(2*p,l,!0),h.setUint16(2*p,u,!0),typeof t[p]){case"string":m=Jf({t:"s",v:t[p]},r),v=qf({t:"s",v:t[p]},r);break;case"number":m=Jf({t:"n",v:t[p]},r),v=qf({t:"n",v:t[p]},r);break;case"boolean":m=Jf({t:"b",v:t[p]},r),v=qf({t:"b",v:t[p]},r);break;default:throw new Error("Unsupported value "+t[p])}f.push(m),l+=m.length,d.push(v),u+=v.length,++o}else c.setUint16(2*p,65535,!0),h.setUint16(2*p,65535);for(e[2][0].data=Bf(o);p<e[7][0].data.length/2;++p)c.setUint16(2*p,65535,!0),h.setUint16(2*p,65535,!0);return e[6][0].data=Lf(f),e[3][0].data=Lf(d),o}function ih(e){return function(t){for(var r=0;r!=e.length;++r){var a=e[r];void 0===t[a[0]]&&(t[a[0]]=a[1]),"n"===a[2]&&(t[a[0]]=Number(t[a[0]]))}}}function oh(e){ih([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function ch(e){ih([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function lh(e,t,r,a,n,s,i,o,c,l,f,h){try{s[a]=ln(it(e,r,!0),t);var u,d=st(e,t);switch(o){case"sheet":u=bl(d,t,n,c,s[a],l,f,h);break;case"chart":if(!(u=wl(d,t,n,c,s[a],l))||!u["!drawel"])break;var p=ut(u["!drawel"].Target,t),m=cn(p),v=function(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return t["!id"][r].Target}(it(e,p,!0),ln(it(e,m,!0),p)),g=ut(v,p),b=cn(g);u=tl(it(e,g,!0),0,0,ln(it(e,b,!0),g),0,u);break;case"macro":T=t,s[a],T.slice(-4),u={"!type":"macro"};break;case"dialog":u=function(e,t,r,a,n,s,i,o){return t.slice(-4),{"!type":"dialog"}}(0,t,0,0,s[a]);break;default:throw new Error("Unrecognized sheet type "+o)}i[a]=u;var w=[];s&&s[a]&&Fe(s[a]).forEach((function(r){var n="";if(s[a][r].Type==on.CMNT){n=ut(s[a][r].Target,t);var i=yl(st(e,n,!0),n,c);if(!i||!i.length)return;mo(u,i,!1)}s[a][r].Type==on.TCMNT&&(n=ut(s[a][r].Target,t),w=w.concat(function(e,t){var r=[],a=!1,n={},s=0;return e.replace(vt,(function(i,o){var c=wt(i);switch(Tt(c[0])){case"<?xml":case"<ThreadedComments":case"</ThreadedComments>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<threadedComment":n={author:c.personId,guid:c.id,ref:c.ref,T:1};break;case"</threadedComment>":null!=n.t&&r.push(n);break;case"<text>":case"<text":s=o+i.length;break;case"</text>":n.t=e.slice(s,o).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":case"<mentions>":case"<ext":a=!0;break;case"</mentions>":case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+c[0]+" in threaded comments")}return i})),r}(st(e,n,!0),c)))})),w&&w.length&&mo(u,w,!0,c.people||[])}catch(E){if(c.WTF)throw E}var T}function fh(e){return"/"==e.charAt(0)?e.slice(1):e}function hh(e,t){if(Se(),oh(t=t||{}),at(e,"META-INF/manifest.xml"))return Cf(e,t);if(at(e,"objectdata.xml"))return Cf(e,t);if(at(e,"Index/Document.iwa")){if("undefined"==typeof Uint8Array)throw new Error("NUMBERS file parsing requires Uint8Array support");if(e.FileIndex)return nh(e);var r=Ce.utils.cfb_new();return ct(e).forEach((function(t){lt(r,t,ot(e,t))})),nh(r)}if(!at(e,"[Content_Types].xml")){if(at(e,"index.xml.gz"))throw new Error("Unsupported NUMBERS 08 file");if(at(e,"index.xml"))throw new Error("Unsupported NUMBERS 09 file");throw new Error("Unsupported ZIP file")}var a,n,s=ct(e),i=function(e){var t={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};if(!e||!e.match)return t;var r={};if((e.match(vt)||[]).forEach((function(e){var a=wt(e);switch(a[0].replace(gt,"<")){case"<?xml":break;case"<Types":t.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[a.Extension]=a.ContentType;break;case"<Override":void 0!==t[an[a.ContentType]]&&t[an[a.ContentType]].push(a.PartName)}})),t.xmlns!==er)throw new Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}(it(e,"[Content_Types].xml")),o=!1;if(0===i.workbooks.length&&st(e,n="xl/workbook.xml",!0)&&i.workbooks.push(n),0===i.workbooks.length){if(!st(e,n="xl/workbook.bin",!0))throw new Error("Could not find workbook");i.workbooks.push(n),o=!0}"bin"==i.workbooks[0].slice(-3)&&(o=!0);var c={},l={};if(!t.bookSheets&&!t.bookProps){if(dc=[],i.sst)try{dc=El(st(e,fh(i.sst)),i.sst,t)}catch(R){if(t.WTF)throw R}t.cellStyles&&i.themes.length&&(c=function(e,t,r){return io(e,r)}(it(e,i.themes[0].replace(/^\//,""),!0)||"",i.themes[0],t)),i.style&&(l=Tl(st(e,fh(i.style)),i.style,c,t))}i.links.map((function(r){try{ln(it(e,cn(fh(r))),r);return kl(st(e,fh(r)),0,r,t)}catch(R){}}));var f=gl(st(e,fh(i.workbooks[0])),i.workbooks[0],t),h={},u="";i.coreprops.length&&((u=st(e,fh(i.coreprops[0]),!0))&&(h=vn(u)),0!==i.extprops.length&&(u=st(e,fh(i.extprops[0]),!0))&&function(e,t,r){var a={};t||(t={}),e=Lt(e),wn.forEach((function(r){var n=(e.match(Ut(r[0]))||[])[1];switch(r[2]){case"string":n&&(t[r[1]]=St(n));break;case"bool":t[r[1]]="true"===n;break;case"raw":var s=e.match(new RegExp("<"+r[0]+"[^>]*>([\\s\\S]*?)</"+r[0]+">"));s&&s.length>0&&(a[r[1]]=s[1])}})),a.HeadingPairs&&a.TitlesOfParts&&En(a.HeadingPairs,a.TitlesOfParts,t,r)}(u,h,t));var d={};t.bookSheets&&!t.bookProps||0!==i.custprops.length&&(u=it(e,fh(i.custprops[0]),!0))&&(d=function(e,t){var r={},a="",n=e.match(Sn);if(n)for(var s=0;s!=n.length;++s){var i=n[s],o=wt(i);switch(o[0]){case"<?xml":case"<Properties":break;case"<property":a=St(o.name);break;case"</property>":a=null;break;default:if(0===i.indexOf("<vt:")){var c=i.split(">"),l=c[0].slice(4),f=c[1];switch(l){case"lpstr":case"bstr":case"lpwstr":case"cy":case"error":r[a]=St(f);break;case"bool":r[a]=It(f);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[a]=parseInt(f,10);break;case"r4":case"r8":case"decimal":r[a]=parseFloat(f);break;case"filetime":case"date":r[a]=$e(f);break;default:if("/"==l.slice(-1))break;t.WTF&&"undefined"!==typeof console&&console.warn("Unexpected",i,l,c)}}else if("</"===i.slice(0,2));else if(t.WTF)throw new Error(i)}}return r}(u,t));var p={};if((t.bookSheets||t.bookProps)&&(f.Sheets?a=f.Sheets.map((function(e){return e.name})):h.Worksheets&&h.SheetNames.length>0&&(a=h.SheetNames),t.bookProps&&(p.Props=h,p.Custprops=d),t.bookSheets&&"undefined"!==typeof a&&(p.SheetNames=a),t.bookSheets?p.SheetNames:t.bookProps))return p;a={};var m={};t.bookDeps&&i.calcchain&&(m=Sl(st(e,fh(i.calcchain)),i.calcchain));var v,g,b=0,w={},T=f.Sheets;h.Worksheets=T.length,h.SheetNames=[];for(var E=0;E!=T.length;++E)h.SheetNames[E]=T[E].name;var y=o?"bin":"xml",S=i.workbooks[0].lastIndexOf("/"),k=(i.workbooks[0].slice(0,S+1)+"_rels/"+i.workbooks[0].slice(S+1)+".rels").replace(/^\//,"");at(e,k)||(k="xl/_rels/workbook."+y+".rels");var _=ln(it(e,k,!0),k.replace(/_rels.*/,"s5s"));(i.metadata||[]).length>=1&&(t.xlmeta=_l(st(e,fh(i.metadata[0])),i.metadata[0],t)),(i.people||[]).length>=1&&(t.people=function(e,t){var r=[],a=!1;return e.replace(vt,(function(e){var n=wt(e);switch(Tt(n[0])){case"<?xml":case"<personList":case"</personList>":case"</person>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<person":r.push({name:n.displayname,id:n.id});break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+n[0]+" in threaded comments")}return e})),r}(st(e,fh(i.people[0])),t)),_&&(_=function(e,t){if(!e)return 0;try{e=t.map((function(t){return t.id||(t.id=t.strRelID),[t.name,e["!id"][t.id].Target,(r=e["!id"][t.id].Type,on.WS.indexOf(r)>-1?"sheet":on.CS&&r==on.CS?"chart":on.DS&&r==on.DS?"dialog":on.MS&&r==on.MS?"macro":r&&r.length?r:"sheet")];var r}))}catch(R){return null}return e&&0!==e.length?e:null}(_,f.Sheets));var A=st(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(b=0;b!=h.Worksheets;++b){var x="sheet";if(_&&_[b]?(v="xl/"+_[b][1].replace(/[\/]?xl\//,""),at(e,v)||(v=_[b][1]),at(e,v)||(v=k.replace(/_rels\/.*$/,"")+_[b][1]),x=_[b][2]):v=(v="xl/worksheets/sheet"+(b+1-A)+"."+y).replace(/sheet0\./,"sheet."),g=v.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&null!=t.sheets)switch(typeof t.sheets){case"number":if(b!=t.sheets)continue e;break;case"string":if(h.SheetNames[b].toLowerCase()!=t.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var C=!1,O=0;O!=t.sheets.length;++O)"number"==typeof t.sheets[O]&&t.sheets[O]==b&&(C=1),"string"==typeof t.sheets[O]&&t.sheets[O].toLowerCase()==h.SheetNames[b].toLowerCase()&&(C=1);if(!C)continue e}}lh(e,v,g,h.SheetNames[b],b,w,a,x,t,f,c,l)}return p={Directory:i,Workbook:f,Props:h,Custprops:d,Deps:m,Sheets:a,SheetNames:h.SheetNames,Strings:dc,Styles:l,Themes:c,SSF:Ke(H)},t&&t.bookFiles&&(e.files?(p.keys=s,p.files=e.files):(p.keys=[],p.files={},e.FullPaths.forEach((function(t,r){t=t.replace(/^Root Entry[\/]/,""),p.keys.push(t),p.files[t]=e.FileIndex[r]})))),t&&t.bookVBA&&(i.vba.length>0?p.vbaraw=st(e,fh(i.vba[0]),!0):i.defaults&&"application/vnd.ms-office.vbaProject"===i.defaults.bin&&(p.vbaraw=st(e,"xl/vbaProject.bin",!0))),p}function uh(e,t){var r=t||{},a="Workbook",n=Ce.find(e,a);try{if(a="/!DataSpaces/Version",!(n=Ce.find(e,a))||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(function(e){var t={};t.id=e.read_shift(0,"lpp4"),t.R=fi(e,4),t.U=fi(e,4),t.W=fi(e,4)}(n.content),a="/!DataSpaces/DataSpaceMap",!(n=Ce.find(e,a))||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var s=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(hi(e));return t}(n.content);if(1!==s.length||1!==s[0].comps.length||0!==s[0].comps[0].t||"StrongEncryptionDataSpace"!==s[0].name||"EncryptedPackage"!==s[0].comps[0].v)throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",!(n=Ce.find(e,a))||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var i=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}(n.content);if(1!=i.length||"StrongEncryptionTransform"!=i[0])throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",!(n=Ce.find(e,a))||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);ui(n.content)}catch(c){}if(a="/EncryptionInfo",!(n=Ce.find(e,a))||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var o=function(e){var t=fi(e);switch(t.Minor){case 2:return[t.Minor,mi(e)];case 3:return[t.Minor,vi()];case 4:return[t.Minor,gi(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+t.Minor)}(n.content);if(a="/EncryptedPackage",!(n=Ce.find(e,a))||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(4==o[0]&&"undefined"!==typeof decrypt_agile)return decrypt_agile(o[1],n.content,r.password||"",r);if(2==o[0]&&"undefined"!==typeof decrypt_std76)return decrypt_std76(o[1],n.content,r.password||"",r);throw new Error("File is password-protected")}function dh(e,t){return"ods"==t.bookType?Nf(e,t):"numbers"==t.bookType?function(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var a=ua(r["!ref"]);a.s.r=a.s.c=0;var n=!1;a.e.c>9&&(n=!0,a.e.c=9),a.e.r>49&&(n=!0,a.e.r=49),n&&console.error("The Numbers writer is currently limited to ".concat(da(a)));var s=Ih(r,{range:a,header:1}),i=["~Sh33tJ5~"];s.forEach((function(e){return e.forEach((function(e){"string"==typeof e&&i.push(e)}))}));var o={},c=[],l=Ce.read(t.numbers,{type:"base64"});l.FileIndex.map((function(e,t){return[e,l.FullPaths[t]]})).forEach((function(e){var t=e[0],r=e[1];2==t.type&&t.name.match(/\.iwa/)&&Gf($f(t.content)).forEach((function(e){c.push(e.id),o[e.id]={deps:[],location:r,type:Wf(e.messages[0].meta[1][0].data)}}))})),c.sort((function(e,t){return e-t}));var f=c.filter((function(e){return e>1})).map((function(e){return[e,Bf(e)]}));l.FileIndex.map((function(e,t){return[e,l.FullPaths[t]]})).forEach((function(e){var t=e[0];e[1],t.name.match(/\.iwa/)&&Gf($f(t.content)).forEach((function(e){e.messages.forEach((function(t){f.forEach((function(t){e.messages.some((function(e){return 11006!=Wf(e.meta[1][0].data)&&function(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var a=0;a<t.length;++a)if(e[r+a]!=t[a])continue e;return!0}return!1}(e.data,t[1])}))&&o[t[0]].deps.push(e.id)}))}))}))}));for(var h,u=Ce.find(l,o[1].location),d=Gf($f(u.content)),p=0;p<d.length;++p){var m=d[p];1==m.id&&(h=m)}var v=Qf(Hf(h.messages[0].data)[1][0].data);for(d=Gf($f((u=Ce.find(l,o[v].location)).content)),p=0;p<d.length;++p)(m=d[p]).id==v&&(h=m);for(v=Qf(Hf(h.messages[0].data)[2][0].data),d=Gf($f((u=Ce.find(l,o[v].location)).content)),p=0;p<d.length;++p)(m=d[p]).id==v&&(h=m);for(v=Qf(Hf(h.messages[0].data)[2][0].data),d=Gf($f((u=Ce.find(l,o[v].location)).content)),p=0;p<d.length;++p)(m=d[p]).id==v&&(h=m);var g=Hf(h.messages[0].data);g[6][0].data=Bf(a.e.r+1),g[7][0].data=Bf(a.e.c+1);for(var b=Qf(g[46][0].data),w=Ce.find(l,o[b].location),T=Gf($f(w.content)),E=0;E<T.length&&T[E].id!=b;++E);if(T[E].id!=b)throw"Bad ColumnRowUIDMapArchive";var y=Hf(T[E].messages[0].data);y[1]=[],y[2]=[],y[3]=[];for(var S=0;S<=a.e.c;++S){var k=[];k[1]=k[2]=[{type:0,data:Bf(S+420690)}],y[1].push({type:2,data:zf(k)}),y[2].push({type:0,data:Bf(S)}),y[3].push({type:0,data:Bf(S)})}y[4]=[],y[5]=[],y[6]=[];for(var _=0;_<=a.e.r;++_)(k=[])[1]=k[2]=[{type:0,data:Bf(_+726270)}],y[4].push({type:2,data:zf(k)}),y[5].push({type:0,data:Bf(_)}),y[6].push({type:0,data:Bf(_)});T[E].messages[0].data=zf(y),w.content=Yf(jf(T)),w.size=w.content.length,delete g[46];var A=Hf(g[4][0].data);A[7][0].data=Bf(a.e.r+1);var x=Qf(Hf(A[1][0].data)[2][0].data);if((T=Gf($f((w=Ce.find(l,o[x].location)).content)))[0].id!=x)throw"Bad HeaderStorageBucket";var C=Hf(T[0].messages[0].data);for(_=0;_<s.length;++_){var O=Hf(C[2][0].data);O[1][0].data=Bf(_),O[4][0].data=Bf(s[_].length),C[2][_]={type:C[2][0].type,data:zf(O)}}T[0].messages[0].data=zf(C),w.content=Yf(jf(T)),w.size=w.content.length;var R=Qf(A[2][0].data);if((T=Gf($f((w=Ce.find(l,o[R].location)).content)))[0].id!=R)throw"Bad HeaderStorageBucket";for(C=Hf(T[0].messages[0].data),S=0;S<=a.e.c;++S)(O=Hf(C[2][0].data))[1][0].data=Bf(S),O[4][0].data=Bf(a.e.r+1),C[2][S]={type:C[2][0].type,data:zf(O)};T[0].messages[0].data=zf(C),w.content=Yf(jf(T)),w.size=w.content.length;var I=Qf(A[4][0].data);!function(){for(var e,t=Ce.find(l,o[I].location),r=Gf($f(t.content)),a=0;a<r.length;++a){var n=r[a];n.id==I&&(e=n)}var s=Hf(e.messages[0].data);s[3]=[];var c=[];i.forEach((function(e,t){c[1]=[{type:0,data:Bf(t)}],c[2]=[{type:0,data:Bf(1)}],c[3]=[{type:2,data:Pf(e)}],s[3].push({type:2,data:zf(c)})})),e.messages[0].data=zf(s);var f=Yf(jf(r));t.content=f,t.size=t.content.length}();var N=Hf(A[3][0].data),F=N[1][0];delete N[2];var D=Hf(F.data),P=Qf(D[2][0].data);!function(){for(var e,t=Ce.find(l,o[P].location),r=Gf($f(t.content)),n=0;n<r.length;++n){var c=r[n];c.id==P&&(e=c)}var f=Hf(e.messages[0].data);delete f[6],delete N[7];var h=new Uint8Array(f[5][0].data);f[5]=[];for(var u=0,d=0;d<=a.e.r;++d){var p=Hf(h);u+=sh(p,s[d],i),p[1][0].data=Bf(d),f[5].push({data:zf(p),type:2})}f[1]=[{type:0,data:Bf(a.e.c+1)}],f[2]=[{type:0,data:Bf(a.e.r+1)}],f[3]=[{type:0,data:Bf(u)}],f[4]=[{type:0,data:Bf(a.e.r+1)}],e.messages[0].data=zf(f);var m=Yf(jf(r));t.content=m,t.size=t.content.length}(),F.data=zf(D),A[3][0].data=zf(N),g[4][0].data=zf(A),h.messages[0].data=zf(g);var L=Yf(jf(d));return u.content=L,u.size=u.content.length,l}(e,t):"xlsb"==t.bookType?function(e,t){uo=1024,e&&!e.SSF&&(e.SSF=Ke(H));e&&e.SSF&&(Se(),ye(e.SSF),t.revssf=Le(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF);t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,mc?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xlsb"==t.bookType?"bin":"xml",a=To.indexOf(t.bookType)>-1,n={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};ch(t=t||{});var s=ft(),i="",o=0;t.cellXfs=[],wc(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});if(lt(s,i="docProps/core.xml",bn(e.Props,t)),n.coreprops.push(i),hn(t.rels,2,i,on.CORE_PROPS),i="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var c=[],l=0;l<e.SheetNames.length;++l)2!=(e.Workbook.Sheets[l]||{}).Hidden&&c.push(e.SheetNames[l]);e.Props.SheetNames=c}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,lt(s,i,yn(e.Props)),n.extprops.push(i),hn(t.rels,3,i,on.EXT_PROPS),e.Custprops!==e.Props&&Fe(e.Custprops||{}).length>0&&(lt(s,i="docProps/custom.xml",kn(e.Custprops)),n.custprops.push(i),hn(t.rels,4,i,on.CUST_PROPS));for(o=1;o<=e.SheetNames.length;++o){var f={"!id":{}},h=e.Sheets[e.SheetNames[o-1]];(h||{})["!type"];if(lt(s,i="xl/worksheets/sheet"+o+"."+r,Al(o-1,i,t,e,f)),n.sheets.push(i),hn(t.wbrels,-1,"worksheets/sheet"+o+"."+r,on.WS[0]),h){var u=h["!comments"],d=!1,p="";u&&u.length>0&&(lt(s,p="xl/comments"+o+"."+r,xl(u,p,t)),n.comments.push(p),hn(f,-1,"../comments"+o+"."+r,on.CMNT),d=!0),h["!legacy"]&&d&&lt(s,"xl/drawings/vmlDrawing"+o+".vml",po(o,h["!comments"])),delete h["!comments"],delete h["!legacy"]}f["!id"].rId1&&lt(s,cn(i),fn(f))}null!=t.Strings&&t.Strings.length>0&&(lt(s,i="xl/sharedStrings."+r,function(e,t,r){return(".bin"===t.slice(-4)?ci:ii)(e,r)}(t.Strings,i,t)),n.strs.push(i),hn(t.wbrels,-1,"sharedStrings."+r,on.SST));lt(s,i="xl/workbook."+r,function(e,t,r){return(".bin"===t.slice(-4)?vl:dl)(e,r)}(e,i,t)),n.workbooks.push(i),hn(t.rels,1,i,on.WB),lt(s,i="xl/theme/theme1.xml",oo(e.Themes,t)),n.themes.push(i),hn(t.wbrels,-1,"theme/theme1.xml",on.THEME),lt(s,i="xl/styles."+r,function(e,t,r){return(".bin"===t.slice(-4)?qi:Ui)(e,r)}(e,i,t)),n.styles.push(i),hn(t.wbrels,-1,"styles."+r,on.STY),e.vbaraw&&a&&(lt(s,i="xl/vbaProject.bin",e.vbaraw),n.vba.push(i),hn(t.wbrels,-1,"vbaProject.bin",on.VBA));return lt(s,i="xl/metadata."+r,function(e){return(".bin"===e.slice(-4)?fo:ho)()}(i)),n.metadata.push(i),hn(t.wbrels,-1,"metadata."+r,on.XLMETA),lt(s,"[Content_Types].xml",sn(n,t)),lt(s,"_rels/.rels",fn(t.rels)),lt(s,"xl/_rels/workbook."+r+".rels",fn(t.wbrels)),delete t.revssf,delete t.ssf,s}(e,t):ph(e,t)}function ph(e,t){uo=1024,e&&!e.SSF&&(e.SSF=Ke(H)),e&&e.SSF&&(Se(),ye(e.SSF),t.revssf=Le(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,mc?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",a=To.indexOf(t.bookType)>-1,n={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};ch(t=t||{});var s=ft(),i="",o=0;if(t.cellXfs=[],wc(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),lt(s,i="docProps/core.xml",bn(e.Props,t)),n.coreprops.push(i),hn(t.rels,2,i,on.CORE_PROPS),i="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var c=[],l=0;l<e.SheetNames.length;++l)2!=(e.Workbook.Sheets[l]||{}).Hidden&&c.push(e.SheetNames[l]);e.Props.SheetNames=c}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,lt(s,i,yn(e.Props)),n.extprops.push(i),hn(t.rels,3,i,on.EXT_PROPS),e.Custprops!==e.Props&&Fe(e.Custprops||{}).length>0&&(lt(s,i="docProps/custom.xml",kn(e.Custprops)),n.custprops.push(i),hn(t.rels,4,i,on.CUST_PROPS));var f=["SheetJ5"];for(t.tcid=0,o=1;o<=e.SheetNames.length;++o){var h={"!id":{}},u=e.Sheets[e.SheetNames[o-1]];(u||{})["!type"];if(lt(s,i="xl/worksheets/sheet"+o+"."+r,Bc(o-1,t,e,h)),n.sheets.push(i),hn(t.wbrels,-1,"worksheets/sheet"+o+"."+r,on.WS[0]),u){var d=u["!comments"],p=!1,m="";if(d&&d.length>0){var v=!1;d.forEach((function(e){e[1].forEach((function(e){1==e.T&&(v=!0)}))})),v&&(lt(s,m="xl/threadedComments/threadedComment"+o+"."+r,go(d,f,t)),n.threadedcomments.push(m),hn(h,-1,"../threadedComments/threadedComment"+o+"."+r,on.TCMNT)),lt(s,m="xl/comments"+o+"."+r,vo(d)),n.comments.push(m),hn(h,-1,"../comments"+o+"."+r,on.CMNT),p=!0}u["!legacy"]&&p&&lt(s,"xl/drawings/vmlDrawing"+o+".vml",po(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}h["!id"].rId1&&lt(s,cn(i),fn(h))}return null!=t.Strings&&t.Strings.length>0&&(lt(s,i="xl/sharedStrings."+r,ii(t.Strings,t)),n.strs.push(i),hn(t.wbrels,-1,"sharedStrings."+r,on.SST)),lt(s,i="xl/workbook."+r,dl(e)),n.workbooks.push(i),hn(t.rels,1,i,on.WB),lt(s,i="xl/theme/theme1.xml",oo(e.Themes,t)),n.themes.push(i),hn(t.wbrels,-1,"theme/theme1.xml",on.THEME),lt(s,i="xl/styles."+r,Ui(e,t)),n.styles.push(i),hn(t.wbrels,-1,"styles."+r,on.STY),e.vbaraw&&a&&(lt(s,i="xl/vbaProject.bin",e.vbaraw),n.vba.push(i),hn(t.wbrels,-1,"vbaProject.bin",on.VBA)),lt(s,i="xl/metadata."+r,ho()),n.metadata.push(i),hn(t.wbrels,-1,"metadata."+r,on.XLMETA),f.length>1&&(lt(s,i="xl/persons/person.xml",function(e){var t=[dt,$t("personList",null,{xmlns:rr,"xmlns:x":fr[0]}).replace(/[\/]>/,">")];return e.forEach((function(e,r){t.push($t("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+r).slice(-12)+"}",userId:e,providerId:"None"}))})),t.push("</personList>"),t.join("")}(f)),n.people.push(i),hn(t.wbrels,-1,"persons/person.xml",on.PEOPLE)),lt(s,"[Content_Types].xml",sn(n,t)),lt(s,"_rels/.rels",fn(t.rels)),lt(s,"xl/_rels/workbook.xml.rels",fn(t.wbrels)),delete t.revssf,delete t.ssf,s}function mh(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=T(e.slice(0,12));break;case"binary":r=e;break;default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function vh(e,t){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return Bl(e.slice(r),t);default:break e}return Ys.to_workbook(e,t)}function gh(e,t,r,a){return a?(r.type="string",Ys.to_workbook(e,r)):Ys.to_workbook(t,r)}function bh(e,t){f();var r=t||{};if("undefined"!==typeof ArrayBuffer&&e instanceof ArrayBuffer)return bh(new Uint8Array(e),((r=Ke(r)).type="array",r));"undefined"!==typeof Uint8Array&&e instanceof Uint8Array&&!r.type&&(r.type="undefined"!==typeof Deno?"buffer":"array");var a,n=e,s=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),pc={},r.dateNF&&(pc.dateNF=r.dateNF),r.type||(r.type=E&&Buffer.isBuffer(e)?"buffer":"base64"),"file"==r.type&&(r.type=E?"buffer":"binary",n=function(e){if("undefined"!==typeof Oe)return Oe.readFileSync(e);if("undefined"!==typeof Deno)return Deno.readFileSync(e);if("undefined"!==typeof $&&"undefined"!==typeof File&&"undefined"!==typeof Folder)try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}(e),"undefined"===typeof Uint8Array||E||(r.type="array")),"string"==r.type&&(s=!0,r.type="binary",r.codepage=65001,n=function(e){return e.match(/[^\x00-\x7F]/)?Mt(e):e}(e)),"array"==r.type&&"undefined"!==typeof Uint8Array&&e instanceof Uint8Array&&"undefined"!==typeof ArrayBuffer){var i=new ArrayBuffer(3),o=new Uint8Array(i);if(o.foo="bar",!o.foo)return(r=Ke(r)).type="array",bh(C(n),r)}switch((a=mh(n,r))[0]){case 208:if(207===a[1]&&17===a[2]&&224===a[3]&&161===a[4]&&177===a[5]&&26===a[6]&&225===a[7])return function(e,t){return Ce.find(e,"EncryptedPackage")?uh(e,t):tf(e,t)}(Ce.read(n,r),r);break;case 9:if(a[1]<=8)return tf(n,r);break;case 60:return Bl(n,r);case 73:if(73===a[1]&&42===a[2]&&0===a[3])throw new Error("TIFF Image File is not a spreadsheet");if(68===a[1])return function(e,t){var r=t||{},a=!!r.WTF;r.WTF=!0;try{var n=js.to_workbook(e,r);return r.WTF=a,n}catch(s){if(r.WTF=a,!s.message.match(/SYLK bad record ID/)&&a)throw s;return Ys.to_workbook(e,t)}}(n,r);break;case 84:if(65===a[1]&&66===a[2]&&76===a[3])return Xs.to_workbook(n,r);break;case 80:return 75===a[1]&&a[2]<9&&a[3]<9?function(e,t){var r=e,a=t||{};return a.type||(a.type=E&&Buffer.isBuffer(e)?"buffer":"base64"),hh(ht(r,a),a)}(n,r):gh(e,n,r,s);case 239:return 60===a[3]?Bl(n,r):gh(e,n,r,s);case 255:if(254===a[1])return function(e,t){var r=e;return"base64"==t.type&&(r=T(r)),r=d.utils.decode(1200,r.slice(2),"str"),t.type="binary",vh(r,t)}(n,r);if(0===a[1]&&2===a[2]&&0===a[3])return Ks.to_workbook(n,r);break;case 0:if(0===a[1]){if(a[2]>=2&&0===a[3])return Ks.to_workbook(n,r);if(0===a[2]&&(8===a[3]||9===a[3]))return Ks.to_workbook(n,r)}break;case 3:case 131:case 139:case 140:return Gs.to_workbook(n,r);case 123:if(92===a[1]&&114===a[2]&&116===a[3])return yi.to_workbook(n,r);break;case 10:case 13:case 32:return function(e,t){var r="",a=mh(e,t);switch(t.type){case"base64":r=T(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=Ye(e);break;default:throw new Error("Unrecognized type "+t.type)}return 239==a[0]&&187==a[1]&&191==a[2]&&(r=Lt(r)),t.type="binary",vh(r,t)}(n,r);case 137:if(80===a[1]&&78===a[2]&&71===a[3])throw new Error("PNG Image File is not a spreadsheet")}return Vs.indexOf(a[0])>-1&&a[2]<=12&&a[3]<=31?Gs.to_workbook(n,r):gh(e,n,r,s)}function wh(e,t){var r=t||{};return r.type="file",bh(e,r)}function Th(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Ne(t.file,Ce.write(e,{type:E?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return Ce.write(e,t)}function Eh(e,t){var r={},a=E?"nodebuffer":"undefined"!==typeof Uint8Array?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=a;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=a;break;default:throw new Error("Unrecognized type "+t.type)}var n=e.FullPaths?Ce.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!==typeof Deno&&"string"==typeof n){if("binary"==t.type||"base64"==t.type)return n;n=new Uint8Array(A(n))}return t.password&&"undefined"!==typeof encrypt_agile?Th(encrypt_agile(n,t.password),t):"file"===t.type?Ne(t.file,n):"string"==t.type?Lt(n):n}function yh(e,t,r){r||(r="");var a=r+e;switch(t.type){case"base64":return w(Mt(a));case"binary":return Mt(a);case"string":return e;case"file":return Ne(t.file,a,"utf8");case"buffer":return E?y(a,"utf8"):"undefined"!==typeof TextEncoder?(new TextEncoder).encode(a):yh(a,{type:"binary"}).split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}function Sh(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",a=0;a<e.length;++a)r+=String.fromCharCode(e[a]);return"base64"==t.type?w(r):"string"==t.type?Lt(r):r;case"file":return Ne(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function kh(e,t){f(),hl(e);var r=Ke(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),"array"==r.type){r.type="binary";var a=kh(e,r);return r.type="array",A(a)}return function(e,t){var r=Ke(t||{});return Eh(ph(e,r),r)}(e,r)}function _h(e,t){f(),hl(e);var r=Ke(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),"array"==r.type){r.type="binary";var a=_h(e,r);return r.type="array",A(a)}var n=0;if(r.sheet&&(n="number"==typeof r.sheet?r.sheet:e.SheetNames.indexOf(r.sheet),!e.SheetNames[n]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return yh(jl(e,r),r);case"slk":case"sylk":return yh(js.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"htm":case"html":return yh(Ef(e.Sheets[e.SheetNames[n]],r),r);case"txt":return function(e,t){switch(t.type){case"base64":return w(e);case"binary":case"string":return e;case"file":return Ne(t.file,e,"binary");case"buffer":return E?y(e,"binary"):e.split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}(Ph(e.Sheets[e.SheetNames[n]],r),r);case"csv":return yh(Dh(e.Sheets[e.SheetNames[n]],r),r,"\ufeff");case"dif":return yh(Xs.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"dbf":return Sh(Gs.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"prn":return yh(Ys.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"rtf":return yh(yi.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"eth":return yh($s.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"fods":return yh(Nf(e,r),r);case"wk1":return Sh(Ks.sheet_to_wk1(e.Sheets[e.SheetNames[n]],r),r);case"wk3":return Sh(Ks.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),Sh(mf(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),function(e,t){var r=t||{};return Th(rf(e,r),r)}(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return function(e,t){var r=Ke(t||{});return Eh(dh(e,r),r)}(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function Ah(e){if(!e.bookType){var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();t.match(/^\.[a-z]+$/)&&(e.bookType=t.slice(1)),e.bookType={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"}[e.bookType]||e.bookType}}function xh(e,t,r){var a=r||{};return a.type="file",a.file=t,Ah(a),_h(e,a)}function Ch(e,t,r){var a=r||{};return a.type="file",a.file=t,Ah(a),kh(e,a)}function Oh(e,t,r,a){var n=r||{};n.type="file",n.file=e,Ah(n),n.type="buffer";var s=a;return s instanceof Function||(s=r),Oe.writeFile(e,_h(t,n),s)}function Rh(e,t,r,a,n,s,i,o){var c=oa(r),l=o.defval,f=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),h=!0,u=1===n?[]:{};if(1!==n)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(v){u.__rowNum__=r}else u.__rowNum__=r;if(!i||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=i?e[r][d]:e[a[d]+c];if(void 0!==p&&void 0!==p.t){var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==l)u[s[d]]=l;else{if(!f||null!==m)continue;u[s[d]]=null}else u[s[d]]=f&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:va(p,m,o);null!=m&&(h=!1)}}else{if(void 0===l)continue;null!=s[d]&&(u[s[d]]=l)}}return{row:u,isempty:h}}function Ih(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},a=0,n=1,s=[],i=0,o="",c={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},f=null!=l.range?l.range:e["!ref"];switch(1===l.header?a=1:"A"===l.header?a=2:Array.isArray(l.header)?a=3:null==l.header&&(a=0),typeof f){case"string":c=pa(f);break;case"number":(c=pa(e["!ref"])).s.r=f;break;default:c=f}a>0&&(n=0);var h=oa(c.s.r),u=[],d=[],p=0,m=0,v=Array.isArray(e),g=c.s.r,b=0,w={};v&&!e[g]&&(e[g]=[]);var T=l.skipHidden&&e["!cols"]||[],E=l.skipHidden&&e["!rows"]||[];for(b=c.s.c;b<=c.e.c;++b)if(!(T[b]||{}).hidden)switch(u[b]=la(b),r=v?e[g][b]:e[u[b]+h],a){case 1:s[b]=b-c.s.c;break;case 2:s[b]=u[b];break;case 3:s[b]=l.header[b-c.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=i=va(r,null,l),m=w[i]||0){do{o=i+"_"+m++}while(w[o]);w[i]=m,w[o]=1}else w[i]=1;s[b]=o}for(g=c.s.r+n;g<=c.e.r;++g)if(!(E[g]||{}).hidden){var y=Rh(e,c,g,u,a,s,v,l);(!1===y.isempty||(1===a?!1!==l.blankrows:l.blankrows))&&(d[p++]=y.row)}return d.length=p,d}var Nh=/"/g;function Fh(e,t,r,a,n,s,i,o){for(var c=!0,l=[],f="",h=oa(r),u=t.s.c;u<=t.e.c;++u)if(a[u]){var d=o.dense?(e[r]||[])[u]:e[a[u]+h];if(null==d)f="";else if(null!=d.v){c=!1,f=""+(o.rawNumbers&&"n"==d.t?d.v:va(d,null,o));for(var p=0,m=0;p!==f.length;++p)if((m=f.charCodeAt(p))===n||m===s||34===m||o.forceQuotes){f='"'+f.replace(Nh,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==d.f||d.F?f="":(c=!1,(f="="+d.f).indexOf(",")>=0&&(f='"'+f.replace(Nh,'""')+'"'));l.push(f)}return!1===o.blankrows&&c?null:l.join(i)}function Dh(e,t){var r=[],a=null==t?{}:t;if(null==e||null==e["!ref"])return"";var n=pa(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),o=void 0!==a.RS?a.RS:"\n",c=o.charCodeAt(0),l=new RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];a.dense=Array.isArray(e);for(var u=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(u[p]||{}).hidden||(h[p]=la(p));for(var m=0,v=n.s.r;v<=n.e.r;++v)(d[v]||{}).hidden||null!=(f=Fh(e,n,v,h,i,c,s,a))&&(a.strip&&(f=f.replace(l,"")),(f||!1!==a.blankrows)&&r.push((m++?o:"")+f));return delete a.dense,r.join("")}function Ph(e,t){t||(t={}),t.FS="\t",t.RS="\n";var r=Dh(e,t);if("undefined"==typeof d||"string"==t.type)return r;var a=d.utils.encode(1200,r,"str");return String.fromCharCode(255)+String.fromCharCode(254)+a}function Lh(e,t,r){var a,n=r||{},s=+!n.skipHeader,i=e||{},o=0,c=0;if(i&&null!=n.origin)if("number"==typeof n.origin)o=n.origin;else{var l="string"==typeof n.origin?fa(n.origin):n.origin;o=l.r,c=l.c}var f={s:{c:0,r:0},e:{c:c,r:o+t.length-1+s}};if(i["!ref"]){var h=pa(i["!ref"]);f.e.c=Math.max(f.e.c,h.e.c),f.e.r=Math.max(f.e.r,h.e.r),-1==o&&(o=h.e.r+1,f.e.r=o+t.length-1+s)}else-1==o&&(o=0,f.e.r=t.length-1+s);var u=n.header||[],d=0;t.forEach((function(e,t){Fe(e).forEach((function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var l=e[r],f="z",h="",p=ha({c:c+d,r:o+t+s});a=Mh(i,p),!l||"object"!==typeof l||l instanceof Date?("number"==typeof l?f="n":"boolean"==typeof l?f="b":"string"==typeof l?f="s":l instanceof Date?(f="d",n.cellDates||(f="n",l=Ue(l)),h=n.dateNF||H[14]):null===l&&n.nullError&&(f="e",l=0),a?(a.t=f,a.v=l,delete a.w,delete a.R,h&&(a.z=h)):i[p]=a={t:f,v:l},h&&(a.z=h)):i[p]=l}))})),f.e.c=Math.max(f.e.c,c+u.length-1);var p=oa(o);if(s)for(d=0;d<u.length;++d)i[la(d+c)+p]={t:"s",v:u[d]};return i["!ref"]=da(f),i}function Mh(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var a=fa(t);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return Mh(e,ha("number"!=typeof t?t:{r:t,c:r||0}))}function Uh(){return{SheetNames:[],Sheets:{}}}function Bh(e,t,r,a){var n=1;if(!r)for(;n<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+n);++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||r;for(++n;n<=65535&&-1!=e.SheetNames.indexOf(r=i+n);++n);}if(fl(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function Wh(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var Hh,zh={encode_col:la,encode_row:oa,encode_cell:ha,encode_range:da,decode_col:ca,decode_row:ia,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:fa,decode_range:ua,format_cell:va,sheet_add_aoa:ba,sheet_add_json:Lh,sheet_add_dom:yf,aoa_to_sheet:wa,json_to_sheet:function(e,t){return Lh(null,e,t)},table_to_sheet:Sf,table_to_book:function(e,t){return ga(Sf(e,t),t)},sheet_to_csv:Dh,sheet_to_txt:Ph,sheet_to_json:Ih,sheet_to_html:Ef,sheet_to_formulae:function(e){var t,r="",a="";if(null==e||null==e["!ref"])return[];var n,s=pa(e["!ref"]),i="",o=[],c=[],l=Array.isArray(e);for(n=s.s.c;n<=s.e.c;++n)o[n]=la(n);for(var f=s.s.r;f<=s.e.r;++f)for(i=oa(f),n=s.s.c;n<=s.e.c;++n)if(r=o[n]+i,a="",void 0!==(t=l?(e[f]||[])[n]:e[r])){if(null!=t.F){if(r=t.F,!t.f)continue;a=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)a=t.f;else{if("z"==t.t)continue;if("n"==t.t&&null!=t.v)a=""+t.v;else if("b"==t.t)a=t.v?"TRUE":"FALSE";else if(void 0!==t.w)a="'"+t.w;else{if(void 0===t.v)continue;a="s"==t.t?"'"+t.v:""+t.v}}c[c.length]=r+"="+a}return c},sheet_to_row_object_array:Ih,sheet_get_cell:Mh,book_new:Uh,book_append_sheet:Bh,book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}throw new Error("Cannot find sheet |"+t+"|")}(e,t);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:Wh,cell_set_internal_link:function(e,t,r){return Wh(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,a){for(var n="string"!=typeof t?t:pa(t),s="string"==typeof t?t:da(t),i=n.s.r;i<=n.e.r;++i)for(var o=n.s.c;o<=n.e.c;++o){var c=Mh(e,i,o);c.t="n",c.F=s,delete c.v,i==n.s.r&&o==n.s.c&&(c.f=r,a&&(c.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};var Vh={to_json:function(e,t){var r=Hh({objectMode:!0});if(null==e||null==e["!ref"])return r.push(null),r;var a={t:"n",v:0},n=0,s=1,i=[],o=0,c="",l={s:{r:0,c:0},e:{r:0,c:0}},f=t||{},h=null!=f.range?f.range:e["!ref"];switch(1===f.header?n=1:"A"===f.header?n=2:Array.isArray(f.header)&&(n=3),typeof h){case"string":l=pa(h);break;case"number":(l=pa(e["!ref"])).s.r=h;break;default:l=h}n>0&&(s=0);var u=oa(l.s.r),d=[],p=0,m=Array.isArray(e),v=l.s.r,g=0,b={};m&&!e[v]&&(e[v]=[]);var w=f.skipHidden&&e["!cols"]||[],T=f.skipHidden&&e["!rows"]||[];for(g=l.s.c;g<=l.e.c;++g)if(!(w[g]||{}).hidden)switch(d[g]=la(g),a=m?e[v][g]:e[d[g]+u],n){case 1:i[g]=g-l.s.c;break;case 2:i[g]=d[g];break;case 3:i[g]=f.header[g-l.s.c];break;default:if(null==a&&(a={w:"__EMPTY",t:"s"}),c=o=va(a,null,f),p=b[o]||0){do{c=o+"_"+p++}while(b[c]);b[o]=p,b[c]=1}else b[o]=1;i[g]=c}return v=l.s.r+s,r._read=function(){for(;v<=l.e.r;)if(!(T[v-1]||{}).hidden){var t=Rh(e,l,v,d,n,i,m,f);if(++v,!1===t.isempty||(1===n?!1!==f.blankrows:f.blankrows))return void r.push(t.row)}return r.push(null)},r},to_html:function(e,t){var r=Hh(),a=t||{},n=null!=a.header?a.header:bf,s=null!=a.footer?a.footer:wf;r.push(n);var i=ua(e["!ref"]);a.dense=Array.isArray(e),r.push(Tf(0,0,a));var o=i.s.r,c=!1;return r._read=function(){if(o>i.e.r)return c||(c=!0,r.push("</table>"+s)),r.push(null);for(;o<=i.e.r;){r.push(gf(e,i,o,a)),++o;break}},r},to_csv:function(e,t){var r=Hh(),a=null==t?{}:t;if(null==e||null==e["!ref"])return r.push(null),r;var n=pa(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),o=void 0!==a.RS?a.RS:"\n",c=o.charCodeAt(0),l=new RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];a.dense=Array.isArray(e);for(var u=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(u[p]||{}).hidden||(h[p]=la(p));var m=n.s.r,v=!1,g=0;return r._read=function(){if(!v)return v=!0,r.push("\ufeff");for(;m<=n.e.r;)if(++m,!(d[m-1]||{}).hidden&&null!=(f=Fh(e,n,m-1,h,i,c,s,a))&&(a.strip&&(f=f.replace(l,"")),f||!1!==a.blankrows))return r.push((g++?o:"")+f);return r.push(null)},r},set_readable:function(e){Hh=e}};const Gh=a.version}}]);
//# sourceMappingURL=46.33816a6a.chunk.js.map