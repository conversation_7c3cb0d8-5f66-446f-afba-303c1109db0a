import { m } from 'framer-motion';
import { Link as RouterLink } from 'react-router-dom';
// @mui
import { styled } from '@mui/material/styles';
import {
  Box,
  Button,
  Typography,
  Container,
  Paper,
  Avatar,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
// icons
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
// components
import Page from '../components/Page';
import { MotionContainer, varBounce } from '../components/animate';

// ----------------------------------------------------------------------

const RootStyle = styled('div')(({ theme }) => ({
  display: 'flex',
  minHeight: '100%',
  alignItems: 'center',
  paddingTop: theme.spacing(10),
  paddingBottom: theme.spacing(5),
}));

const SmsContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  maxWidth: '100%',
  margin: 'auto',
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[2],
  padding: theme.spacing(2),
  minWidth: 300,
}));

const SmsMessage = styled(Paper, {
  shouldForwardProp: (prop) => prop !== 'isUser',
})(({ theme, isUser }) => ({
  padding: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(1.5),
  maxWidth: '80%',
  alignSelf: isUser ? 'flex-end' : 'flex-start',
  backgroundColor: isUser ? theme.palette.primary.main : theme.palette.grey[200],
  color: isUser ? theme.palette.primary.contrastText : theme.palette.text.primary,
}));

// ----------------------------------------------------------------------

export default function HelpPage() {
  // Example array of supporters (6 or more). Replace with real data.
  const supporters = [
    {
      id: 1,
      name: 'Baatarkhuu Ganbold',
      phone: '(+976) 99199642',
      fbLink: 'https://www.facebook.com/baatarkhuu.ganbold.94',
      avatar: '/images/avatars/sup1.jpg',
      locationLink: 'https://goo.gl/maps/hyvMxK7AbDwQkzWS8', // Example Google Maps link
      locationTitle: 'Ханын материал салбар',
    },
    {
      id: 2,
      name: 'Munkh Aldar Ankhvv',
      phone: '(+976) 99100525',
      fbLink: 'https://www.facebook.com/monkhaldar.ankhvv',
      avatar: '/images/avatars/sup2.jpg',
      locationLink: 'https://goo.gl/maps/dG1AXNwAjM7LTyAN8',
      locationTitle: 'Компюьтер ланд салбар',
    },
    {
      id: 3,
      name: 'Б.Энхтэнгэр',
      phone: '(+976) 88136004',
      fbLink: 'https://www.facebook.com/profile.php?id=100045189365124',
      avatar: '/images/avatars/sup3.jpg',
      locationLink: 'https://goo.gl/maps/vmoQPRTQ5fw8ipma7',
      locationTitle: 'Компютер ланд',
    },
    {
      id: 4,
      name: 'Dagwadorj Batsuuri',
      phone: '(+976) 99355473',
      fbLink: 'https://www.facebook.com/profile.php?id=100004848316952',
      avatar: '/images/avatars/sup4.jpg',
      locationLink: 'https://goo.gl/maps/ZfBpguTNqqM2Yevy7',
      locationTitle: 'Эрдэнэт салбар',
    },
    {
      id: 5,
      name: 'Ө.Орших',
      phone: '(+976) 88156627',
      fbLink: 'https://www.facebook.com/B.T.Zukaa',
      avatar: '/images/avatars/sup5.jpg',
      locationLink: 'https://goo.gl/maps/Hi1DAWFbncZzCV6K8',
      locationTitle: 'Дархан салбар',
    },
    {
      id: 6,
      name: 'Ганзориг Нармандах',
      phone: '(+976) 95446633',
      fbLink: 'https://www.facebook.com/ganzorig.narmandax',
      avatar: '/images/avatars/sup6.jpg',
      locationLink: 'https://goo.gl/maps/4yJ4ahDkfyy5GKwt7',
      locationTitle: 'Дорнод салбар',
    },
    {
      id: 7,
      name: 'М.Лхагваа',
      phone: '(+976) 80895905',
      fbLink: 'https://www.facebook.com/munkhjargl.lkhagwmndal',
      avatar: '/images/avatars/sup7.jpg',
      locationLink: 'https://goo.gl/maps/4yJ4ahDkfyy5GKwt7',
      locationTitle: 'Дундговь салбар',
    },
  ];

  // Example array of commands
  const commands = [
    { command: 'untar', description: 'машин унтраах үйлдэл' },
    { command: 'as', description: 'машин асаах үйлдэл' },
    { command: 'check', description: 'Системийн одоогийн төлөв байдалийг илтгэх JSON өгөгдлийг авах комманд.' },
    { command: 'lock', description: "цоожлох үйлдэл, 'locked' гэсэн хариу өгнө." },
    { command: 'unlock', description: "цоож нээх үйлдэл, 'unlocked' гэсэн хариу өгнө." },
    { command: 'mirror', description: "толь эвхэх үйлдэл, 'mirror received' гэсэн хариу өгнө." },
    { command: 'update', description: 'OTA шинэчлэлтийн үйл явцыг эхлүүлж, хамгийн сүүлийн фирмварийг төхөөрөмжинд суулгана' },
    { command: 'version', description: 'Программын одоогийн хувилбарын мэдээллийг өгнө.' },
    { command: 'sim', description: 'Unitel SIM картын апп дээрхи мэдээллийг шинэчлэнэ' },
    { command: 'id', description: 'Төхөөрөмжийн ID-г өгнө.' },
    { command: 'sound off', description: 'төхөөрөмжийн дууг хаах' },
    { command: 'sound on', description: 'төхөөрөмжийн дууг нээх' },
    { command: 'display on', description: 'дэлгэцтэй холбох' },
    { command: 'display off', description: 'дэлгэцээс салгах' },
    { command: 'key on', description: 'запас түлхүүрийг байнгын идэвхтэй болгох' },
    { command: 'key off', description: 'запас түлхүүр идэвхгүй болгох' },
    { command: 'asa[секунд]', description: 'зasaCommand функцийг заасан хугацаанд ажиллуулна. Жишээ asa5 гэвэл 5 секундын турш машины кундакторыг түлхэнэ' },
    { command: 'tempXXxYY', description: 'AS болон UNTAR горимуудын зорилтот температурыг тохируулна. Жишээ temp20x25 машин 20 градуст асаад 25 градуст унтрана' },
    { command: 'asTTxx', description: 'asTT10 10 минут дараа асна' },
    { command: 'set server1', description: 'төхөөрөмжийг 1 дүгээр серверт холбох. Бусад сервер засвартай болон сервис хийгдэх үед шилжүүлж ашиглана' },
    { command: 'set server2', description: 'төхөөрөмжийг 2 дүгээр серверт холбох. Бусад сервер засвартай болон сервис хийгдэх үед шилжүүлж ашиглана' },
    { command: 'set server3', description: 'төхөөрөмжийг 3 дүгээр серверт холбох. Бусад сервер засвартай болон сервис хийгдэх үед шилжүүлж ашиглана' },






    
    
    
    // ... add more commands as needed ...
  ];

  // Example array of multiple YouTube videos (5-6). Replace with real IDs/titles.
  const tutorialVideos = [
    { id: 1, title: 'Лиценз сунгах', embedId: 'im5wSOJ8Qv8' },
    { id: 2, title: 'Сим карт мэдээлэл шинэчлэх www.aslaa.mn ээр орно', embedId: 'QO3PgxCIpQA' },
    { id: 3, title: 'Хүйтэн үед буюу акк хүчдэл удаан өсөх үед аппаар машинаа асаах', embedId: 'yiOmB9mAhHY' },
    { id: 4, title: 'Нууц үг сэргээх', embedId: '0mcA2qqlSSQ' },
    
  ];

  // Common Q&A (FAQ) Section
  const faqs = [
    {
      question: 'Машин өглөө олон комманд явуулж асаад байна заримдаа асахгүй байна яах вэ?',
      answer: ' асаалт суулгах үед хувилсан түлхүүрийн чип буруу байршилд бэхлэгдсэн бол түлхүүрээ танихгүй асахгүй эсвэл олон даруулж асах. Үүнийг баталгаажуулхын тулд машиндаа ориг түлхүүртэйгээ ороод аппаас 1 л оролдлоор асуудалгүй асаж байвал тухайн чипээ зөв байрлалд байрлуулснаар асуудал шийдэгднэ.  машины баттерей асаах комманд илгээснээс 10 секундын дараа 13.5в оос дээш болж ассан төлөвөө илгээдэг. Өвөл хүйтэнд машин 15-20сек дараа ассан төлөвөө илгээж байгаа тул апп дээрээ дахин асаах комманд илгээхгүйгээр map button дарж төлөвөө дахин авна. Олон команд илгээх нь ассан машиныг унтраах, унтарсан машиныг асаах үйлдэл хийнэ ',
    },
    {
      question: 'Миний машинд запас түлхүүр байхгүй. Танай үнэгүй түлхүүрийг ашиглаж болох уу?',
      answer: 'Болно. Та өөрийн машины оригинал түлхүүрийг идэвхжүүлэгч болгон ашиглаж, бидний үнэгүй дагалдуулдаг түлхүүрийг хэрэглэж болно. Гэхдээ энэ тохиолдолд машины хаалганд ойртоход автоматаар нээгдэх зэрэг ухаалаг функцууд ажиллахгүй.',
    },
    {
      question: 'Запас түлхүүр яагаад хэрэгтэй вэ? ',
      answer: 'Баттерейгүй запас түлхүүрийг төхөөрөмжид холбож, машин асаах үед хэдхэн секунд идэвхждэг идэвхжүүлэгчээр ашигладаг. Зөв байрлуулснаар машин асах бөгөөд хулгайн эрсдэлгүй байдлыг хангана. Зарим ховор тохиолдолд байршил тохирохгүй бол асаах команд ирсэн ч машин асахгүй байж болох тул суулгуулсан газартаа очиж тохируулснаар хэвийн ажиллах болно. Тиймээс запас түлхүүрийг зөв газарт байрлуулах нь чухал.',
    },
    // Add more Q&A as needed...
  ];

  return (
    <Page title="Мессеж коммандуудын жагсаалт" sx={{ height: 1 }}>
      <RootStyle>
        <Container component={MotionContainer}>
          <Box sx={{ maxWidth: 480, margin: 'auto', textAlign: 'center' }}>
             {/* ---------- App UI Explanation Image ---------- */}
             <m.div variants={varBounce().in}>
              <Typography variant="h5" paragraph sx={{ mb: 3 }}>
                Апп интерфейс танилцуулга
              </Typography>
              <img
                src="/images/appui.jpg"
                alt="App UI Explanation"
                style={{ width: '100%', borderRadius: '8px', marginBottom: '20px' }}
              />
            </m.div>
            {/* ---------- End App UI Explanation Image ---------- */}
            {/* ---------- Supporters Section (Responsive Grid) ---------- */}
            <m.div variants={varBounce().in}>
              <Typography variant="h5" paragraph sx={{ mb: 3 }}>
                Асаалт суулгалт болон суулгалтын дараах үйлчилгээ
              </Typography>
              <Grid container spacing={2} justifyContent="center">
                {supporters.map((supporter) => (
                  <Grid key={supporter.id} item xs={12} sm={6} md={4}>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        p: 2,
                        border: 1,
                        borderColor: 'grey.300',
                        borderRadius: 2,
                      }}
                    >
                      <Avatar
                        alt={supporter.name}
                        src={supporter.avatar}
                        sx={{ width: 60, height: 60, mb: 1 }}
                      />
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                        {supporter.name}
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        Утас:{' '}
                        <a href={`tel:${supporter.phone.replace(/\D/g, '')}`}>
                          {supporter.phone}
                        </a>
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                        Facebook:{' '}
                        <a href={supporter.fbLink} target="_blank" rel="noopener noreferrer">
                          {supporter.fbLink.replace(/^https?:\/\//, '')}
                        </a>
                      </Typography>
                      {/* -------------- Location Link -------------- */}
                      {supporter.locationLink && (
                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                          Хаяг:{' '}
                          <a
                            href={supporter.locationLink}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {supporter.locationTitle || 'Map Link'}
                          </a>
                        </Typography>
                      )}
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </m.div>
            {/* ---------- End Supporters Section ---------- */}

            {/* ---------- Commands List ---------- */}
            <m.div variants={varBounce().in} sx={{ mt: 6 }}>
              <Typography variant="h4" paragraph>
                Мессеж командуудын жагсаалт
              </Typography>
            </m.div>
            <Typography sx={{ color: 'text.secondary' }}>
              Асаалтын Системийн SMS командуудыг доор жагсаав
            </Typography>

            <m.div variants={varBounce().in}>
              <SmsContainer sx={{ mt: 4 }}>
                {commands.map((cmd, index) => (
                  <div key={`cmd-${index}-${cmd.command}`}>
                    <SmsMessage isUser>
                      <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                        {cmd.command}
                      </Typography>
                    </SmsMessage>
                    <SmsMessage>
                      <Typography variant="body2">{cmd.description}</Typography>
                    </SmsMessage>
                  </div>
                ))}
              </SmsContainer>
            </m.div>
            {/* ---------- End Commands List ---------- */}

            {/* ---------- Multiple YouTube Videos ---------- */}
            <m.div variants={varBounce().in}>
              <Typography variant="h6" gutterBottom sx={{ mt: 6 }}>
                Зааварчилгаа бичлэг
              </Typography>
              <Grid container spacing={3} sx={{ mt: 2 }}>
                {tutorialVideos.map((video) => (
                  <Grid key={video.id} item xs={12}>
                    <Typography variant="subtitle1" sx={{ mb: 1 }}>
                      {video.title}
                    </Typography>
                    <Box
                      sx={{
                        position: 'relative',
                        paddingBottom: '56.25%', // 16:9 aspect ratio
                        height: 0,
                        overflow: 'hidden',
                        borderRadius: 2,
                        boxShadow: 1,
                      }}
                    >
                      <iframe
                        title={video.title}
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                        }}
                        src={`https://www.youtube.com/embed/${video.embedId}`}
                        frameBorder="0"
                        allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      />
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </m.div>
            {/* ---------- End Multiple YouTube Videos ---------- */}

            {/* ---------- Common Q&A Section ---------- */}
            <m.div variants={varBounce().in} sx={{ mt: 6 }}>
              <Typography variant="h6" paragraph>
                Түгээмэл асуулт &amp; Хариулт
              </Typography>
              {faqs.map((faq, index) => (
                <Accordion key={index} sx={{ textAlign: 'left' }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      {faq.question}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2">{faq.answer}</Typography>
                  </AccordionDetails>
                </Accordion>
              ))}
            </m.div>
            {/* ---------- End Common Q&A Section ---------- */}

            <Button
              to="/home"
              size="large"
              variant="contained"
              component={RouterLink}
              sx={{ mt: 5 }}
            >
              Go to Home
            </Button>
          </Box>
        </Container>
      </RootStyle>
    </Page>
  );
}