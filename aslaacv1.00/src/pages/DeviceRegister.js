
import { useSnackbar } from 'notistack';
import { useState, useEffect, useCallback, useMemo, lazy, Suspense } from 'react';
import { useTranslation } from 'react-i18next';

// @mui
import {
  Stack,
  Container,
  Typography,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  Card,
  CardContent,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Switch,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  QrCodeScanner as QrIcon,
  DevicesOther as DeviceIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';

// hooks
import useAuth from '../hooks/useAuth';
// components
import Page from '../components/Page';
import axios from '../utils/axios';
import Layout from '../layout';

// Lazy load QR scanner to reduce initial bundle size
const BarcodeScannerComponent = lazy(() => import("react-qr-barcode-scanner"));

// Constants for better performance
const INITIAL_FORM_DATA = {
  deviceName: '',
  deviceNumber: '',
  type: '4g',
  uix: 'CarV1.2',
  isDefault: false
};

const DEVICE_TYPES = [
  { value: '4g', label: '4G' }
];

const UIX_TYPES = [
  { value: 'CarV1.1', label: 'CarV 1.1' },
  { value: 'CarV1.2', label: 'CarV 1.2' },
  { value: 'Car2.1', label: 'Asa2.1' },
  { value: 'Car2.2', label: 'Asa2.2' },
  { value: 'Chip', label: 'Chip' },
  { value: 'GPS', label: 'GPS' }
];

// Memoized DeviceCard component for better performance
const DeviceCard = ({ device, onEdit, onDelete, t }) => (
  <Card sx={{ '&:hover': { boxShadow: 3 } }}>
    <CardContent>
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Stack direction="row" alignItems="center" spacing={2}>
          <DeviceIcon color="primary" sx={{ fontSize: 32 }} />
          <Box>
            <Typography variant="h6">
              {device.deviceName || 'Unnamed Device'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {device.deviceNumber}
            </Typography>
            <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
              <Chip
                label={device.type.toUpperCase()}
                size="small"
                color="primary"
                variant="outlined"
              />
              <Chip
                label={device.uix}
                size="small"
                color="secondary"
                variant="outlined"
              />
              {device.isDefault && (
                <Chip
                  label="Default"
                  size="small"
                  color="success"
                  icon={<CheckIcon />}
                />
              )}
            </Stack>
          </Box>
        </Stack>
        <Stack direction="row" spacing={1}>
          <IconButton
            onClick={() => onEdit(device)}
            color="primary"
            title="Edit device"
          >
            <EditIcon />
          </IconButton>
          <IconButton
            onClick={() => onDelete(device)}
            color="error"
            disabled={device.isDefault}
            title={device.isDefault ? "Cannot delete default device" : "Delete device"}
          >
            <DeleteIcon />
          </IconButton>
        </Stack>
      </Stack>
    </CardContent>
  </Card>
);

// Memoized DeviceList component for better performance
const DeviceList = ({ devices, onEdit, onDelete, t }) => {
  if (devices.length === 0) {
    return (
      <Card>
        <CardContent sx={{ textAlign: 'center', py: 6 }}>
          <DeviceIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No devices registered
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Click the "Register Device" button to add your first device
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Stack spacing={2}>
      {devices.map((device, index) => (
        <DeviceCard
          key={device._id || device.deviceNumber || index}
          device={device}
          onEdit={onEdit}
          onDelete={onDelete}
          t={t}
        />
      ))}
    </Stack>
  );
};

export default function DeviceRegister() {
  const { initialize, user } = useAuth();
  const { t } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();

  const [devices, setDevices] = useState(user?.devices || []);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingDevice, setEditingDevice] = useState(null);
  const [showQrScanner, setShowQrScanner] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState(INITIAL_FORM_DATA);

  // Memoized handlers for better performance
  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
    setEditingDevice(null);
    setShowQrScanner(false);
    setFormData(INITIAL_FORM_DATA);
  }, []);

  const handleQrResult = useCallback((err, result) => {
    if (result) {
      setFormData(prev => ({ ...prev, deviceNumber: result.text }));
      setShowQrScanner(false);
    }
  }, []);

  const handleSubmit = useCallback(async () => {
    if (!formData.deviceName || !formData.deviceNumber) {
      enqueueSnackbar('Please fill in all required fields', { variant: 'error' });
      return;
    }

    setIsSubmitting(true);
    const deviceData = {
      ...formData,
      _id: editingDevice?._id || ''
    };

    try {
      const res = await axios.post('/api/device/register', deviceData);
      if (res.data.success) {
        enqueueSnackbar(
          editingDevice ? 'Device updated successfully' : 'Device registered successfully',
          { variant: 'success' }
        );
        handleCloseDialog();
        initialize();
      } else {
        enqueueSnackbar(res.data.message || 'Operation failed', { variant: 'error' });
      }
    } catch (err) {
      enqueueSnackbar('An error occurred', { variant: 'error' });
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, editingDevice, enqueueSnackbar, initialize, handleCloseDialog]);
  const handleDelete = useCallback(async (device) => {
    if (device.isDefault) {
      enqueueSnackbar('Cannot delete default device', { variant: 'error' });
      return;
    }

    try {
      const res = await axios.post('/api/device/delete', { deviceNumber: device.deviceNumber });
      if (res.data.success) {
        enqueueSnackbar('Device deleted successfully', { variant: 'success' });
        initialize();
      } else {
        enqueueSnackbar(res.data.message || 'Failed to delete device', { variant: 'error' });
      }
    } catch (err) {
      enqueueSnackbar('Failed to delete device', { variant: 'error' });
    }
  }, [enqueueSnackbar, initialize]);

  const handleOpenDialog = useCallback((device = null) => {
    setEditingDevice(device);
    setFormData(device ? {
      deviceName: device.deviceName || '',
      deviceNumber: device.deviceNumber || '',
      type: device.type || '4g',
      uix: device.uix || 'CarV1.2',
      isDefault: device.isDefault || false
    } : INITIAL_FORM_DATA);
    setOpenDialog(true);
  }, []);

  const handleFormChange = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  // Memoized values for better performance
  const isFormValid = useMemo(() =>
    formData.deviceName.trim() && formData.deviceNumber.trim(),
    [formData.deviceName, formData.deviceNumber]
  );

  const deviceTypeOptions = useMemo(() =>
    DEVICE_TYPES.map(type => (
      <MenuItem key={type.value} value={type.value}>
        {type.label}
      </MenuItem>
    )), []
  );

  const uixTypeOptions = useMemo(() =>
    UIX_TYPES.map(uix => (
      <MenuItem key={uix.value} value={uix.value}>
        {uix.label}
      </MenuItem>
    )), []
  );

  useEffect(() => {
    setDevices(user?.devices || []);
  }, [user?.devices]);
  return (
    <Page title="Device Registration">
      <Layout />
      <Container maxWidth="md" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, textAlign: 'center' }}>
          <Typography variant="h4" gutterBottom>
            {t("device_profile.device_information")}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your registered devices
          </Typography>
        </Box>

        {/* Add Device Button */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
            size="large"
          >
            {t("words.register")} Device
          </Button>
        </Box>

        {/* Devices List */}
        <DeviceList
          devices={devices}
          onEdit={handleOpenDialog}
          onDelete={handleDelete}
          t={t}
        />
        {/* Device Registration/Edit Dialog */}
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            {editingDevice ? 'Edit Device' : 'Register New Device'}
          </DialogTitle>
          <DialogContent>
            <Stack spacing={3} sx={{ mt: 1 }}>
              <TextField
                label={t("words.device_name")}
                value={formData.deviceName}
                onChange={(e) => handleFormChange('deviceName', e.target.value)}
                fullWidth
                required
                error={!formData.deviceName.trim() && isSubmitting}
                helperText={!formData.deviceName.trim() && isSubmitting ? 'This field is required' : ''}
              />

              <Stack direction="row" spacing={1}>
                <TextField
                  label={t("words.device_number")}
                  value={formData.deviceNumber}
                  onChange={(e) => handleFormChange('deviceNumber', e.target.value)}
                  fullWidth
                  required
                  error={!formData.deviceNumber.trim() && isSubmitting}
                  helperText={!formData.deviceNumber.trim() && isSubmitting ? 'This field is required' : ''}
                />
                <IconButton
                  onClick={() => setShowQrScanner(!showQrScanner)}
                  color="primary"
                  size="large"
                  title="Scan QR Code"
                >
                  <QrIcon />
                </IconButton>
              </Stack>

              {showQrScanner && (
                <Suspense fallback={
                  <Box sx={{
                    width: '100%',
                    height: '300px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    border: '2px dashed',
                    borderColor: 'grey.300',
                    borderRadius: 2
                  }}>
                    <CircularProgress />
                  </Box>
                }>
                  <Box sx={{
                    width: '100%',
                    height: '300px',
                    border: '2px solid',
                    borderColor: 'primary.main',
                    borderRadius: 2,
                    overflow: 'hidden',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}>
                    <BarcodeScannerComponent
                      width="100%"
                      height="100%"
                      onUpdate={handleQrResult}
                    />
                  </Box>
                </Suspense>
              )}

              <FormControl fullWidth>
                <InputLabel>{t("words.device_type")}</InputLabel>
                <Select
                  value={formData.type}
                  label={t("words.device_type")}
                  onChange={(e) => handleFormChange('type', e.target.value)}
                >
                  {deviceTypeOptions}
                </Select>
              </FormControl>

              <FormControl fullWidth>
                <InputLabel>{t("words.device_uix")}</InputLabel>
                <Select
                  value={formData.uix}
                  label={t("words.device_uix")}
                  onChange={(e) => handleFormChange('uix', e.target.value)}
                >
                  {uixTypeOptions}
                </Select>
              </FormControl>

              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isDefault}
                    onChange={(e) => handleFormChange('isDefault', e.target.checked)}
                    color="primary"
                  />
                }
                label={t("words.default")}
              />
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              disabled={!isFormValid || isSubmitting}
              startIcon={isSubmitting ? <CircularProgress size={16} /> : null}
            >
              {isSubmitting
                ? 'Saving...'
                : editingDevice
                  ? 'Update'
                  : 'Register'
              }
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Page>
  );
}
