import { Accordion, AccordionDetails, AccordionSummary, Box,  Container, Stack, TextField, Typography } from "@mui/material";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

import { useTranslation } from 'react-i18next';
import {  useState } from "react";
import { LoadingButton } from "@mui/lab";
import { useSnackbar } from "notistack";

import axios from '../utils/axios';

import useAuth from "../hooks/useAuth";
import Page from "../components/Page";
import Layout from "../layout";
import { HOST_API } from "../config";
import Label from "../components/Label";


const verificationStatus = ['no-license', 'pending', 'verified'];

export default function DriverProfile() {
    const { user, initialize } = useAuth();
    const verified = user?.driverLicenseVerification || 0;
    const { t } = useTranslation();
    const { enqueueSnackbar } = useSnackbar();



    const [profile, setProfile] = useState({
        username: user?.username,
        address: user?.address,
        description: user?.description,

        // driverLicenseFile: user?.driverLicenseFile || '',
    })
    const [image, setImage] = useState(user?.driverLicenseFile || '');



    const imageChange = (event) => {
        if (event.target.files && event.target.files.length > 0) {
            setImage(event.target.files[0]);
        }
    }
    const handleSubmit = () => {
        if (typeof image === 'object') {
            const formData = new FormData();
            formData.append("username", profile.username);
            formData.append("address", profile.address);
            formData.append("description", profile.description);
            formData.append("driverLicenseFile", image);
            axios.post('/api/device/set-driver-profile', formData).then(res => {
                if (res.status === 200 && res.data?.success){
                    enqueueSnackbar('Submitted successful', { variant: 'success' })
                    initialize();
                }
                    
                else{
                    enqueueSnackbar('Whoops! please try again', { variant: 'error' })
                    console.log(res.data.err);
                }
                    
            })
        }
        else {
            enqueueSnackbar('Select Driver License File with image type', { variant: 'error' })
        }

    }



    return (
        <Page title="Driver Profile">
            <Layout />
            <Container sx={{ py: { xs: 12 } }} maxWidth={'sm'}>
                <form>
                    <Stack justifyContent={'center'} alignItems={'center'} sx={{ width: '100%' }}>
                        <Accordion sx={{ width: '100%' }}  >
                            <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}

                            >
                                <Stack direction="row" justifyContent={'space-between'} sx={{ width: '100%' }} alignItems={'center'}>
                                    <Typography variant={'h5'}>Driver License</Typography>
                                    <Label
                                        color={verified === 2 ? 'success' : (verified === 1 ? 'warning' : 'error')} >
                                        {verificationStatus[user?.driverLicenseVerification]}
                                    </Label>

                                </Stack>
                            </AccordionSummary>
                            <AccordionDetails>
                                <Stack direction="column" sx={{ width: '100%' }} gap={2} paddingY={2}>
                                    <TextField label={t('driver.name')} onChange={(e) => { setProfile({ ...profile, username: e.target.value }) }} value={profile.username} />
                                    <TextField label={t('driver.address')} onChange={(e) => { setProfile({ ...profile, address: e.target.value }) }} value={profile.address} />
                                    <Box sx={{ width: '100%', }}>
                                        <input accept="image/*" type='file' hidden id='image' onChange={imageChange} />
                                        <Typography sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }} component="label" htmlFor='image' >
{image !== '' ?
    <img src={typeof image === 'object' ? URL.createObjectURL(image) : `${HOST_API}${image}`} alt="Driver license" style={{ width: '100%' }} />
    :
    <img src={`/images/driver-license.png`} alt="Default driver license" style={{ width: '100%', }} />
}
                                        </Typography>
                                    </Box>
                                    <TextField label={t('driver.description')} onChange={(e) => { setProfile({ ...profile, description: e.target.value }) }} value={profile.description} />
                                    <LoadingButton onClick={handleSubmit}  size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                                        variant="contained" >{t('words.save_change')}
                                    </LoadingButton>
                                </Stack>

                            </AccordionDetails>
                        </Accordion>






                    </Stack>
                </form>

            </Container>


        </Page>
    )
}
