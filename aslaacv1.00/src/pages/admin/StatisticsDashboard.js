import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Paper,
  Chip,
  Button
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import Page from '../../components/Page';
import Layout from '../../layout';
import axios from '../../utils/axios';
import useAuth from '../../hooks/useAuth';
import statisticsMqttService from '../../services/statisticsMqttService';
import io from 'socket.io-client';
import { HOST_API } from '../../config';

// Import chart components
import CommandOverviewChart from '../../components/admin/statistics/CommandOverviewChart';
import ResponseTimeChart from '../../components/admin/statistics/ResponseTimeChart';
import TopUsersTable from '../../components/admin/statistics/TopUsersTable';
import DevicePerformanceChart from '../../components/admin/statistics/DevicePerformanceChart';
import TimeBasedChart from '../../components/admin/statistics/TimeBasedChart';
import StatisticsFilters from '../../components/admin/statistics/StatisticsFilters';
import ExportButton from '../../components/admin/statistics/ExportButton';
import DetailedCommandLogs from '../../components/admin/statistics/DetailedCommandLogs';
import RealtimeStatistics from '../../components/admin/statistics/RealtimeStatistics';
import DeviceDetailsDialog from '../../components/admin/statistics/DeviceDetailsDialog';
import UserDetailsDialog from '../../components/admin/statistics/UserDetailsDialog';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`statistics-tabpanel-${index}`}
      aria-labelledby={`statistics-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function StatisticsDashboard() {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [realtimeConnected, setRealtimeConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Filter states
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
    endDate: new Date(),
    period: 'daily',
    deviceNumber: '',
    userId: '',
    commandType: ''
  });

  // Data states
  const [commandStats, setCommandStats] = useState(null);
  const [topUsers, setTopUsers] = useState([]);
  const [deviceAnalytics, setDeviceAnalytics] = useState(null);
  const [timeBasedStats, setTimeBasedStats] = useState([]);

  // Real-time update counters
  const [realtimeStats, setRealtimeStats] = useState({
    newCommands: 0,
    activeDevices: 0,
    activeUsers: 0
  });
  const commandCountRef = useRef(0);

  // Drill-down dialog states
  const [showDetailedLogs, setShowDetailedLogs] = useState(false);
  const [showDeviceDetails, setShowDeviceDetails] = useState(false);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [selectedUser, setSelectedUser] = useState({ id: null, info: null });

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    // Update MQTT subscription with new filters
    if (realtimeConnected) {
      statisticsMqttService.publishFilters(newFilters);
    }
  };









  // Drill-down handlers
  const handleUserClick = useCallback((userId, userInfo) => {
    setSelectedUser({ id: userId, info: userInfo });
    setShowUserDetails(true);
  }, []);

  const handleDeviceClick = useCallback((deviceNumber) => {
    setSelectedDevice(deviceNumber);
    setShowDeviceDetails(true);
  }, []);



  // Fetch command statistics
  const fetchCommandStatistics = useCallback(async () => {
    try {
      console.log('Fetching command statistics with filters:', filters);
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        period: filters.period,
        ...(filters.deviceNumber && { deviceNumber: filters.deviceNumber }),
        ...(filters.userId && { userId: filters.userId }),
        ...(filters.commandType && { commandType: filters.commandType })
      };

      console.log('API call params:', params);
      const response = await axios.get('/api/admin/statistics/commands', { params });
      console.log('Command statistics response:', response.data);

      if (response.data.success) {
        setCommandStats(response.data.data);
        console.log('Command stats set:', response.data.data);
      } else {
        console.error('API returned success=false:', response.data);
        setError(response.data.error || 'Failed to fetch command statistics');
      }
    } catch (err) {
      console.error('Error fetching command statistics:', err);
      console.error('Error details:', err.response?.data);
      setError(`Failed to fetch command statistics: ${err.response?.data?.error || err.message}`);
    }
  }, [filters]);

  // Fetch top users
  const fetchTopUsers = useCallback(async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        limit: 10
      };

      const response = await axios.get('/api/admin/statistics/top-users', { params });
      if (response.data.success) {
        setTopUsers(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching top users:', err);
      setError('Failed to fetch top users');
    }
  }, [filters]);

  // Fetch device analytics
  const fetchDeviceAnalytics = useCallback(async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        limit: 20
      };

      const response = await axios.get('/api/admin/statistics/device-analytics', { params });
      if (response.data.success) {
        setDeviceAnalytics(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching device analytics:', err);
      setError('Failed to fetch device analytics');
    }
  }, [filters]);

  // Fetch time-based statistics
  const fetchTimeBasedStatistics = useCallback(async () => {
    try {
      const params = {
        startDate: filters.startDate.toISOString(),
        endDate: filters.endDate.toISOString(),
        groupBy: 'day',
        period: filters.period
      };

      const response = await axios.get('/api/admin/statistics/time-based', { params });
      if (response.data.success) {
        setTimeBasedStats(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching time-based statistics:', err);
      setError('Failed to fetch time-based statistics');
    }
  }, [filters]);

  // Create sample data for testing
  const createSampleData = async () => {
    try {
      setLoading(true);
      console.log('Creating sample data...');

      const response = await axios.post('/api/admin/statistics/create-sample-data');
      console.log('Sample data response:', response.data);

      if (response.data.success) {
        console.log('Sample data created successfully');
        // Refresh the data after creating samples
        await loadData();
      } else {
        setError(response.data.error || 'Failed to create sample data');
      }
    } catch (err) {
      console.error('Error creating sample data:', err);
      setError(`Failed to create sample data: ${err.response?.data?.error || err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Load all data
  const loadData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      await Promise.all([
        fetchCommandStatistics(),
        fetchTopUsers(),
        fetchDeviceAnalytics(),
        fetchTimeBasedStatistics()
      ]);
    } catch (err) {
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  }, [fetchCommandStatistics, fetchTopUsers, fetchDeviceAnalytics, fetchTimeBasedStatistics]);

  // Setup Socket.IO real-time connection for statistics
  useEffect(() => {
    if (user) {
      console.log('Statistics Dashboard: Setting up Socket.IO connection');

      const socket = io.connect(HOST_API, {
        transports: ["polling", "websocket"],
      });

      socket.on("connect", () => {
        console.log('Statistics Dashboard: Connected to Socket.IO');
        console.log('Statistics Dashboard: User data:', user);
        socket.emit("logined", user);
        setRealtimeConnected(true);
        setError(null);
      });

      socket.on("disconnect", () => {
        console.log('Statistics Dashboard: Disconnected from Socket.IO');
        setRealtimeConnected(false);
      });

      // Listen for real-time statistics updates
      socket.on("statistics-command-update", (data) => {
        console.log('🔥 Statistics Dashboard: Received command update:', data);
        console.log('🔥 Current command count ref:', commandCountRef.current);
        console.log('🔥 Current realtime stats:', realtimeStats);

        if (data.type === 'command_logged') {
          // Update real-time stats
          setRealtimeStats(prev => {
            const newStats = {
              ...prev,
              newCommands: prev.newCommands + 1
            };
            console.log('🔥 Updated realtime stats:', newStats);
            return newStats;
          });

          // Increment command count ref
          commandCountRef.current += 1;
          console.log('🔥 Incremented command count ref to:', commandCountRef.current);

          // Refresh statistics every 2 commands for more responsive updates
          if (commandCountRef.current % 2 === 0) {
            console.log(`🔥 Statistics Dashboard: Refreshing statistics after ${commandCountRef.current} commands`);
            loadData();
          }

          // Update last update timestamp
          setLastUpdate(new Date());
          console.log('🔥 Updated last update timestamp');
        }
      });

      // Cleanup function
      return () => {
        if (socket) {
          socket.disconnect();
          socket.close();
        }
      };
    }
  }, [user, loadData, realtimeStats]); // Added missing dependencies

  useEffect(() => {
    loadData();
  }, [filters, loadData]);

  if (loading && !commandStats) {
    return (
      <Page title="Statistics Dashboard">
        <Layout />
        <Container sx={{ py: { xs: 12 } }} maxWidth="xl">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Page>
    );
  }

  return (
    <Page title="Statistics Dashboard">
      <Layout />
      <Container sx={{ py: { xs: 12 } }} maxWidth="xl">
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4">
              {t('statistics.title')}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 1 }}>
              <Chip
                label={realtimeConnected ? 'Real-time Connected' : 'Real-time Disconnected'}
                color={realtimeConnected ? 'success' : 'error'}
                size="small"
                variant="outlined"
              />
              {lastUpdate && (
                <Typography variant="body2" color="text.secondary">
                  Last update: {lastUpdate.toLocaleTimeString()}
                </Typography>
              )}
              {realtimeConnected && (
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Chip
                    label={`${realtimeStats.newCommands} new commands`}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                  <Chip
                    label={`${realtimeStats.activeDevices} active devices`}
                    size="small"
                    color="info"
                    variant="outlined"
                  />
                  <Chip
                    label={`${realtimeStats.activeUsers} active users`}
                    size="small"
                    color="secondary"
                    variant="outlined"
                  />
                </Box>
              )}
            </Box>
          </Box>
          <ExportButton
            filters={filters}
            onExport={() => {/* Export functionality */}}
          />
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Debug Information */}
        {process.env.NODE_ENV === 'development' && (
          <Card sx={{ mb: 3, p: 2, backgroundColor: '#f5f5f5' }}>
            <Typography variant="h6" gutterBottom>{t('statistics.debug.title')}</Typography>
            <Typography variant="body2">{t('statistics.debug.user_role')}: {user?.role}</Typography>
            <Typography variant="body2">{t('statistics.debug.loading')}: {loading.toString()}</Typography>
            <Typography variant="body2">{t('statistics.debug.command_stats')}: {commandStats ? t('statistics.debug.loaded') : t('statistics.debug.not_loaded')}</Typography>
            <Typography variant="body2">{t('statistics.debug.top_users')}: {topUsers ? `${topUsers.length} ${t('statistics.debug.users')}` : t('statistics.debug.not_loaded')}</Typography>
            <Typography variant="body2">{t('statistics.debug.device_analytics')}: {deviceAnalytics ? `${deviceAnalytics.length} ${t('statistics.debug.devices')}` : t('statistics.debug.not_loaded')}</Typography>
            <Typography variant="body2">{t('statistics.debug.time_based_stats')}: {timeBasedStats ? `${timeBasedStats.length} ${t('statistics.debug.entries')}` : t('statistics.debug.not_loaded')}</Typography>
            <Typography variant="body2">{t('statistics.debug.error')}: {error || t('statistics.debug.none')}</Typography>

            <Box sx={{ mt: 2 }}>
              <Button
                variant="contained"
                size="small"
                onClick={createSampleData}
                disabled={loading}
                sx={{ mr: 1 }}
              >
                {t('statistics.debug.create_sample_data')}
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={loadData}
                disabled={loading}
                sx={{ mr: 1 }}
              >
                🔄 Manual Refresh
              </Button>
              <Typography variant="body2" sx={{ mt: 1 }}>
                🔥 Real-time Commands: {realtimeStats.newCommands} |
                🔗 Socket.IO: {realtimeConnected ? '✅ Connected' : '❌ Disconnected'} |
                📊 Command Count Ref: {commandCountRef.current}
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={loadData}
                disabled={loading}
              >
                {t('statistics.debug.refresh_data')}
              </Button>
            </Box>
          </Card>
        )}

        {/* Real-time Statistics Panel */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}>
            <StatisticsFilters
              filters={filters}
              onFilterChange={handleFilterChange}
              onRefresh={loadData}
              loading={loading}
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <RealtimeStatistics onStatisticsUpdate={loadData} />
          </Grid>
        </Grid>

        <Paper sx={{ width: '100%', mt: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label={t('statistics.tabs.overview')} />
            <Tab label={t('statistics.tabs.command_analytics')} />
            <Tab label={t('statistics.tabs.device_performance')} />
            <Tab label={t('statistics.tabs.user_activity')} />
            <Tab label={t('statistics.tabs.time_analysis')} />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <CommandOverviewChart data={commandStats} />
              </Grid>
              <Grid item xs={12} md={6}>
                <ResponseTimeChart data={commandStats} />
              </Grid>
              <Grid item xs={12}>
                <TopUsersTable
                  data={topUsers}
                  onUserClick={handleUserClick}
                  filters={filters}
                />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <CommandOverviewChart data={commandStats} detailed={true} />
              </Grid>
              <Grid item xs={12} md={6}>
                <ResponseTimeChart data={commandStats} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Command Type Distribution
                    </Typography>
                    {/* Command type chart will be implemented */}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <DevicePerformanceChart
                  data={deviceAnalytics}
                  onDeviceClick={handleDeviceClick}
                  filters={filters}
                />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TopUsersTable
                  data={topUsers}
                  detailed={true}
                  onUserClick={handleUserClick}
                  filters={filters}
                />
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={4}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TimeBasedChart data={timeBasedStats} />
              </Grid>
            </Grid>
          </TabPanel>
        </Paper>

        {/* Drill-down Dialogs */}
        <DetailedCommandLogs
          open={showDetailedLogs}
          onClose={() => setShowDetailedLogs(false)}
          filters={filters}
          title="Detailed Command Logs"
        />

        <DeviceDetailsDialog
          open={showDeviceDetails}
          onClose={() => setShowDeviceDetails(false)}
          deviceNumber={selectedDevice}
          filters={filters}
        />

        <UserDetailsDialog
          open={showUserDetails}
          onClose={() => setShowUserDetails(false)}
          userId={selectedUser.id}
          userInfo={selectedUser.info}
          filters={filters}
        />
      </Container>
    </Page>
  );
}
