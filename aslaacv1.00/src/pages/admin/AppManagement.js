import React from 'react';
import { Container, Typography, Grid, Divider } from '@mui/material';
import Page from '../../components/Page';
import Layout from '../../layout';
import ApkUploadComponent from '../../components/admin/ApkUploadComponent';
import SslValidationUpload from '../../components/admin/SslValidationUpload';
export default function AppManagement() {
  
  // We no longer need this check since we have AdminGuard
  // if (!user?.role?.includes("admin")) {
  //   return <Navigate to="/" />;
  // }

  return (
    <Page title="App Management">
      <Layout />
      <Container sx={{ py: { xs: 12 } }} maxWidth="md">
        <Typography variant="h4" sx={{ mb: 3 }}>
          App Management
        </Typography>
        <Divider sx={{ mb: 3 }} />
        
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <ApkUploadComponent />
          </Grid>
          <Grid item xs={12}>
            <SslValidationUpload />
          </Grid>
        </Grid>
      </Container>
    </Page>
  );
}
