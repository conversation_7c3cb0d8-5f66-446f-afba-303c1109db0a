import * as Yup from 'yup';
import { useSnackbar } from 'notistack';
import { useState, useEffect, useMemo, useCallback } from 'react';
import CloseIcon from '@mui/icons-material/Close';

// @mui
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import {
    Button,
    Typography,
    Divider,
    Dialog,
    DialogTitle,
    Stack,
    DialogContent,
    List,
    ListItemIcon,
    ListItemText,
    ListItem,
    IconButton,
    Box,
} from '@mui/material';
// components
import Iconify from '../../components/Iconify';
import { FormProvider, RHFSelect, RHFSwitch, RHFTextField } from '../../components/hook-form';

// sections
import axios from "../../utils/axios";
import { LoadingButton } from '@mui/lab';

export default function DeviceDialog({ user, open, onClose }) {
    const [current, setCurrent] = useState(null);
    const { enqueueSnackbar } = useSnackbar();
    const [devices, setDevices] = useState([]);
    
    const EditSchema = Yup.object().shape({
        // Add any schema rules here if required
    });
    
    const defaultValues = useMemo(() => ({
        deviceNumber: current?.deviceNumber || '',
        type: current?.type || '4g',
        uix: current?.uix || 'Car',
        isDefault: current?.isDefault || true,
    }), [current]);

    const methods = useForm({
        resolver: yupResolver(EditSchema),
        defaultValues,
    });

    const {
        reset,
        handleSubmit,
        formState: { isSubmitting },
    } = methods;

    const onSubmit = (data) => {
        axios.post('/api/device/register', { ...data, _id: current?._id, phoneNumber: user?.phoneNumber }).then(res => {
            if (res.status === 200 && res.data.success) {
                enqueueSnackbar(res.data?.message || 'Saved successful', { variant: 'success' });
                reset();
                load();
            }
            else {
                enqueueSnackbar(res.data?.message, { variant: 'error' });
            }
        }).catch(err => {
            console.error(err);
        });
    };

    const handleDelete = (device) => {
        axios.post('/api/device/delete', { deviceNumber: device.deviceNumber }).then(res => {
            if (res.data.success) {
                load();
            } else {
                console.error('Delete failed:', res.data.message);
            }
        }).catch(err => {
            console.error(err);
        });
    };

    const load = useCallback(() => {
        axios.post('/api/device/gets', { phoneNumber: user.phoneNumber }).then(res => {
            if (res.status === 200 && res.data.success) {
                setDevices(res.data.devices);
            }
        }).catch(err => {
            console.error(err);
        });
    }, [user]); 

    useEffect(() => {
        reset(defaultValues);
    }, [current, reset, defaultValues]); // Add 'reset' and 'defaultValues' as dependencies

    useEffect(() => {
        if (user) load();
    }, [user, load]); // Add 'load' as a dependency

    return (
        <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose}>
            <DialogTitle>
                {`${user?.username || user?.phoneNumber}`}
                <IconButton
                    aria-label="close"
                    onClick={onClose}
                    sx={{
                        position: 'absolute',
                        right: 8,
                        top: 8,
                        color: (theme) => theme.palette.grey[500],
                    }}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>

            <DialogContent>
                <Stack gap={1}>
                    <List>
                        <Divider />
                        {devices?.map((device, index) => (
                            <Box key={index}>
                                <ListItem>
                                    <ListItemIcon>
                                        <Iconify height={30} width={30} icon={`${device?.uix === 'Car' ? 'fa-solid:car' : 'ion:hardware-chip-outline'}`} />
                                    </ListItemIcon>
                                    <ListItemText>{device?.deviceNumber}</ListItemText>
                                    <ListItemIcon>
                                        <Iconify height={30} width={30} icon={'healthicons:network-4g-outline'} />
                                    </ListItemIcon>
                                    <IconButton onClick={() => handleDelete(device)}>
                                        <Iconify height={20} width={20} icon={'fluent:delete-16-regular'} />
                                    </IconButton>
                                    <IconButton onClick={() => setCurrent(device)}>
                                        <Iconify height={20} width={20} icon={'arcticons:set-edit'} />
                                    </IconButton>
                                </ListItem>
                                <Divider />
                            </Box>
                        ))}
                    </List>
                </Stack>

                <Stack gap={2}>
                    <Typography sx={{ mt: 2 }}>Device Information</Typography>
                    <FormProvider methods={methods} onSubmit={handleSubmit(onSubmit)}>
                        <Stack spacing={3}>
                            <RHFTextField name="deviceNumber" label="Device Number" />
                            <RHFSelect name="type" label="Device Type">
                                <option value="4g">4G Net</option>
                            </RHFSelect>
                            <RHFSelect name="uix" label="UIX Type">
                                <option value="Car">Car</option>
                                <option value="Chip">Chip</option>
                            </RHFSelect>
                            <RHFSwitch name="isDefault" label="Default Device" />
                            <Stack gap={1} direction={{ sm: 'row', xs: 'column' }}>
                                <Button fullWidth size="large" sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }} onClick={() => setCurrent(null)}>
                                    Reset
                                </Button>
                                <LoadingButton
                                    fullWidth
                                    size="large"
                                    sx={{ bgcolor: 'grey.50016', border: '1px solid', borderColor: 'grey.50048' }}
                                    type="submit"
                                    variant="contained"
                                    loading={isSubmitting}
                                >
                                    Save Changes.
                                </LoadingButton>
                            </Stack>
                        </Stack>
                    </FormProvider>
                </Stack>
            </DialogContent>
        </Dialog>
    );
}