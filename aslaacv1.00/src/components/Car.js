import { Box } from "@mui/material";


export default function Car({ device, status, action, color = 'black', title = "" }) {


    return (
        <Box sx={{
            width: { xs: '100%', sm: '480px', md: '520px', lg: '580px' },
            maxWidth: '580px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center'
        }}>
            <Box sx={{
                width: '100%',
                height: { xs: '320px', sm: '420px', md: '460px', lg: '520px' },
                position: 'relative',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
            }}>
                <img
                    src={`/images/car-${color}-body.png`}
                    alt={`Car body of ${color}`}
                    style={{
                        width: "100%",
                        height: "auto",
                        position: "absolute",
                        left: 0,
                        top: '50%',
                        transform: 'translateY(-50%)',
                        objectFit: 'contain'
                    }}
                />
                {(status && status.sta === 1) && <>
                    <img
                        src={'/images/car-front-light.svg'}
                        alt="Car front light"
                        style={{
                            width: "100%",
                            height: "auto",
                            position: "absolute",
                            left: 0,
                            top: '50%',
                            transform: 'translateY(-50%)',
                            objectFit: 'contain'
                        }}
                    />
                </>}
                {status && (status?.sta === 1 || status?.light === 1) && <>
                    <img
                        src={'/images/car-rear-light.svg'}
                        alt="Car rear light"
                        style={{
                            width: "100%",
                            height: "auto",
                            position: "absolute",
                            left: 0,
                            top: '50%',
                            transform: 'translateY(-50%)',
                            objectFit: 'contain'
                        }}
                    />
                </>}
                <img
                    src={`/images/car-${color}-l-door.svg`}
                    alt="Car left door"
                    style={{
                        position: "absolute",
                        width: "100%",
                        height: "auto",
                        top: '50%',
                        transform: 'translateY(-50%)',
                        objectFit: 'contain'
                    }}
                />
                <img
                    src={`/images/car-${color}-r-door.svg`}
                    alt="Car right door"
                    style={{
                        position: "absolute",
                        width: "100%",
                        height: "auto",
                        top: '50%',
                        transform: 'translateY(-50%)',
                        objectFit: 'contain'
                    }}
                />
            </Box>
            {/* Removed Typography components for device name and speed */}
        </Box>
    )
}
