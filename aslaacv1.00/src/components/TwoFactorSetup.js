import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  TextField,
  Alert,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Chip,
  Grid,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
// LoadingButton replaced with regular Button + CircularProgress
import { ContentCopy, Download, Security } from '@mui/icons-material';
// import QRCode from 'qrcode.react';
import { useSnackbar } from 'notistack';
import axios from '../utils/axios';

const steps = ['Setup', 'Verify', 'Backup Codes'];

const TwoFactorSetup = ({ open, onClose, onComplete }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [qrCode, setQrCode] = useState('');
  const [secret, setSecret] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [backupCodes, setBackupCodes] = useState([]);
  const [error, setError] = useState('');
  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    if (open && activeStep === 0) {
      initiate2FASetup();
    }
  }, [open, activeStep]);

  const initiate2FASetup = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await axios.post('/api/2fa/setup');
      
      if (response.data.status === 200) {
        setQrCode(response.data.data.qrCode);
        setSecret(response.data.data.secret);
        setActiveStep(1);
      } else {
        setError(response.data.message || 'Failed to setup 2FA');
      }
    } catch (error) {
      console.error('2FA setup error:', error);
      setError(error.response?.data?.message || 'Failed to setup 2FA');
    } finally {
      setLoading(false);
    }
  };

  const verifyAndEnable2FA = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const response = await axios.post('/api/2fa/enable', {
        token: verificationCode
      });
      
      if (response.data.status === 200) {
        setBackupCodes(response.data.data.backupCodes);
        setActiveStep(2);
        enqueueSnackbar('2FA enabled successfully!', { variant: 'success' });
      } else {
        setError(response.data.message || 'Invalid verification code');
      }
    } catch (error) {
      console.error('2FA verification error:', error);
      setError(error.response?.data?.message || 'Failed to verify code');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    enqueueSnackbar('Copied to clipboard!', { variant: 'success' });
  };

  const downloadBackupCodes = () => {
    const content = `ASLAA 2FA Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\n\n${backupCodes.join('\n')}\n\nKeep these codes safe! Each code can only be used once.`;
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'aslaa-backup-codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    enqueueSnackbar('Backup codes downloaded!', { variant: 'success' });
  };

  const handleComplete = () => {
    onComplete();
    onClose();
    setActiveStep(0);
    setVerificationCode('');
    setError('');
  };

  const handleClose = () => {
    onClose();
    setActiveStep(0);
    setVerificationCode('');
    setError('');
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Box textAlign="center" py={2}>
            {loading ? (
              <Typography>Setting up 2FA...</Typography>
            ) : (
              <Typography>Initializing 2FA setup...</Typography>
            )}
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom textAlign="center">
              Scan QR Code with Google Authenticator
            </Typography>
            
            <Box display="flex" justifyContent="center" mb={3}>
              <Paper elevation={3} sx={{ p: 2, display: 'inline-block' }}>
                {qrCode ? (
                  <img
                    src={qrCode}
                    alt="QR Code for 2FA Setup"
                    style={{ width: 200, height: 200 }}
                  />
                ) : (
                  <Box
                    sx={{
                      width: 200,
                      height: 200,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'grey.100'
                    }}
                  >
                    <Typography>Loading QR Code...</Typography>
                  </Box>
                )}
              </Paper>
            </Box>

            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                1. Install Google Authenticator on your phone<br/>
                2. Scan the QR code above<br/>
                3. Enter the 6-digit code from the app below
              </Typography>
            </Alert>

            <Box mb={2}>
              <Typography variant="subtitle2" gutterBottom>
                Manual Entry Key (if you can't scan):
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <TextField
                  value={secret}
                  size="small"
                  fullWidth
                  InputProps={{ readOnly: true }}
                />
                <Tooltip title="Copy to clipboard">
                  <IconButton onClick={() => copyToClipboard(secret)}>
                    <ContentCopy />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>

            <TextField
              label="Verification Code"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              fullWidth
              placeholder="Enter 6-digit code"
              inputProps={{ maxLength: 6, style: { textAlign: 'center', fontSize: '1.2em' } }}
            />
          </Box>
        );

      case 2:
        return (
          <Box>
            <Box textAlign="center" mb={3}>
              <Security color="success" sx={{ fontSize: 48, mb: 1 }} />
              <Typography variant="h6" color="success.main">
                2FA Successfully Enabled!
              </Typography>
            </Box>

            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Important: Save Your Backup Codes
              </Typography>
              <Typography variant="body2">
                These backup codes can be used to access your account if you lose your authenticator device. 
                Each code can only be used once.
              </Typography>
            </Alert>

            <Paper elevation={1} sx={{ p: 2, mb: 2, bgcolor: 'grey.50' }}>
              <Grid container spacing={1}>
                {backupCodes.map((code, index) => (
                  <Grid item xs={6} key={index}>
                    <Chip 
                      label={code} 
                      variant="outlined" 
                      size="small"
                      sx={{ fontFamily: 'monospace', width: '100%' }}
                    />
                  </Grid>
                ))}
              </Grid>
            </Paper>

            <Box display="flex" gap={1} justifyContent="center">
              <Button
                variant="outlined"
                startIcon={<ContentCopy />}
                onClick={() => copyToClipboard(backupCodes.join('\n'))}
              >
                Copy Codes
              </Button>
              <Button
                variant="outlined"
                startIcon={<Download />}
                onClick={downloadBackupCodes}
              >
                Download
              </Button>
            </Box>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box>
          <Typography variant="h6" component="div">Enable Two-Factor Authentication</Typography>
          <Stepper activeStep={activeStep} sx={{ mt: 2 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {renderStepContent()}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          {activeStep === 2 ? 'Close' : 'Cancel'}
        </Button>
        
        {activeStep === 1 && (
          <Button
            onClick={verifyAndEnable2FA}
            variant="contained"
            disabled={loading || verificationCode.length !== 6}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            Verify & Enable
          </Button>
        )}
        
        {activeStep === 2 && (
          <Button onClick={handleComplete} variant="contained">
            Complete Setup
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default TwoFactorSetup;
