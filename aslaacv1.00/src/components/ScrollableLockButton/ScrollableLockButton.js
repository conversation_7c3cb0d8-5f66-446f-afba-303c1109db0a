import { useState, useCallback, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import Iconify from '../Iconify';

ScrollableLockButton.propTypes = {
  onLockClick: PropTypes.func.isRequired,
  onMirrorClick: PropTypes.func.isRequired,
  isLocked: PropTypes.bool,
  lockLabel: PropTypes.string,
  mirrorLabel: PropTypes.string,
  sx: PropTypes.object,
  disabled: PropTypes.bool,
  lockIcon: PropTypes.string,
  mirrorIcon: PropTypes.string,
  colorScheme: PropTypes.object,
  size: PropTypes.number,
  showScrollIndicator: PropTypes.bool,
};

export default function ScrollableLockButton({
  onLockClick,
  onMirrorClick,
  isLocked = false,
  lockLabel = 'Lock',
  mirrorLabel = 'Mirror',
  sx = {},
  disabled = false,
  lockIcon = 'material-symbols:lock',
  mirrorIcon = 'mdi:car-side-mirror',
  colorScheme = {},
  size = 90,
  showScrollIndicator = true,
  ...other
}) {
  const [isMirrorMode, setIsMirrorMode] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const resetTimeoutRef = useRef(null);
  const scrollTimeoutRef = useRef(null);

  // Auto-reset to lock mode after 8 seconds
  const setResetTimeout = useCallback(() => {
    if (resetTimeoutRef.current) {
      clearTimeout(resetTimeoutRef.current);
    }
    resetTimeoutRef.current = setTimeout(() => {
      setIsMirrorMode(false);
    }, 8000);
  }, []);

  // Ref for the button element to add non-passive wheel listeners
  const buttonRef = useRef(null);

  // Handle scroll/swipe gestures
  const handleWheel = useCallback((event) => {
    if (disabled) return;
    
    // Check if this is an actual wheel event (not a click)
    if (!event.deltaY && !event.deltaX && !event.deltaZ) {
      return; // Not a real wheel event, ignore
    }
    
    // Try to prevent default, but handle passive listener gracefully
    try {
      event.preventDefault();
      event.stopPropagation();
    } catch (e) {
      // Passive event listener, can't prevent default
      console.warn('Cannot prevent default on passive wheel event');
    }
    
    setIsScrolling(true);
    
    // Clear existing scroll timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    // Determine scroll direction
    const deltaY = event.deltaY;
    const newMode = deltaY > 0; // Scroll down = Mirror mode, Scroll up = Lock mode
    
    setIsMirrorMode(newMode);
    
    if (newMode) {
      setResetTimeout();
    } else {
      if (resetTimeoutRef.current) {
        clearTimeout(resetTimeoutRef.current);
      }
    }
    
    // Reset scrolling state after animation
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 300);
  }, [disabled, setResetTimeout]);

  // Handle touch gestures for mobile
  const touchStartY = useRef(0);
  const touchEndY = useRef(0);
  const touchStartTime = useRef(0);

  const handleTouchStart = useCallback((event) => {
    if (disabled) return;
    touchStartY.current = event.touches[0].clientY;
    touchStartTime.current = Date.now();
  }, [disabled]);

  const handleTouchMove = useCallback((event) => {
    if (disabled) return;
    touchEndY.current = event.touches[0].clientY;
  }, [disabled]);

  const handleTouchEnd = useCallback((event) => {
    if (disabled) return;
    
    const deltaY = touchStartY.current - touchEndY.current;
    const touchDuration = Date.now() - touchStartTime.current;
    const minSwipeDistance = 40;
    
    // Only treat as swipe if distance and duration criteria are met
    if (Math.abs(deltaY) > minSwipeDistance && touchDuration > 100 && touchDuration < 1000) {
      try {
        event.preventDefault();
        event.stopPropagation();
      } catch (e) {
        console.warn('Cannot prevent default on passive touch event');
      }
      
      setIsScrolling(true);
      
      // Swipe up = Mirror mode, Swipe down = Lock mode
      const newMode = deltaY > 0;
      setIsMirrorMode(newMode);
      
      if (newMode) {
        setResetTimeout();
      } else {
        if (resetTimeoutRef.current) {
          clearTimeout(resetTimeoutRef.current);
        }
      }
      
      // Reset scrolling state
      setTimeout(() => {
        setIsScrolling(false);
      }, 300);
    }
  }, [disabled, setResetTimeout]);

  // Handle button click
  const handleClick = useCallback((event) => {
    if (disabled || isScrolling) return;
    
    // Ensure this is a genuine click event
    if (event.type !== 'click') return;
    
    if (isMirrorMode) {
      onMirrorClick(event);
    } else {
      onLockClick(event);
    }
  }, [disabled, isScrolling, isMirrorMode, onMirrorClick, onLockClick]);

  // Setup non-passive wheel event listener
  useEffect(() => {
    const buttonElement = buttonRef.current;
    if (!buttonElement) return;

    const wheelHandler = (event) => {
      handleWheel(event);
    };

    try {
      buttonElement.addEventListener('wheel', wheelHandler, { passive: false });
    } catch (e) {
      buttonElement.addEventListener('wheel', wheelHandler);
    }

    return () => {
      if (buttonElement) {
        buttonElement.removeEventListener('wheel', wheelHandler);
      }
    };
  }, [handleWheel]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (resetTimeoutRef.current) {
        clearTimeout(resetTimeoutRef.current);
      }
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // Get current colors and content based on mode
  const currentColors = isMirrorMode ? colorScheme.mirror : (isLocked ? colorScheme.lock.locked : colorScheme.lock.unlocked);
  const currentIcon = isMirrorMode ? mirrorIcon : (isLocked ? 'material-symbols:lock-open' : lockIcon);
  const currentLabel = isMirrorMode ? mirrorLabel : lockLabel;

  return (
    <Box
      sx={{
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        ...sx,
      }}
      {...other}
    >
      {/* Scroll Indicator */}
      <AnimatePresence>
        {showScrollIndicator && !isMirrorMode && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            style={{
              position: 'absolute',
              top: -25,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              pointerEvents: 'none',
              zIndex: 10,
            }}
          >
            <motion.div
              animate={{ 
                y: [0, -3, 0],
                opacity: [0.6, 1, 0.6]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Iconify
                icon="material-symbols:keyboard-arrow-up"
                width={16}
                height={16}
                sx={{ 
                  color: colorScheme.mirror?.primary || '#6b7280',
                  filter: `drop-shadow(0 0 4px ${colorScheme.mirror?.glow || 'rgba(107, 114, 128, 0.4)'})`
                }}
              />
            </motion.div>
            <Typography
              variant="caption"
              sx={{
                fontSize: '0.6rem',
                color: colorScheme.mirror?.primary || '#6b7280',
                textShadow: `0 0 4px ${colorScheme.mirror?.glow || 'rgba(107, 114, 128, 0.4)'}`,
                mt: 0.5,
              }}
            >
              Mirror
            </Typography>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Button */}
      <motion.div
        whileHover={{ scale: 1.02, y: -2 }}
        whileTap={{ scale: 0.98 }}
        animate={{
          scale: isScrolling ? 1.05 : 1,
          rotateY: isScrolling ? (isMirrorMode ? 180 : 0) : 0,
        }}
        transition={{ 
          duration: 0.3,
          ease: "easeInOut"
        }}
      >
        <Box
          ref={buttonRef}
          role="button"
          onClick={handleClick}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onContextMenu={(e) => e.preventDefault()}
          sx={{
            width: size,
            height: size,
            borderRadius: '16px',
            background: '#262626', // Premium dark gray for automotive feel
            border: 'none', // Remove all borders
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.15)', // Clean subtle shadow
            transition: "all 0.15s cubic-bezier(0.4, 0, 0.2, 1)", // Faster, smoother transitions
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: disabled ? 'not-allowed' : 'pointer',
            position: 'relative',
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none',
            WebkitTouchCallout: 'none',
            WebkitTapHighlightColor: 'transparent',
            opacity: disabled ? 0.6 : 1,
            '&:hover': {
              transform: 'translateY(-1px)', // Subtle elevation
              boxShadow: `
                0 4px 8px rgba(0, 0, 0, 0.2),
                inset 0 0 5px ${currentColors?.accent || '#14b8a6'}30
              `, // Increased shadow + inner glow
            },
            '&:active': {
              transform: 'translateY(1px)', // Depression effect
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.15)', // Reduced shadow on press
              transition: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)', // Smooth transition
            },
          }}
        >
          {/* Icon with smooth transition */}
          <motion.div
            key={currentIcon}
            initial={{ opacity: 0, scale: 0.8, rotateY: 180 }}
            animate={{ opacity: 1, scale: 1, rotateY: 0 }}
            exit={{ opacity: 0, scale: 0.8, rotateY: -180 }}
            transition={{ duration: 0.3 }}
          >
            <Iconify
              icon={currentIcon}
              width={28}
              height={28}
              sx={{
                color: currentColors?.icon || currentColors?.text || '#d4d4d4',
                mb: 1,
                pointerEvents: 'none',
                zIndex: 1,
              }}
            />
          </motion.div>
          
          {/* Label with smooth transition */}
          <motion.div
            key={currentLabel}
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Typography
              variant="caption"
              sx={{
                color: '#f0f0f0', // Clean off-white text
                fontSize: '14px', // Consistent 14px font size
                fontWeight: 300, // Light font weight
                fontFamily: '"Inter", "SF Pro Display", "Roboto", sans-serif',
                textAlign: 'center',
                lineHeight: 1.2,
                maxWidth: '70px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                userSelect: 'none',
                WebkitUserSelect: 'none',
                MozUserSelect: 'none',
                msUserSelect: 'none',
                pointerEvents: 'none',
                zIndex: 1,
                mt: 1, // 8px spacing between icon and label
              }}
            >
              {currentLabel}
            </Typography>
          </motion.div>
        </Box>
      </motion.div>
    </Box>
  );
}
