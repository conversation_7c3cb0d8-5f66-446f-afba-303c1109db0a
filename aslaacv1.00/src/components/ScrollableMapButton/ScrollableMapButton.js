import { useState, useCallback, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import Iconify from '../Iconify';

ScrollableMapButton.propTypes = {
  onMapClick: PropTypes.func.isRequired,
  onHistoryClick: PropTypes.func.isRequired,
  mapLabel: PropTypes.string,
  historyLabel: PropTypes.string,
  sx: PropTypes.object,
  disabled: PropTypes.bool,
  mapIcon: PropTypes.string,
  historyIcon: PropTypes.string,
  colorScheme: PropTypes.object,
  size: PropTypes.number,
  showScrollIndicator: PropTypes.bool,
};

export default function ScrollableMapButton({
  onMapClick,
  onHistoryClick,
  mapLabel = 'Map',
  historyLabel = 'History',
  sx = {},
  disabled = false,
  mapIcon = 'material-symbols:location-on',
  historyIcon = 'material-symbols:history',
  colorScheme = {},
  size = 90,
  showScrollIndicator = true,
  ...other
}) {
  const [isHistoryMode, setIsHistoryMode] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const resetTimeoutRef = useRef(null);
  const scrollTimeoutRef = useRef(null);

  // Auto-reset to map mode after 8 seconds
  const setResetTimeout = useCallback(() => {
    if (resetTimeoutRef.current) {
      clearTimeout(resetTimeoutRef.current);
    }
    resetTimeoutRef.current = setTimeout(() => {
      setIsHistoryMode(false);
    }, 8000);
  }, []);

  // Handle scroll/swipe gestures
  const handleWheel = useCallback((event) => {
    if (disabled) return;

    // Check if this is an actual wheel event (not a click)
    // Wheel events have deltaY, deltaX, or deltaZ values
    if (!event.deltaY && !event.deltaX && !event.deltaZ) {
      return; // Not a real wheel event, ignore
    }

    // Try to prevent default, but handle passive listener gracefully
    try {
      event.preventDefault();
      event.stopPropagation();
    } catch (e) {
      // Passive event listener, can't prevent default
      console.warn('Cannot prevent default on passive wheel event');
    }

    setIsScrolling(true);

    // Clear existing scroll timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Determine scroll direction
    const deltaY = event.deltaY;
    const newMode = deltaY > 0; // Scroll down = History mode, Scroll up = Map mode

    setIsHistoryMode(newMode);

    if (newMode) {
      setResetTimeout();
    } else {
      if (resetTimeoutRef.current) {
        clearTimeout(resetTimeoutRef.current);
      }
    }

    // Reset scrolling state after animation
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 300);
  }, [disabled, setResetTimeout]);

  // Handle touch gestures for mobile
  const touchStartY = useRef(0);
  const touchEndY = useRef(0);
  const touchStartTime = useRef(0);

  const handleTouchStart = useCallback((event) => {
    if (disabled) return;
    touchStartY.current = event.touches[0].clientY;
    touchStartTime.current = Date.now();
  }, [disabled]);

  const handleTouchMove = useCallback((event) => {
    if (disabled) return;
    touchEndY.current = event.touches[0].clientY;
  }, [disabled]);

  const handleTouchEnd = useCallback((event) => {
    if (disabled) return;

    const deltaY = touchStartY.current - touchEndY.current;
    const touchDuration = Date.now() - touchStartTime.current;
    const minSwipeDistance = 40; // Increased threshold to avoid accidental triggers

    // Only treat as swipe if:
    // 1. Distance is above threshold
    // 2. Duration is reasonable for a swipe (not too quick, not too slow)
    // 3. It's not a quick tap
    if (Math.abs(deltaY) > minSwipeDistance && touchDuration > 100 && touchDuration < 1000) {
      // Only prevent default for actual swipe gestures, not taps
      try {
        event.preventDefault();
        event.stopPropagation();
      } catch (e) {
        // Handle passive event listener
        console.warn('Cannot prevent default on passive touch event');
      }

      setIsScrolling(true);

      // Swipe up = History mode, Swipe down = Map mode
      const newMode = deltaY > 0;
      setIsHistoryMode(newMode);

      if (newMode) {
        setResetTimeout();
      } else {
        if (resetTimeoutRef.current) {
          clearTimeout(resetTimeoutRef.current);
        }
      }

      // Reset scrolling state
      setTimeout(() => {
        setIsScrolling(false);
      }, 300);
    }
    // If it's not a swipe, let the click event handle it normally
  }, [disabled, setResetTimeout]);

  // Handle button click
  const handleClick = useCallback((event) => {
    if (disabled || isScrolling) return;

    // Ensure this is a genuine click event, not triggered by scroll/touch
    if (event.type !== 'click') return;

    // Don't prevent default or stop propagation for click events
    // as the parent handlers need to process them properly

    if (isHistoryMode) {
      onHistoryClick(event);
    } else {
      onMapClick(event);
    }
  }, [disabled, isScrolling, isHistoryMode, onHistoryClick, onMapClick]);

  // Ref for the button element to add non-passive wheel listeners
  const buttonRef = useRef(null);

  // Setup non-passive wheel event listener
  useEffect(() => {
    const buttonElement = buttonRef.current;
    if (!buttonElement) return;

    const wheelHandler = (event) => {
      handleWheel(event);
    };

    // Add wheel event listener with non-passive option
    try {
      buttonElement.addEventListener('wheel', wheelHandler, { passive: false });
    } catch (e) {
      // Fallback for browsers that don't support passive option
      buttonElement.addEventListener('wheel', wheelHandler);
    }

    return () => {
      if (buttonElement) {
        buttonElement.removeEventListener('wheel', wheelHandler);
      }
    };
  }, [handleWheel]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (resetTimeoutRef.current) {
        clearTimeout(resetTimeoutRef.current);
      }
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // Get current colors based on mode
  const currentColors = isHistoryMode ? colorScheme.history : colorScheme.location;
  const currentIcon = isHistoryMode ? historyIcon : mapIcon;
  const currentLabel = isHistoryMode ? historyLabel : mapLabel;

  return (
    <Box
      sx={{
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        ...sx,
      }}
      {...other}
    >
      {/* Scroll Indicator */}
      <AnimatePresence>
        {showScrollIndicator && !isHistoryMode && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            style={{
              position: 'absolute',
              top: -25,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              pointerEvents: 'none',
              zIndex: 10,
            }}
          >
            <motion.div
              animate={{ 
                y: [0, -3, 0],
                opacity: [0.6, 1, 0.6]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Iconify
                icon="material-symbols:keyboard-arrow-up"
                width={16}
                height={16}
                sx={{ 
                  color: colorScheme.history?.primary || '#1e40af',
                  filter: `drop-shadow(0 0 4px ${colorScheme.history?.glow || 'rgba(30, 64, 175, 0.4)'})`
                }}
              />
            </motion.div>
            <Typography
              variant="caption"
              sx={{
                fontSize: '0.6rem',
                color: colorScheme.history?.primary || '#1e40af',
                textShadow: `0 0 4px ${colorScheme.history?.glow || 'rgba(30, 64, 175, 0.4)'}`,
                mt: 0.5,
              }}
            >
              History
            </Typography>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Button */}
      <motion.div
        whileHover={{ scale: 1.02, y: -2 }}
        whileTap={{ scale: 0.98 }}
        animate={{
          scale: isScrolling ? 1.05 : 1,
          rotateY: isScrolling ? (isHistoryMode ? 180 : 0) : 0,
        }}
        transition={{ 
          duration: 0.3,
          ease: "easeInOut"
        }}
      >
        <Box
          ref={buttonRef}
          role="button"
          onClick={handleClick}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onContextMenu={(e) => e.preventDefault()}
          sx={{
            width: size,
            height: size,
            borderRadius: '16px',
            background: '#262626', // Premium dark gray for automotive feel
            border: 'none', // Remove all borders
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.15)', // Clean subtle shadow
            transition: "all 0.15s cubic-bezier(0.4, 0, 0.2, 1)", // Faster, smoother transitions
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: disabled ? 'not-allowed' : 'pointer',
            position: 'relative',
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none',
            WebkitTouchCallout: 'none',
            WebkitTapHighlightColor: 'transparent',
            opacity: disabled ? 0.6 : 1,
            '&::before': {
              content: '""',
              position: 'absolute',
              top: -3,
              left: -3,
              right: -3,
              bottom: -3,
              borderRadius: '50%',
              background: `linear-gradient(45deg, ${currentColors?.accent || 'rgba(234, 88, 12, 0.3)'}, rgba(255,255,255,0.1))`,
              opacity: 0,
              transition: 'opacity 0.3s ease',
              zIndex: 0,
            },
            '&:hover::before': {
              opacity: 1,
            },
            '&:hover': {
              transform: 'translateY(-1px)', // Subtle elevation
              boxShadow: `
                0 4px 8px rgba(0, 0, 0, 0.2),
                inset 0 0 5px ${currentColors?.accent || '#f97316'}30
              `, // Increased shadow + inner glow
            },
            '&:active': {
              transform: 'translateY(1px)', // Depression effect
              boxShadow: '0 1px 2px rgba(0, 0, 0, 0.15)', // Reduced shadow on press
              transition: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)', // Smooth transition
            },
          }}
        >
          {/* Icon with smooth transition */}
          <motion.div
            key={currentIcon}
            initial={{ opacity: 0, scale: 0.8, rotateY: 180 }}
            animate={{ opacity: 1, scale: 1, rotateY: 0 }}
            exit={{ opacity: 0, scale: 0.8, rotateY: -180 }}
            transition={{ duration: 0.3 }}
          >
            <Iconify
              icon={currentIcon}
              width={36}
              height={36}
              sx={{
                color: currentColors?.text || '#ffffff',
                mb: 0.5,
                pointerEvents: 'none',
                zIndex: 1,
              }}
            />
          </motion.div>
          
          {/* Label with smooth transition */}
          <motion.div
            key={currentLabel}
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Typography
              variant="caption"
              sx={{
                color: currentColors?.text || '#ffffff',
                fontSize: '0.6rem',
                textAlign: 'center',
                lineHeight: 1,
                maxWidth: '70px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                userSelect: 'none',
                WebkitUserSelect: 'none',
                MozUserSelect: 'none',
                msUserSelect: 'none',
                pointerEvents: 'none',
                zIndex: 1,
              }}
            >
              {currentLabel}
            </Typography>
          </motion.div>
        </Box>
      </motion.div>
    </Box>
  );
}
