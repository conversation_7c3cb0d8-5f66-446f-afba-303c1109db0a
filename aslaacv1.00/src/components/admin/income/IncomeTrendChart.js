import React from 'react';
import PropTypes from 'prop-types';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Chip
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

IncomeTrendChart.propTypes = {
  data: PropTypes.array,
  period: PropTypes.string,
  detailed: PropTypes.bool
};

export default function IncomeTrendChart({ data = [], period = 'monthly', detailed = false }) {
  const theme = useTheme();
  const { t } = useTranslation();

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('income.charts.trends', 'Income Trends')}
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography variant="body2" color="text.secondary">
              {t('income.no_data', 'No data available')}
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (amount) => {
    return `₮${amount.toLocaleString()}`;
  };

  // Format period labels based on period type
  const formatPeriodLabel = (periodData) => {
    if (!periodData) return '';
    
    switch (period) {
      case 'daily':
        return `${periodData.year}-${String(periodData.month).padStart(2, '0')}-${String(periodData.day).padStart(2, '0')}`;
      case 'yearly':
        return `${periodData.year}`;
      default: // monthly
        return `${periodData.year}-${String(periodData.month).padStart(2, '0')}`;
    }
  };

  // Prepare labels and data
  const labels = data.map(item => formatPeriodLabel(item.period));
  
  // Prepare data for line chart (income trends)
  const lineData = {
    labels,
    datasets: [
      {
        label: t('income.charts.total_income', 'Total Income'),
        data: data.map(item => item.totalIncome),
        borderColor: theme.palette.primary.main,
        backgroundColor: theme.palette.primary.light,
        fill: false,
        tension: 0.4,
        pointRadius: 6,
        pointHoverRadius: 8
      },
      {
        label: t('income.charts.order_income', 'Order Income'),
        data: data.map(item => item.orderIncome),
        borderColor: theme.palette.warning.main,
        backgroundColor: theme.palette.warning.light,
        fill: false,
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 6
      },
      {
        label: t('income.charts.license_income', 'License Income'),
        data: data.map(item => item.licenseIncome),
        borderColor: theme.palette.secondary.main,
        backgroundColor: theme.palette.secondary.light,
        fill: false,
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 6
      }
    ]
  };

  // Prepare data for bar chart (transaction counts)
  const barData = {
    labels,
    datasets: [
      {
        label: t('income.charts.orders', 'Orders'),
        data: data.map(item => item.orderCount),
        backgroundColor: theme.palette.warning.main,
        borderColor: theme.palette.warning.dark,
        borderWidth: 1
      },
      {
        label: t('income.charts.licenses', 'Licenses'),
        data: data.map(item => item.licenseCount),
        backgroundColor: theme.palette.secondary.main,
        borderColor: theme.palette.secondary.dark,
        borderWidth: 1
      }
    ]
  };

  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return formatCurrency(value);
          }
        }
      },
      x: {
        ticks: {
          maxRotation: 45
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'index'
    }
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.parsed.y.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      },
      x: {
        ticks: {
          maxRotation: 45
        }
      }
    }
  };

  // Calculate summary statistics
  const totalIncome = data.reduce((sum, item) => sum + item.totalIncome, 0);
  const totalTransactions = data.reduce((sum, item) => sum + item.totalTransactions, 0);
  const averageIncome = totalIncome / Math.max(data.length, 1);
  const highestIncome = Math.max(...data.map(item => item.totalIncome));
  const lowestIncome = Math.min(...data.map(item => item.totalIncome));

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {t('income.charts.trends', 'Income Trends')} - {t(`income.filters.${period}`, period)}
        </Typography>
        
        <Grid container spacing={3}>
          {/* Income Trend Line Chart */}
          <Grid item xs={12} md={detailed ? 12 : 8}>
            <Box sx={{ height: detailed ? 400 : 300 }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('income.charts.income_over_time', 'Income Over Time')}
              </Typography>
              <Line data={lineData} options={lineOptions} />
            </Box>
          </Grid>

          {/* Transaction Count Bar Chart */}
          {detailed && (
            <Grid item xs={12} md={6}>
              <Box sx={{ height: 300 }}>
                <Typography variant="subtitle2" gutterBottom>
                  {t('income.charts.transaction_trends', 'Transaction Trends')}
                </Typography>
                <Bar data={barData} options={barOptions} />
              </Box>
            </Grid>
          )}

          {/* Summary Statistics */}
          <Grid item xs={12} md={detailed ? 6 : 4}>
            <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1, height: '100%' }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('income.charts.period_summary', 'Period Summary')}
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {t('income.charts.total_income', 'Total Income')}
                </Typography>
                <Typography variant="h6" color="primary">
                  {formatCurrency(totalIncome)}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {t('income.charts.average_income', 'Average Income')}
                </Typography>
                <Typography variant="h6" color="info.main">
                  {formatCurrency(Math.round(averageIncome))}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {t('income.charts.highest_period', 'Highest Period')}
                </Typography>
                <Typography variant="h6" color="success.main">
                  {formatCurrency(highestIncome)}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {t('income.charts.lowest_period', 'Lowest Period')}
                </Typography>
                <Typography variant="h6" color="warning.main">
                  {formatCurrency(lowestIncome)}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 2 }}>
                <Chip 
                  label={`${data.length} ${t('income.charts.periods', 'periods')}`}
                  size="small"
                  color="primary"
                />
                <Chip 
                  label={`${totalTransactions.toLocaleString()} ${t('income.charts.transactions', 'transactions')}`}
                  size="small"
                  color="secondary"
                />
              </Box>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}
