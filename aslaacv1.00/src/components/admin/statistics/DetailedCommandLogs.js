import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  Typography,
  TextField,
  MenuItem,
  Pagination,
  IconButton,
  Tooltip,
  Collapse
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import RefreshIcon from '@mui/icons-material/Refresh';
import { formatDistanceToNow } from 'date-fns';
import axios from '../../../utils/axios';

DetailedCommandLogs.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  filters: PropTypes.object,
  title: PropTypes.string
};

export default function DetailedCommandLogs({ open, onClose, filters = {}, title = "Detailed Command Logs" }) {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });
  
  // Local filters
  const [localFilters, setLocalFilters] = useState({
    commandType: '',
    responseStatus: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    ...filters
  });

  const [expandedRows, setExpandedRows] = useState(new Set());

  // Fetch detailed logs
  const fetchLogs = useCallback(async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: pagination.limit,
        ...localFilters,
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate })
      };

      const response = await axios.get('/api/admin/statistics/detailed-logs', { params });

      if (response.data.success) {
        setLogs(response.data.data.logs);
        setPagination(prev => ({
          ...prev,
          ...response.data.data.pagination
        }));
      }
    } catch (error) {
      console.error('Error fetching detailed logs:', error);
    } finally {
      setLoading(false);
    }
  }, [pagination.limit, localFilters, filters.startDate, filters.endDate]);

  useEffect(() => {
    if (open) {
      fetchLogs();
    }
  }, [open, localFilters, fetchLogs]);

  const handlePageChange = (event, newPage) => {
    fetchLogs(newPage);
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setLocalFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const toggleRowExpansion = (logId) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(logId)) {
      newExpanded.delete(logId);
    } else {
      newExpanded.add(logId);
    }
    setExpandedRows(newExpanded);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'success';
      case 'failed': return 'error';
      case 'timeout': return 'warning';
      case 'device_offline': return 'error';
      case 'invalid_response': return 'warning';
      default: return 'default';
    }
  };

  const getCommandTypeColor = (type) => {
    const colors = {
      power_on: 'success',
      power_off: 'error',
      lock: 'warning',
      unlock: 'info',
      location: 'primary',
      status: 'secondary',
      config: 'default',
      other: 'default'
    };
    return colors[type] || 'default';
  };

  const commandTypeOptions = [
    { value: '', label: 'All Commands' },
    { value: 'power_on', label: 'Power On' },
    { value: 'power_off', label: 'Power Off' },
    { value: 'lock', label: 'Lock' },
    { value: 'unlock', label: 'Unlock' },
    { value: 'location', label: 'Location' },
    { value: 'status', label: 'Status' },
    { value: 'config', label: 'Configuration' },
    { value: 'other', label: 'Other' }
  ];

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'success', label: 'Success' },
    { value: 'failed', label: 'Failed' },
    { value: 'timeout', label: 'Timeout' },
    { value: 'device_offline', label: 'Device Offline' },
    { value: 'invalid_response', label: 'Invalid Response' },
    { value: 'pending', label: 'Pending' }
  ];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xl" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">{title}</Typography>
          <Tooltip title="Refresh">
            <IconButton onClick={() => fetchLogs(pagination.page)} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {/* Filters */}
        <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <TextField
            select
            label="Command Type"
            name="commandType"
            value={localFilters.commandType}
            onChange={handleFilterChange}
            size="small"
            sx={{ minWidth: 150 }}
          >
            {commandTypeOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </TextField>
          
          <TextField
            select
            label="Status"
            name="responseStatus"
            value={localFilters.responseStatus}
            onChange={handleFilterChange}
            size="small"
            sx={{ minWidth: 150 }}
          >
            {statusOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </TextField>
          
          <TextField
            select
            label="Sort By"
            name="sortBy"
            value={localFilters.sortBy}
            onChange={handleFilterChange}
            size="small"
            sx={{ minWidth: 150 }}
          >
            <MenuItem value="createdAt">Date</MenuItem>
            <MenuItem value="responseTime">Response Time</MenuItem>
            <MenuItem value="command">Command</MenuItem>
            <MenuItem value="deviceNumber">Device</MenuItem>
          </TextField>
          
          <TextField
            select
            label="Order"
            name="sortOrder"
            value={localFilters.sortOrder}
            onChange={handleFilterChange}
            size="small"
            sx={{ minWidth: 100 }}
          >
            <MenuItem value="desc">Descending</MenuItem>
            <MenuItem value="asc">Ascending</MenuItem>
          </TextField>
        </Box>

        {/* Logs Table */}
        <TableContainer component={Paper} variant="outlined">
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell width={50}></TableCell>
                <TableCell>Date/Time</TableCell>
                <TableCell>User</TableCell>
                <TableCell>Device</TableCell>
                <TableCell>Command</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Response Time</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {logs.map((log) => (
                <React.Fragment key={log._id}>
                  <TableRow hover>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => toggleRowExpansion(log._id)}
                      >
                        {expandedRows.has(log._id) ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                      </IconButton>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(log.createdAt).toLocaleString()}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatDistanceToNow(new Date(log.createdAt), { addSuffix: true })}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {log.user || 'Unknown'}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {log.deviceNumber}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {log.deviceType}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {log.command}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Chip
                        label={log.commandType || 'other'}
                        size="small"
                        color={getCommandTypeColor(log.commandType)}
                        variant="outlined"
                      />
                    </TableCell>
                    
                    <TableCell>
                      <Chip
                        label={log.responseStatus || 'pending'}
                        size="small"
                        color={getStatusColor(log.responseStatus)}
                        variant="outlined"
                      />
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {log.responseTime ? `${Math.round(log.responseTime)}ms` : '-'}
                      </Typography>
                    </TableCell>
                  </TableRow>
                  
                  {/* Expanded row content */}
                  <TableRow>
                    <TableCell colSpan={8} sx={{ py: 0 }}>
                      <Collapse in={expandedRows.has(log._id)} timeout="auto" unmountOnExit>
                        <Box sx={{ p: 2, bgcolor: 'background.neutral' }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Command Details
                          </Typography>
                          
                          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2 }}>
                            <Box>
                              <Typography variant="caption" color="text.secondary">Sent Time:</Typography>
                              <Typography variant="body2">
                                {log.sentTime ? new Date(log.sentTime).toLocaleString() : '-'}
                              </Typography>
                            </Box>
                            
                            <Box>
                              <Typography variant="caption" color="text.secondary">Receive Time:</Typography>
                              <Typography variant="body2">
                                {log.receiveTime ? new Date(log.receiveTime).toLocaleString() : '-'}
                              </Typography>
                            </Box>
                            
                            <Box>
                              <Typography variant="caption" color="text.secondary">Response Type:</Typography>
                              <Typography variant="body2">{log.responseType || '-'}</Typography>
                            </Box>
                            
                            <Box>
                              <Typography variant="caption" color="text.secondary">Device Online:</Typography>
                              <Chip
                                label={log.deviceOnline ? 'Online' : 'Offline'}
                                size="small"
                                color={log.deviceOnline ? 'success' : 'error'}
                                variant="outlined"
                              />
                            </Box>
                            
                            {log.signalStrength && (
                              <Box>
                                <Typography variant="caption" color="text.secondary">Signal Strength:</Typography>
                                <Typography variant="body2">{log.signalStrength} dBm</Typography>
                              </Box>
                            )}
                            
                            {log.failureReason && (
                              <Box>
                                <Typography variant="caption" color="text.secondary">Failure Reason:</Typography>
                                <Typography variant="body2" color="error.main">{log.failureReason}</Typography>
                              </Box>
                            )}
                          </Box>
                          
                          {log.response && (
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="caption" color="text.secondary">Response:</Typography>
                              <Paper variant="outlined" sx={{ p: 1, mt: 0.5, bgcolor: 'background.paper' }}>
                                <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
                                  {log.response}
                                </Typography>
                              </Paper>
                            </Box>
                          )}
                          
                          {log.message && (
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="caption" color="text.secondary">Message:</Typography>
                              <Typography variant="body2">{log.message}</Typography>
                            </Box>
                          )}
                        </Box>
                      </Collapse>
                    </TableCell>
                  </TableRow>
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
            <Pagination
              count={pagination.pages}
              page={pagination.page}
              onChange={handlePageChange}
              color="primary"
              disabled={loading}
            />
          </Box>
        )}

        {logs.length === 0 && !loading && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" color="text.secondary">
              No command logs found for the selected filters
            </Typography>
          </Box>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
}
