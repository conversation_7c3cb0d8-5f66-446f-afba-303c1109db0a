import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import CommandIcon from '@mui/icons-material/Send';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import { formatDistanceToNow } from 'date-fns';
import axios from '../../../utils/axios';
import DetailedCommandLogs from './DetailedCommandLogs';

UserDetailsDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  userId: PropTypes.string,
  userInfo: PropTypes.object,
  filters: PropTypes.object
};

export default function UserDetailsDialog({ open, onClose, userId, userInfo, filters = {} }) {
  const theme = useTheme();
  const [userStats, setUserStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showCommandLogs, setShowCommandLogs] = useState(false);

  // Fetch user statistics
  const fetchUserStats = useCallback(async () => {
    if (!userId) return;

    setLoading(true);
    try {
      const params = {
        userId,
        ...filters
      };

      const response = await axios.get('/api/admin/statistics/top-users', { params });

      if (response.data.success) {
        // Find the specific user in the response
        const user = response.data.data.find(u => u._id === userId);
        if (user) {
          setUserStats(user);
        }
      }
    } catch (error) {
      console.error('Error fetching user statistics:', error);
    } finally {
      setLoading(false);
    }
  }, [userId, filters]);

  useEffect(() => {
    if (open && userId) {
      fetchUserStats();
    }
  }, [open, userId, filters, fetchUserStats]);

  const getSuccessRateColor = (rate) => {
    if (rate >= 90) return 'success';
    if (rate >= 70) return 'warning';
    return 'error';
  };

  const getResponseTimeColor = (time) => {
    if (time < 1000) return theme.palette.success.main;
    if (time < 3000) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const getActivityLevel = (commandCount) => {
    if (commandCount >= 100) return { label: 'Very Active', color: 'success' };
    if (commandCount >= 50) return { label: 'Active', color: 'info' };
    if (commandCount >= 20) return { label: 'Moderate', color: 'warning' };
    return { label: 'Low Activity', color: 'error' };
  };

  // Use userInfo if userStats is not available
  const displayUser = userStats || userInfo;

  if (!displayUser && !loading) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>User Details</DialogTitle>
        <DialogContent>
          <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
            {userId ? 'No data available for this user' : 'No user selected'}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: theme.palette.primary.main }}>
              <PersonIcon />
            </Avatar>
            <Box>
              <Typography variant="h6">
                {displayUser?.username || 'Unknown User'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {displayUser?.phoneNumber || 'No phone number'}
              </Typography>
            </Box>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {loading ? (
            <Box sx={{ py: 4 }}>
              <LinearProgress />
              <Typography variant="body2" sx={{ textAlign: 'center', mt: 2 }}>
                Loading user statistics...
              </Typography>
            </Box>
          ) : displayUser ? (
            <Grid container spacing={3}>
              {/* Overview Cards */}
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary.main">
                      {displayUser.totalCommands || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Commands
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color={getSuccessRateColor(displayUser.successRate)}>
                      {Math.round(displayUser.successRate || 0)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Success Rate
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography 
                      variant="h4" 
                      sx={{ color: getResponseTimeColor(displayUser.avgResponseTime || 0) }}
                    >
                      {Math.round(displayUser.avgResponseTime || 0)}ms
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Avg Response
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="error.main">
                      {displayUser.failedCommands || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Failed Commands
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* User Information */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      User Information
                    </Typography>
                    
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <PersonIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Username"
                          secondary={displayUser.username || 'Unknown'}
                        />
                      </ListItem>
                      
                      <ListItem>
                        <ListItemIcon>
                          <PhoneIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Phone Number"
                          secondary={displayUser.phoneNumber || 'Not provided'}
                        />
                      </ListItem>
                      
                      <ListItem>
                        <ListItemIcon>
                          <AccessTimeIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Last Activity"
                          secondary={
                            displayUser.lastActivity 
                              ? formatDistanceToNow(new Date(displayUser.lastActivity), { addSuffix: true })
                              : 'Never'
                          }
                        />
                      </ListItem>
                      
                      <ListItem>
                        <ListItemIcon>
                          <TrendingUpIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Activity Level"
                          secondary={
                            <Chip
                              label={getActivityLevel(displayUser.totalCommands || 0).label}
                              size="small"
                              color={getActivityLevel(displayUser.totalCommands || 0).color}
                              variant="outlined"
                            />
                          }
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              {/* Performance Metrics */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Performance Metrics
                    </Typography>
                    
                    <Box sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Success Rate</Typography>
                        <Typography variant="body2">{Math.round(displayUser.successRate || 0)}%</Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={displayUser.successRate || 0}
                        color={getSuccessRateColor(displayUser.successRate)}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                    
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Command Breakdown
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Successful:</Typography>
                        <Typography variant="body2" color="success.main">
                          {displayUser.successfulCommands || 0}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Failed:</Typography>
                        <Typography variant="body2" color="error.main">
                          {displayUser.failedCommands || 0}
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Quick Stats */}
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Quick Statistics
                    </Typography>
                    
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={4}>
                        <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
                          <CommandIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                          <Typography variant="h5" color="primary.main">
                            {displayUser.totalCommands || 0}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Total Commands Sent
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={4}>
                        <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
                          <AccessTimeIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
                          <Typography variant="h5" color="info.main">
                            {Math.round(displayUser.avgResponseTime || 0)}ms
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Average Response Time
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={4}>
                        <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
                          <TrendingUpIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                          <Typography variant="h5" color="success.main">
                            {Math.round(displayUser.successRate || 0)}%
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Success Rate
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          ) : null}
        </DialogContent>
        
        <DialogActions>
          <Button 
            onClick={() => setShowCommandLogs(true)}
            variant="outlined"
            disabled={!displayUser}
          >
            View Command History
          </Button>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Command Logs Dialog */}
      <DetailedCommandLogs
        open={showCommandLogs}
        onClose={() => setShowCommandLogs(false)}
        filters={{ ...filters, userId }}
        title={`Command History - ${displayUser?.username || 'User'}`}
      />
    </>
  );
}
