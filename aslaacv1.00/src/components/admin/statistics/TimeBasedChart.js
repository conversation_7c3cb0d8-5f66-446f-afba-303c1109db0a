import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardContent, Typography, Box, Grid } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

TimeBasedChart.propTypes = {
  data: PropTypes.array
};

export default function TimeBasedChart({ data = [] }) {
  const theme = useTheme();

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Time-based Analytics
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography variant="body2" color="text.secondary">
              No time-based data available
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  // Format labels based on the time period
  const formatLabel = (timeData) => {
    if (timeData.hour !== undefined) {
      return `${timeData.month}/${timeData.day} ${timeData.hour}:00`;
    } else if (timeData.day !== undefined) {
      return `${timeData.month}/${timeData.day}`;
    } else if (timeData.week !== undefined) {
      return `Week ${timeData.week}, ${timeData.year}`;
    } else if (timeData.month !== undefined) {
      return `${timeData.month}/${timeData.year}`;
    }
    return 'Unknown';
  };

  const labels = data.map(item => formatLabel(item._id));

  // Command volume over time
  const commandVolumeData = {
    labels,
    datasets: [
      {
        label: 'Total Commands',
        data: data.map(item => item.totalCommands || 0),
        borderColor: theme.palette.primary.main,
        backgroundColor: theme.palette.primary.light,
        tension: 0.1,
      },
      {
        label: 'Successful Commands',
        data: data.map(item => item.successfulCommands || 0),
        borderColor: theme.palette.success.main,
        backgroundColor: theme.palette.success.light,
        tension: 0.1,
      },
      {
        label: 'Failed Commands',
        data: data.map(item => item.failedCommands || 0),
        borderColor: theme.palette.error.main,
        backgroundColor: theme.palette.error.light,
        tension: 0.1,
      },
    ],
  };

  // Success rate over time
  const successRateData = {
    labels,
    datasets: [
      {
        label: 'Success Rate (%)',
        data: data.map(item => Math.round(item.successRate || 0)),
        borderColor: theme.palette.info.main,
        backgroundColor: theme.palette.info.light,
        tension: 0.1,
        yAxisID: 'y',
      },
      {
        label: 'Avg Response Time (ms)',
        data: data.map(item => Math.round(item.avgResponseTime || 0)),
        borderColor: theme.palette.warning.main,
        backgroundColor: theme.palette.warning.light,
        tension: 0.1,
        yAxisID: 'y1',
      },
    ],
  };

  // User and device activity
  const activityData = {
    labels,
    datasets: [
      {
        label: 'Active Users',
        data: data.map(item => item.uniqueUserCount || 0),
        backgroundColor: theme.palette.secondary.main,
        borderColor: theme.palette.secondary.dark,
        borderWidth: 1,
      },
      {
        label: 'Active Devices',
        data: data.map(item => item.uniqueDeviceCount || 0),
        backgroundColor: theme.palette.info.main,
        borderColor: theme.palette.info.dark,
        borderWidth: 1,
      },
    ],
  };

  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time Period'
        }
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Commands'
        }
      },
    },
  };

  const dualAxisOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time Period'
        }
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Success Rate (%)'
        },
        max: 100,
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'Response Time (ms)'
        },
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time Period'
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Count'
        }
      },
    },
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Time-based Analytics
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Box sx={{ height: 400 }}>
              <Typography variant="subtitle2" gutterBottom>
                Command Volume Over Time
              </Typography>
              <Line data={commandVolumeData} options={lineChartOptions} />
            </Box>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Box sx={{ height: 350 }}>
              <Typography variant="subtitle2" gutterBottom>
                Success Rate & Response Time
              </Typography>
              <Line data={successRateData} options={dualAxisOptions} />
            </Box>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Box sx={{ height: 350 }}>
              <Typography variant="subtitle2" gutterBottom>
                User & Device Activity
              </Typography>
              <Bar data={activityData} options={barChartOptions} />
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}
