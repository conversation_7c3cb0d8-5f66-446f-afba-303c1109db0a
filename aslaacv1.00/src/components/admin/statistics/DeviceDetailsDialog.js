import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import DevicesIcon from '@mui/icons-material/Devices';
import SignalCellularAltIcon from '@mui/icons-material/SignalCellularAlt';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { formatDistanceToNow } from 'date-fns';
import axios from '../../../utils/axios';
import DetailedCommandLogs from './DetailedCommandLogs';

DeviceDetailsDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  deviceNumber: PropTypes.string,
  filters: PropTypes.object
};

export default function DeviceDetailsDialog({ open, onClose, deviceNumber, filters = {} }) {
  const theme = useTheme();
  const [deviceStats, setDeviceStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showCommandLogs, setShowCommandLogs] = useState(false);

  // Fetch device statistics
  const fetchDeviceStats = useCallback(async () => {
    if (!deviceNumber) return;

    setLoading(true);
    try {
      const params = {
        deviceNumber,
        ...filters
      };

      const response = await axios.get('/api/admin/statistics/device-analytics', { params });

      if (response.data.success && response.data.data.devices.length > 0) {
        setDeviceStats(response.data.data.devices[0]);
      }
    } catch (error) {
      console.error('Error fetching device statistics:', error);
    } finally {
      setLoading(false);
    }
  }, [deviceNumber, filters]);

  useEffect(() => {
    if (open && deviceNumber) {
      fetchDeviceStats();
    }
  }, [open, deviceNumber, filters, fetchDeviceStats]);

  const getPerformanceColor = (successRate) => {
    if (successRate >= 95) return 'success';
    if (successRate >= 85) return 'info';
    if (successRate >= 70) return 'warning';
    return 'error';
  };

  const getResponseTimeColor = (time) => {
    if (time < 1000) return theme.palette.success.main;
    if (time < 3000) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const getSignalQuality = (strength) => {
    if (strength > -70) return { label: 'Excellent', color: 'success' };
    if (strength > -85) return { label: 'Good', color: 'info' };
    if (strength > -100) return { label: 'Fair', color: 'warning' };
    return { label: 'Poor', color: 'error' };
  };

  if (!deviceStats && !loading) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>Device Details</DialogTitle>
        <DialogContent>
          <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
            {deviceNumber ? 'No data available for this device' : 'No device selected'}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DevicesIcon />
            <Typography variant="h6">
              Device Details: {deviceNumber}
            </Typography>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {loading ? (
            <Box sx={{ py: 4 }}>
              <LinearProgress />
              <Typography variant="body2" sx={{ textAlign: 'center', mt: 2 }}>
                Loading device statistics...
              </Typography>
            </Box>
          ) : deviceStats ? (
            <Grid container spacing={3}>
              {/* Overview Cards */}
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary.main">
                      {deviceStats.totalCommands || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Commands
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color={getPerformanceColor(deviceStats.successRate)}>
                      {Math.round(deviceStats.successRate || 0)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Success Rate
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography 
                      variant="h4" 
                      sx={{ color: getResponseTimeColor(deviceStats.avgResponseTime || 0) }}
                    >
                      {Math.round(deviceStats.avgResponseTime || 0)}ms
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Avg Response
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="error.main">
                      {deviceStats.failedCommands || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Failed Commands
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              {/* Device Information */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Device Information
                    </Typography>
                    
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <DevicesIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Device Type"
                          secondary={deviceStats.deviceType || '4g'}
                        />
                      </ListItem>
                      
                      <ListItem>
                        <ListItemIcon>
                          <AccessTimeIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Last Activity"
                          secondary={
                            deviceStats.lastActivity 
                              ? formatDistanceToNow(new Date(deviceStats.lastActivity), { addSuffix: true })
                              : 'Never'
                          }
                        />
                      </ListItem>
                      
                      {deviceStats.avgSignalStrength && (
                        <ListItem>
                          <ListItemIcon>
                            <SignalCellularAltIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Signal Strength"
                            secondary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography variant="body2">
                                  {deviceStats.avgSignalStrength} dBm
                                </Typography>
                                <Chip
                                  label={getSignalQuality(deviceStats.avgSignalStrength).label}
                                  size="small"
                                  color={getSignalQuality(deviceStats.avgSignalStrength).color}
                                  variant="outlined"
                                />
                              </Box>
                            }
                          />
                        </ListItem>
                      )}
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              {/* Performance Metrics */}
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Performance Metrics
                    </Typography>
                    
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Success Rate</Typography>
                        <Typography variant="body2">{Math.round(deviceStats.successRate || 0)}%</Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={deviceStats.successRate || 0}
                        color={getPerformanceColor(deviceStats.successRate)}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                    
                    <Divider sx={{ my: 2 }} />
                    
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="body2" color="text.secondary">
                            Min Response
                          </Typography>
                          <Typography variant="h6">
                            {Math.round(deviceStats.minResponseTime || 0)}ms
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={6}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="body2" color="text.secondary">
                            Max Response
                          </Typography>
                          <Typography variant="h6">
                            {Math.round(deviceStats.maxResponseTime || 0)}ms
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>

              {/* Command Breakdown */}
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Command Breakdown
                    </Typography>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <CheckCircleIcon color="success" />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Successful
                            </Typography>
                            <Typography variant="h6">
                              {deviceStats.successfulCommands || 0}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <ErrorIcon color="error" />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Failed
                            </Typography>
                            <Typography variant="h6">
                              {deviceStats.failedCommands || 0}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <AccessTimeIcon color="warning" />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Timeouts
                            </Typography>
                            <Typography variant="h6">
                              {deviceStats.timeoutCount || 0}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <DevicesIcon color="error" />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Offline
                            </Typography>
                            <Typography variant="h6">
                              {deviceStats.offlineCount || 0}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          ) : null}
        </DialogContent>
        
        <DialogActions>
          <Button 
            onClick={() => setShowCommandLogs(true)}
            variant="outlined"
            disabled={!deviceStats}
          >
            View Command Logs
          </Button>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Command Logs Dialog */}
      <DetailedCommandLogs
        open={showCommandLogs}
        onClose={() => setShowCommandLogs(false)}
        filters={{ ...filters, deviceNumber }}
        title={`Command Logs - ${deviceNumber}`}
      />
    </>
  );
}
