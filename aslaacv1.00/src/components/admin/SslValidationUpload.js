import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  LinearProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { Delete as DeleteIcon, FileCopy as FileCopyIcon } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import axios from '../../utils/axios';
import { HOST_API } from '../../config/apiConfig';

export default function SslValidationUpload() {
  const [file, setFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [files, setFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState({ open: false, filename: null });
  const { enqueueSnackbar } = useSnackbar();

  const loadFiles = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await axios.get('/api/admin/ssl/list-validation-files');
      if (response.data.success) {
        setFiles(response.data.files);
      }
    } catch (error) {
      console.error('Error loading SSL files:', error);
      enqueueSnackbar('Failed to load SSL validation files', { variant: 'error' });
    } finally {
      setIsLoading(false);
    }
  }, [enqueueSnackbar]);

  useEffect(() => {
    loadFiles();
  }, [loadFiles]);

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile && selectedFile.name.toLowerCase().endsWith('.txt')) {
      setFile(selectedFile);
      setUploadStatus(null);
      setUploadProgress(0);
    } else {
      setFile(null);
      setUploadStatus({
        success: false,
        message: 'Please select a valid SSL validation file (.txt)'
      });
    }
  };

  const handleUpload = async () => {
    if (!file) {
      setUploadStatus({
        success: false,
        message: 'Please select an SSL validation file first'
      });
      return;
    }

    setIsUploading(true);
    setUploadStatus(null);
    setUploadProgress(0);

    const formData = new FormData();
    formData.append('sslFile', file);

    try {
      const response = await axios.post('/api/admin/ssl/upload-validation-file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      if (response.data.success) {
        setUploadStatus({
          success: true,
          message: 'SSL validation file uploaded successfully',
          file: response.data.file
        });
        enqueueSnackbar('SSL validation file uploaded successfully', { variant: 'success' });
        setFile(null);
        loadFiles(); // Refresh the file list
      } else {
        throw new Error(response.data.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setUploadStatus({
        success: false,
        message: error.response?.data?.message || error.message || 'Upload failed'
      });
      enqueueSnackbar('Failed to upload SSL validation file', { variant: 'error' });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDelete = async (filename) => {
    try {
      const response = await axios.delete(`/api/admin/ssl/delete-validation-file/${filename}`);
      if (response.data.success) {
        enqueueSnackbar('SSL validation file deleted successfully', { variant: 'success' });
        loadFiles(); // Refresh the file list
      } else {
        throw new Error(response.data.message || 'Delete failed');
      }
    } catch (error) {
      console.error('Delete error:', error);
      enqueueSnackbar('Failed to delete SSL validation file', { variant: 'error' });
    }
    setDeleteDialog({ open: false, filename: null });
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    enqueueSnackbar('URL copied to clipboard', { variant: 'success' });
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Box>
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Upload SSL Certificate Validation File
          </Typography>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Upload SSL certificate validation files for domain verification. These files will be accessible at /.well-known/pki-validation/
          </Typography>
          
          <Box sx={{ mt: 2, mb: 2 }}>
            <input
              accept=".txt"
              style={{ display: 'none' }}
              id="ssl-file-upload"
              type="file"
              onChange={handleFileChange}
              disabled={isUploading}
            />
            <label htmlFor="ssl-file-upload">
              <Button
                variant="outlined"
                component="span"
                disabled={isUploading}
              >
                Select SSL Validation File (.txt)
              </Button>
            </label>
            
            {file && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                Selected file: {file.name}
              </Typography>
            )}
          </Box>

          {isUploading && (
            <Box sx={{ width: '100%', mb: 2 }}>
              <LinearProgress variant="determinate" value={uploadProgress} />
              <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
                {uploadProgress}% Uploaded
              </Typography>
            </Box>
          )}

          <Button
            variant="contained"
            onClick={handleUpload}
            disabled={!file || isUploading}
            sx={{ mr: 2 }}
          >
            {isUploading ? 'Uploading...' : 'Upload File'}
          </Button>

          {uploadStatus && (
            <Alert 
              severity={uploadStatus.success ? 'success' : 'error'} 
              sx={{ mt: 2 }}
            >
              {uploadStatus.message}
              {uploadStatus.success && uploadStatus.file && (
                <Box sx={{ mt: 1 }}>
                  <Typography variant="body2">
                    Access URL: {HOST_API.replace(/\/$/, '')}{uploadStatus.file.accessUrl}
                  </Typography>
                </Box>
              )}
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Uploaded SSL Validation Files
          </Typography>
          
          {isLoading ? (
            <LinearProgress />
          ) : (
            <TableContainer component={Paper} sx={{ mt: 2 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Filename</TableCell>
                    <TableCell>Size</TableCell>
                    <TableCell>Uploaded</TableCell>
                    <TableCell>Access URL</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {files.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} align="center">
                        No SSL validation files uploaded
                      </TableCell>
                    </TableRow>
                  ) : (
                    files.map((file) => (
                      <TableRow key={file.filename}>
                        <TableCell>{file.filename}</TableCell>
                        <TableCell>{formatFileSize(file.size)}</TableCell>
                        <TableCell>{formatDate(file.uploadedAt)}</TableCell>
                        <TableCell>
                          <Chip
                            label={`${HOST_API.replace(/\/$/, '')}${file.accessUrl}`}
                            size="small"
                            onClick={() => copyToClipboard(`${HOST_API.replace(/\/$/, '')}${file.accessUrl}`)}
                            icon={<FileCopyIcon />}
                            clickable
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton
                            color="error"
                            onClick={() => setDeleteDialog({ open: true, filename: file.filename })}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, filename: null })}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the SSL validation file "{deleteDialog.filename}"?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, filename: null })}>
            Cancel
          </Button>
          <Button 
            onClick={() => handleDelete(deleteDialog.filename)} 
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
