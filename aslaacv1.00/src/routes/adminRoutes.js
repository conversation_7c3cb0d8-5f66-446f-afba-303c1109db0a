import { lazy } from 'react';
import AuthGuard from '../guards/AuthGuard';
import AdminGuard from '../guards/AdminGuard';

const DeviceEdit = lazy(() => import('../pages/admin/DeviceEdit'));

const Orders = lazy(() => import('../pages/admin/OrderList'));
const AppManagement = lazy(() => import('../pages/admin/AppManagement'));
const StatisticsDashboard = lazy(() => import('../pages/admin/StatisticsDashboard'));
const IncomeDashboard = lazy(() => import('../pages/admin/IncomeDashboard'));
const IoTDeviceManagement = lazy(() => import('../pages/admin/IoTDeviceManagement'));

const adminRoutes = {
  path: 'admin',
  children: [
    { path: 'device/:id', element: <AuthGuard><AdminGuard><DeviceEdit /></AdminGuard></AuthGuard> },
    { path: 'orders', element: <AuthGuard><AdminGuard><Orders /></AdminGuard></AuthGuard> },

    { path: 'app-management', element: <AuthGuard><AdminGuard><AppManagement /></AdminGuard></AuthGuard> },
    { path: 'statistics', element: <AuthGuard><AdminGuard><StatisticsDashboard /></AdminGuard></AuthGuard> },
    { path: 'income', element: <AuthGuard><AdminGuard><IncomeDashboard /></AdminGuard></AuthGuard> },
    { path: 'iot-device-management', element: <AuthGuard><AdminGuard><IoTDeviceManagement /></AdminGuard></AuthGuard> },
  ],
};

export default adminRoutes;
