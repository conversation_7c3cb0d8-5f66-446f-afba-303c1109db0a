import 'package:flutter/foundation.dart';
import './device.dart';
import './two_factor_auth.dart';

class User {
  final String id;
  String password;
  final String phoneNumber;
  String username;
  final String role;
  final String token;
  String licenseKey;
  String expired;
  num remainDays;
  String driverLicenseFile;
  final int driverLicenseVerification;
  final String address;
  final String description;
  final num balance;
  final String status;

  Device? device;
  List<Device>? devices;

  // 2FA fields
  final bool twoFactorEnabled;
  final DateTime? twoFactorEnabledAt;
  final List<BackupCode>? backupCodes;
  final String? lastTotpUsed;

  User({
    required this.phoneNumber,
    required this.id,
    required this.password,
    required this.username,
    required this.role,
    required this.address,
    required this.balance,
    required this.description,
    required this.driverLicenseFile,
    required this.driverLicenseVerification,
    required this.expired,
    required this.licenseKey,
    required this.remainDays,
    required this.status,
    required this.token,
    required this.device,
    required this.devices,
    // 2FA fields
    this.twoFactorEnabled = false,
    this.twoFactorEnabledAt,
    this.backupCodes,
    this.lastTotpUsed,
  });

  factory User.fromGuest() {
    return User(
      phoneNumber: '**********',
      id: '**********',
      username: 'guest',
      password: '1234',
      role: 'guest',
      address: 'address',
      balance: 123450,
      description: 'ID of citizen',
      expired: '${DateTime.now().add(Duration(days: 30))}',
      remainDays: 30,
      status: 'Trial',
      driverLicenseFile: '',
      driverLicenseVerification: 0,
      licenseKey: '',
      token: '',

      devices: [Device.fromVirtual()].toList(),
      device: Device.fromVirtual(),
      // 2FA fields for guest user
      twoFactorEnabled: false,
      twoFactorEnabledAt: null,
      backupCodes: null,
      lastTotpUsed: null,
    );
  }

  factory User.fromJson(Map<String, dynamic> json, String? _token) {
    final Map<String, dynamic> user = (json['user'] ?? {});

    final List? devices = (user['devices']);

    debugPrint('${Device.fromMap(user['device'])} is devuce');
    debugPrint('${Device.fromVirtual()} is devices');

    return User(
      phoneNumber: user['phoneNumber'] ?? '',
      id: user['_id'] ?? '',
      username: user['username'] ?? '',
      password: user['pinCode'] ?? '',
      role: user['role'] ?? '',
      address: user['address'] ?? '',
      balance: user['balance'] ?? 0,
      description: user['description'] ?? '',
      expired: user['expired'] ?? '',
      remainDays: user['remainDays'] ?? 0,
      status: user['status'] ?? '',
      driverLicenseFile: user['driverLicenseFile'] ?? '',
      driverLicenseVerification: user['driverLicenseVerification'] ?? 0,
      licenseKey: user['licenseKey'] ?? '',
      token: _token == null ? (json['token'] ?? '') : _token,

      device: user['device'] != null
          ? Device.fromMap(user['device'])
          : Device.fromVirtual(),
      devices: devices == null
          ? [Device.fromVirtual()]
          : devices.map((e) => Device.fromMap(e)).toList(),
      // 2FA fields
      twoFactorEnabled: user['twoFactorEnabled'] ?? false,
      twoFactorEnabledAt: user['twoFactorEnabledAt'] != null
          ? DateTime.parse(user['twoFactorEnabledAt'])
          : null,
      backupCodes: user['backupCodes'] != null
          ? (user['backupCodes'] as List)
              .map((bc) => BackupCode.fromJson(bc))
              .toList()
          : null,
      lastTotpUsed: user['lastTotpUsed'],
    );
  }

  @override
  String toString() {
    debugPrint('$devices');
    return '{id:$id, name:$username}';
  }

  // Add the toJson method
  Map<String, dynamic> toJson() => toMap();

  Map<String, dynamic> toMap() {
    return {
      "id": id,
      "password": password,
      "phoneNumber": phoneNumber,
      "username": username,
      "role": role,
      "token": token,
      "licenseKey": licenseKey,
      "expired": expired,
      "remainDays": remainDays,
      "driverLicenseFile": driverLicenseFile,
      "driverLicenseVerification": driverLicenseVerification,
      "address": address,
      "description": description,
      "balance": balance,
      "status": status,
      "device": device?.toMap(),
      "devices": devices?.map((device) => device.toMap()).toList(),
      // 2FA fields
      "twoFactorEnabled": twoFactorEnabled,
      "twoFactorEnabledAt": twoFactorEnabledAt?.toIso8601String(),
      "backupCodes": backupCodes?.map((bc) => bc.toJson()).toList(),
      "lastTotpUsed": lastTotpUsed,
    };
  }
}

class UserNotifier extends ChangeNotifier {
  late User? _user;

  User? get client => _user;

  set client(User? newUser) {
    _user = newUser;
    notifyListeners();
  }

  void logout() {
    this._user = null;
    notifyListeners();
  }
}
